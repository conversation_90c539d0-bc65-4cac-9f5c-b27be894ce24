#!/usr/bin/env python3
"""
HKU Complete Quiz Export - 显示每个问题的详细记录
类似于之前的1600+行Excel，包含所有quiz questions
"""

import psycopg2
import csv
import json
from datetime import datetime

def export_complete_quiz_data():
    """导出完整的quiz数据，每个问题一行"""
    
    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }
    
    try:
        print("🔄 导出完整quiz数据（每个问题一行）...")
        
        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 查询所有quiz questions的详细信息
        query = """
        SELECT 
            uq.date as answer_date,
            fg.family_name,
            au.username as member_name,
            c.name as category_name,
            q.category as question_category,
            q.quiz_type,
            q.question,
            q.answer as correct_answer,
            uq.selected_answer as given_answer_letter,
            q.option as question_options,
            uq.is_correct,
            CASE WHEN uq.is_correct THEN q.score ELSE 0 END as question_score,
            q.score as max_question_score,
            uq.user_id,
            fg.id as family_group_id,
            COALESCE(sc.steps, 0) as step_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        JOIN family_category c ON fg.category_id = c.id
        LEFT JOIN question_stepscounter sc ON uq.user_id = sc.user_id AND uq.date = sc.created_on::date
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        ORDER BY uq.date, fg.family_name, au.username, q.quiz_type, q.id
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"📊 找到 {len(results)} 条quiz question记录")
        
        # 统计信息
        families = set()
        users = set()
        categories = set()
        quiz_types = set()
        
        for row in results:
            families.add(row[1])  # family_name
            users.add(row[2])     # member_name
            categories.add(row[3]) # category_name
            quiz_types.add(row[5]) # quiz_type
        
        print(f"📋 统计信息:")
        print(f"   家庭组数: {len(families)}")
        print(f"   用户数: {len(users)}")
        print(f"   类别数: {len(categories)} ({', '.join(sorted(categories))})")
        print(f"   Quiz类型: {len(quiz_types)} ({', '.join(sorted(quiz_types))})")
        
        # 生成CSV文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"hku_complete_quiz_export_{timestamp}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入表头
            headers = [
                'Answer Date', 'Family Name', 'Member Name', 'Family Category',
                'Question Category', 'Quiz Type', 'Question', 'Correct Answer', 
                'Given Answer', 'All Answer Options', 'Is Correct', 
                'Question Score', 'Max Question Score', 'User ID', 
                'Family Group ID', 'Step Count'
            ]
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            
            # 处理每一行数据
            for row in results:
                # 处理答案选项
                given_answer_letter = row[8]  # 用户选择的字母
                question_options = row[9]     # JSON格式的选项
                quiz_type = row[5]            # Quiz类型
                
                # 对于Bonus questions，解析选项并获取完整答案文本
                if quiz_type == 'Bonus' and question_options:
                    try:
                        options_dict = json.loads(question_options) if isinstance(question_options, str) else question_options
                        # 获取用户选择的完整答案
                        given_answer_full = options_dict.get(given_answer_letter, given_answer_letter) if given_answer_letter else ''
                        # 格式化所有选项
                        all_options = '; '.join([f"{k}: {v}" for k, v in options_dict.items()])
                    except:
                        given_answer_full = given_answer_letter if given_answer_letter else ''
                        all_options = str(question_options) if question_options else ''
                else:
                    given_answer_full = given_answer_letter if given_answer_letter else ''
                    all_options = ''
                
                # 构建CSV行数据
                csv_row = [
                    row[0],   # Answer Date
                    row[1],   # Family Name
                    row[2],   # Member Name
                    row[3],   # Family Category
                    row[4],   # Question Category
                    row[5],   # Quiz Type
                    row[6],   # Question
                    row[7] if quiz_type != 'Bonus' else '',  # Correct Answer (空白对于Bonus questions)
                    given_answer_full,  # Given Answer (完整文本)
                    all_options,  # All Answer Options (所有选项)
                    row[10],  # Is Correct
                    row[11],  # Question Score
                    row[12],  # Max Question Score
                    row[13],  # User ID
                    row[14],  # Family Group ID
                    row[15],  # Step Count
                ]
                
                writer.writerow(csv_row)
        
        print(f"✅ 完整quiz导出完成: {filename}")
        print(f"📊 总记录数: {len(results)}")
        
        # 按类别分组统计
        print(f"\n📋 按家庭类别分组:")
        category_stats = {}
        for row in results:
            category = row[3]  # category_name
            if category not in category_stats:
                category_stats[category] = {'families': set(), 'records': 0}
            category_stats[category]['families'].add(row[1])  # family_name
            category_stats[category]['records'] += 1
        
        for category, stats in sorted(category_stats.items()):
            print(f"   {category}: {len(stats['families'])}个家庭组, {stats['records']}条记录")
        
        cursor.close()
        conn.close()
        
        return filename
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    export_complete_quiz_data()
