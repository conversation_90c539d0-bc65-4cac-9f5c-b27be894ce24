{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Family Members' %}{% endblock %}
{% block content %}
{% load static %}

{% if msg %}
<div id="idFamilyMemberMsg" class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
    <span class="font-medium">{{ msg }}</span>
    <span onclick="closeMsg('idFamilyMemberMsg')">
        <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </span>
</div>
{% endif %}
 
<div class="p-4">
    <div class="mb-3 flex">
        <h1 class="text-2xl font-bold mb-4">{% trans 'Family Members' %} </h1> <span class="text-2xl text-gray-500">&nbsp- {{ family_group_name }} ( {{ family_group_category }} )</span>
    </div>

    <div class="relative w-full shadow-lg rounded-lg overflow-x-auto sm:rounded-lg">
        <div class="table-container">
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                    <tr>
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Created On' %}
                            </div>
                        </th>
    
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'First Name' %}
                            </div>
                        </th>
    
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Last Name' %}
                            </div>
                        </th> 
       
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Is Leader' %}
                            </div>
                        </th>
    
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'View' %}
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% if family_member_details %}
                    {% for family_member_detail in family_member_details %}
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
    
                        <td class="px-6 py-4">
                            {{ family_member_detail.created_on }}
                        </td>
    
                        {% comment %} <td class="px-6 py-4">
                            {{ family_member_detail.family_group }}
                        </td> {% endcomment %}
    
                        <td class="px-6 py-4">
                            {{ family_member_detail.first_name }}
                        </td>
    
                        <td class="px-6 py-4">
                            {{ family_member_detail.last_name }}
                        </td>
    
                        <td class="px-6 py-4">
                            <div {% if family_member_detail.is_leader == 'True' %}  class="w-fit pl-2 pr-2 bg-green-100 text-green-900 rounded-md" {% else %} class="w-fit pl-2 pr-2 bg-red-100 text-red-900 rounded-md" {% endif %}>
                                {{ family_member_detail.is_leader }}
                            </div>
                        </td>
    
                        <td class="px-6 py-4">
                            <div class="flex">
                                <a href="/member_wise_que_ans/{{ family_member_detail.member_id }}" title="View" >
                                    <svg class="w-6 h-6 text-blue-500 hover:text-blue-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                                        <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                        <path stroke="none" d="M0 0h24v24H0z"/>
                                        <line x1="4" y1="7" x2="20" y2="7" />
                                        <line x1="10" y1="11" x2="10" y2="17" />
                                        <line x1="14" y1="11" x2="14" y2="17" />
                                        <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12" />
                                        <path d="M9 7v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3" />
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                    {% else %}
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="5" class="px-6 py-4 text-base">Data is not available.</td></tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
<script>
    $(document).ready(function() { 
        msg = '{{ msg }}';
        if (msg) {
            setTimeout(() => {
                $('#idFamilyMemberMsg').hide();
            }, 10000);
        }
    })
</script>
{% endblock content %}
