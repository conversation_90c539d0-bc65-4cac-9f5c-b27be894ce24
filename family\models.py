from django.db import models
from django.contrib.auth.models import User
import os
from django.conf import settings
# Create your models here.


def get_family_grp_file_path(instance, filename):
    # Upload path: user/<user_id>/image.jpg
    return f"media/family_group/{instance.family_group.id}/{filename}"

def get_family_members_step_file_path(instance, filename):
    # Upload path: user/<user_id>/image.jpg
    return f"media/family_members_step/{instance.member.id}/{filename}"

class Category(models.Model):   
    name = models.CharField(max_length=255)
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return self.name

class FamilyGroups(models.Model):
    family_id = models.CharField(max_length=255)  
    leader_id = models.ForeignKey(User, on_delete=models.PROTECT, related_name='leader_of_families')
    family_name = models.CharField(max_length=255)
    category = models.ForeignKey(Category, on_delete=models.PROTECT)
    is_quiz_starts = models.BooleanField(default=False)
    icon_url = models.URLField(max_length=255, null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.family_name

class FamilyGroupFile(models.Model):
    family_group = models.ForeignKey(FamilyGroups, on_delete=models.PROTECT)
    file = models.FileField(upload_to='family_group/image', blank=True, null=True)
    file_name = models.CharField(max_length=100, blank=True, null=True)


class WeeklyUploadFile(models.Model):
    family_group = models.ForeignKey(FamilyGroups, on_delete=models.PROTECT, null=True)
    file = models.FileField(upload_to='weekly_uploaded_file/image', blank=True, null=True)
    file_name = models.CharField(max_length=100, blank=True, null=True)
    score = models.IntegerField(default=0)
    created_on = models.DateTimeField(auto_now_add=True)
    
class FamilyMembers(models.Model):
    family_group = models.ForeignKey(FamilyGroups, on_delete=models.CASCADE, related_name='family_members')
    member = models.ForeignKey(User, on_delete=models.PROTECT)
    is_member = models.BooleanField(default=False, help_text="When user accept invitation or join using referral code then it will be True")
    created_on = models.DateTimeField(auto_now_add=True)
    is_deleted = models.BooleanField(default=False)
    is_leader = models.BooleanField(default=False)

class FamilyMembersSteps(models.Model):
    member = models.ForeignKey(FamilyMembers, on_delete=models.PROTECT)
    steps = models.IntegerField()
    file = models.FileField(upload_to='family_members_step/image', null=True, blank=True)
    file_name = models.CharField(max_length=255, null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)


class CategoryChangeReq(models.Model):
    family = models.ForeignKey(FamilyGroups, on_delete=models.PROTECT)
    reason = models.CharField(max_length=255,null=True,blank=True)
    created_on = models.DateTimeField(auto_now_add=True)