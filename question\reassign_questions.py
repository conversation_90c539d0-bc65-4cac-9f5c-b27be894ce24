import logging
from django.utils.timezone import now, timedelta
from .models import Question, UserQuestion, QuizDates
from django.contrib.auth.models import User
from django.db.models import Q
from django.db import transaction
from family.models import FamilyMembers
from decouple import config

logger = logging.getLogger('django')

def chunked_queryset(queryset, chunk_size=100):
    queryset = queryset.order_by('id')
    start = 0
    end = chunk_size
    while start < queryset.count():
        yield queryset[start:end]
        start = end
        end += chunk_size

def get_food_question_list(question_list):
    regular_questions_sports = list(question_list.filter(quiz_type="Regular", category = 'Food'))
    #bonus_questions_sports = list(question_list.filter(quiz_type="Bonus", category = 'Food'))
    logger.info(f"Regular questions for Food: {len(regular_questions_sports)}")
    return regular_questions_sports

def get_sports_question_list(question_list):
    regular_questions_food = list(question_list.filter(quiz_type="Regular", category = 'Sports'))
    #bonus_questions_food = list(question_list.filter(quiz_type="Bonus", category = 'Sports'))
    logger.info(f"Regular questions for Sports: {len(regular_questions_food)}")
    return regular_questions_food


def delete_old_questions():
    old_user_question = UserQuestion.objects.all()
    for q in old_user_question:
        q.is_deleted = True
    logger.info(f"Deleting old question....")
    UserQuestion.objects.bulk_update(old_user_question, ["is_deleted"])


def get_question_to_assign(regular_question, bonus_questions, next_date, walk_questions=None):
    #print(f"************* {len(walk_questions)} **************")
    if walk_questions is not None:
        logger.info(f"Getting question to assign....")
        if next_date.weekday() == int(config("WEEK_DAY")):  # If Sunday
            # if len(bonus_questions) < 3:
            #     logger.warning("There are not enough bonus questions available to assign.")
            #     return True, "There are not enough bonus questions available to assign."
            walk_questions_to_assign  = walk_questions[:1]
            regular_questions_to_assign  = regular_question[:4]
            bonus_questions_to_assign = bonus_questions[:3]
            questions_to_assign = regular_questions_to_assign + bonus_questions_to_assign + walk_questions_to_assign
            regular_question = regular_question[4:]
            bonus_questions = bonus_questions[3:]
            walk_questions = walk_questions[1:]
        else:
            walk_questions_to_assign  = walk_questions[:1]
            regular_questions_to_assign  = regular_question[:4]
            questions_to_assign = regular_questions_to_assign + walk_questions_to_assign
            regular_question = regular_question[4:]
            walk_questions = walk_questions[1:]
        return regular_question, bonus_questions, questions_to_assign, walk_questions
    else:
        logger.info(f"Getting question to assign....")
        if next_date.weekday() == int(config("WEEK_DAY")):  # If Sunday
            # if len(bonus_questions) < 3:
            #     logger.warning("There are not enough bonus questions available to assign.")
            #     return True, "There are not enough bonus questions available to assign."
            regular_questions_to_assign  = regular_question[:5]
            bonus_questions_to_assign = bonus_questions[:3]
            questions_to_assign = regular_questions_to_assign + bonus_questions_to_assign
            regular_question = regular_question[5:]
            bonus_questions = bonus_questions[3:]
        else:
            regular_questions_to_assign  = regular_question[:5]
            questions_to_assign = regular_questions_to_assign
            regular_question = regular_question[5:]
        return regular_question, bonus_questions, questions_to_assign


def assign_questions_to_new_users():
    logger.info("*** assign_questions_to_users ***")
    # delete_old_questions()
    try:
        question_list = Question.active_objects.all()
        # family_members = FamilyMembers.objects.all()
        que_user_ids = list(UserQuestion.objects.values_list('user_id', flat=True))
        family_member_ids = FamilyMembers.objects.exclude(member_id__in=que_user_ids).values_list('member', flat=True)
        users = User.objects.filter(id__in = family_member_ids)
        food_regular_questions = get_food_question_list(question_list)
        sports_regular_question = get_sports_question_list(question_list)

        bonus_questions = list(question_list.filter(quiz_type="Bonus"))

        walk_question = list(question_list.filter(quiz_type="Regular", category="Walk"))
        start_date = QuizDates.objects.values("start_date").order_by("-id").first()
        if start_date:
            start_date = start_date["start_date"]
        else:
            start_date = now().date()
        

        # if len(regular_questions) < 5:
        #     logger.warning("There are not enough regular questions available to assign.")
        #     return

        current_date = start_date - timedelta(days=1)
        logger.info(f"Current date: {current_date}")
        if len(sports_regular_question) == 0 and len(food_regular_questions) == 0:
            return True, "Questions uploaded successfully."
        while sports_regular_question or food_regular_questions or bonus_questions:
            next_date = current_date + timedelta(days=1)
            logger.info(f"Next date: {next_date}")
            sports_regular_question, bonus_questions, sports_questions_to_assign, walk_question = get_question_to_assign(sports_regular_question, bonus_questions, next_date, walk_question)
            food_regular_questions, bonus_questions, food_questions_to_assign = get_question_to_assign(food_regular_questions, bonus_questions, next_date)
            for user_chunk in chunked_queryset(users):
                user_questions_to_create = []

                for user in user_chunk:
                    family_member = FamilyMembers.objects.filter(member = user).first()
                    family_member.family_group.is_quiz_starts = True
                    family_member.family_group.save()
                    # if not family_member:
                    #     logger.info(f"User {user.username} is not member of any family group's.")
                    #     continue
                    family_category = family_member.family_group.category.name
                    if family_category == "Food":
                        question_to_assign = food_questions_to_assign
                    else:
                        question_to_assign = sports_questions_to_assign
                    
                    user_question = UserQuestion.active_objects.filter(user=user, date=next_date)
                    assigned_questions_today = user_question.count()

                    if next_date.weekday() == int(config("WEEK_DAY")) and assigned_questions_today >= 8:
                        logger.info(f"User {user.id} already has 8 questions assigned for {next_date}.")
                        continue
                    elif next_date.weekday() != int(config("WEEK_DAY")) and assigned_questions_today >= 5:
                        logger.info(f"User {user.id} already has 5 questions assigned for {next_date}.")
                        continue
                    
                    for question in question_to_assign:
                        if not user_question.filter(question=question).exists():
                            user_questions_to_create.append(
                                UserQuestion(user=user, question=question, date=next_date)
                            )
                            logger.info(f"Question is assigned to the user {user} Successfully")

                if user_questions_to_create:
                    print("*** user_question _to_create", user_questions_to_create)
                    with transaction.atomic():
                        print("*** bulk creating user_questions ***")
                        UserQuestion.objects.bulk_create(user_questions_to_create)
                        logger.info(f"Assigned {len(user_questions_to_create)} questions to users for {next_date}.")

            current_date = next_date    # Update current date to next_date
        return True, "Questions uploaded successfully."
    except Exception as e:
        logger.error(f"Error assigning questions to users: {e}")
        return False, f"Error assigning questions to users: {e}"
    


def get_most_recent_monday(d):
        """Given a date, return the most recent Monday."""
        return d - timedelta(days=d.weekday())