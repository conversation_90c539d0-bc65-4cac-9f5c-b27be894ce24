import smtplib, traceback
from django.conf import settings
from base.views import create_from_exceptions
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from django.core.mail import EmailMultiAlternatives



def send_mail(request, subject, email_from, email_to, email_cc, email_bcc, html_content):
    try:
        msg = MIMEMultipart('alternative')
        msg['From'] = email_from
        msg['To'] = ', '.join(email_to)
        msg['Cc'] = ', '.join(email_cc)
        msg['Subject'] = subject
        
        # Attach HTML content
        text_content = MIMEText(html_content, 'html')
        msg.attach(text_content)

        # Combine all recipients for sending
        all_recipients = email_to + email_cc + email_bcc

        # Send the email
        # server = smtplib.SMTP_SSL(settings.EMAIL_HOST, settings.EMAIL_PORT)
        # server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
        # server.sendmail(email_from, all_recipients, msg.as_string())
        # server.quit()
        
        email = EmailMultiAlternatives(subject, "", settings.EMAIL_HOST_USER, email_to)
        email.attach_alternative(html_content, "text/html")

        # Send the email
        email.send(fail_silently=False)
        return True
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return False