#!/usr/bin/env python3
"""
查询数据库中最早和最晚的数据
"""

from hku_local_config import HKU_LOCAL_CONFIG
import psycopg2

def check_data_range():
    """查询数据的日期范围"""
    
    try:
        conn = psycopg2.connect(**HKU_LOCAL_CONFIG)
        cursor = conn.cursor()
        
        print("🔍 查询数据库中的日期范围...")
        
        # 1. 查询quiz数据的日期范围
        print(f"\n📊 Quiz数据日期范围:")
        cursor.execute("""
            SELECT 
                MIN(date) as earliest_date,
                MAX(date) as latest_date,
                COUNT(*) as total_records,
                COUNT(DISTINCT date) as unique_dates,
                COUNT(DISTINCT user_id) as unique_users
            FROM question_userquestion 
            WHERE answered = true
            AND is_deleted = false
        """)
        
        min_date, max_date, total_records, unique_dates, unique_users = cursor.fetchone()
        
        print(f"   📅 最早日期: {min_date}")
        print(f"   📅 最晚日期: {max_date}")
        print(f"   📊 总记录数: {total_records}")
        print(f"   📆 总天数: {unique_dates}")
        print(f"   👥 参与用户数: {unique_users}")
        
        # 2. 查询最早的几条记录
        print(f"\n📋 最早的5条记录:")
        cursor.execute("""
            SELECT 
                uq.date,
                fg.family_name,
                au.username,
                q.quiz_type,
                q.question
            FROM question_userquestion uq
            JOIN question_question q ON uq.question_id = q.id
            JOIN auth_user au ON uq.user_id = au.id
            JOIN family_familymembers fm ON au.id = fm.member_id
            JOIN family_familygroups fg ON fm.family_group_id = fg.id
            WHERE uq.answered = true
            AND uq.is_deleted = false
            AND q.is_deleted = false
            AND fm.is_deleted = false
            AND fm.is_member = true
            ORDER BY uq.date ASC, uq.created_at ASC
            LIMIT 5
        """)
        
        earliest_records = cursor.fetchall()
        for i, (date, family, user, quiz_type, question) in enumerate(earliest_records, 1):
            print(f"   {i}. {date} | {family} | {user} | {quiz_type} | {question[:50]}...")
        
        # 3. 查询最晚的几条记录
        print(f"\n📋 最晚的5条记录:")
        cursor.execute("""
            SELECT 
                uq.date,
                fg.family_name,
                au.username,
                q.quiz_type,
                q.question
            FROM question_userquestion uq
            JOIN question_question q ON uq.question_id = q.id
            JOIN auth_user au ON uq.user_id = au.id
            JOIN family_familymembers fm ON au.id = fm.member_id
            JOIN family_familygroups fg ON fm.family_group_id = fg.id
            WHERE uq.answered = true
            AND uq.is_deleted = false
            AND q.is_deleted = false
            AND fm.is_deleted = false
            AND fm.is_member = true
            ORDER BY uq.date DESC, uq.created_at DESC
            LIMIT 5
        """)
        
        latest_records = cursor.fetchall()
        for i, (date, family, user, quiz_type, question) in enumerate(latest_records, 1):
            print(f"   {i}. {date} | {family} | {user} | {quiz_type} | {question[:50]}...")
        
        # 4. 按日期统计记录数
        print(f"\n📈 每日记录数统计 (前10天和后10天):")
        
        print(f"\n📅 最早的10天:")
        cursor.execute("""
            SELECT 
                date,
                COUNT(*) as daily_count,
                COUNT(DISTINCT user_id) as daily_users
            FROM question_userquestion 
            WHERE answered = true
            AND is_deleted = false
            GROUP BY date
            ORDER BY date ASC
            LIMIT 10
        """)
        
        early_days = cursor.fetchall()
        for date, count, users in early_days:
            print(f"   {date}: {count} 条记录, {users} 个用户")
        
        print(f"\n📅 最晚的10天:")
        cursor.execute("""
            SELECT 
                date,
                COUNT(*) as daily_count,
                COUNT(DISTINCT user_id) as daily_users
            FROM question_userquestion 
            WHERE answered = true
            AND is_deleted = false
            GROUP BY date
            ORDER BY date DESC
            LIMIT 10
        """)
        
        late_days = cursor.fetchall()
        for date, count, users in late_days:
            print(f"   {date}: {count} 条记录, {users} 个用户")
        
        # 5. 查询步数数据的日期范围
        print(f"\n🚶 步数数据日期范围:")
        cursor.execute("""
            SELECT 
                MIN(date) as earliest_date,
                MAX(date) as latest_date,
                COUNT(*) as total_records,
                COUNT(DISTINCT date) as unique_dates,
                COUNT(DISTINCT user_id) as unique_users,
                AVG(steps) as avg_steps,
                MAX(steps) as max_steps
            FROM question_usersteps
        """)
        
        step_data = cursor.fetchone()
        if step_data[0]:  # 如果有步数数据
            min_step_date, max_step_date, step_records, step_dates, step_users, avg_steps, max_steps = step_data
            print(f"   📅 最早日期: {min_step_date}")
            print(f"   📅 最晚日期: {max_step_date}")
            print(f"   📊 总记录数: {step_records}")
            print(f"   📆 总天数: {step_dates}")
            print(f"   👥 参与用户数: {step_users}")
            print(f"   📈 平均步数: {avg_steps:.0f}")
            print(f"   🏆 最高步数: {max_steps}")
        else:
            print(f"   ❌ 没有找到步数数据")
        
        # 6. 查询家庭组创建时间
        print(f"\n🏠 家庭组创建时间:")
        try:
            cursor.execute("""
                SELECT
                    MIN(created_on) as earliest_family,
                    MAX(created_on) as latest_family,
                    COUNT(*) as total_families
                FROM family_familygroups
            """)

            family_data = cursor.fetchone()
            earliest_family, latest_family, total_families = family_data
            print(f"   📅 最早创建: {earliest_family}")
            print(f"   📅 最晚创建: {latest_family}")
            print(f"   🏠 总家庭组数: {total_families}")
        except Exception as e:
            print(f"   ❌ 查询家庭组失败: {e}")

        # 7. 查询用户注册时间
        print(f"\n👥 用户注册时间:")
        try:
            cursor.execute("""
                SELECT
                    MIN(date_joined) as earliest_user,
                    MAX(date_joined) as latest_user,
                    COUNT(*) as total_users
                FROM auth_user
            """)

            user_data = cursor.fetchone()
            earliest_user, latest_user, total_users = user_data
            print(f"   📅 最早注册: {earliest_user}")
            print(f"   📅 最晚注册: {latest_user}")
            print(f"   👥 总用户数: {total_users}")
        except Exception as e:
            print(f"   ❌ 查询用户失败: {e}")
        
        cursor.close()
        conn.close()
        
        # 8. 总结
        print(f"\n📋 数据范围总结:")
        print(f"   🎯 Quiz活动期间: {min_date} 到 {max_date}")
        print(f"   📊 活动天数: {unique_dates} 天")
        print(f"   👥 参与用户: {unique_users} 人")
        print(f"   📝 总回答数: {total_records} 条")
        print(f"   🏠 活跃家庭组: {total_families} 个")
        
        # 计算活动持续时间
        from datetime import datetime
        if min_date and max_date:
            start = datetime.strptime(str(min_date), '%Y-%m-%d')
            end = datetime.strptime(str(max_date), '%Y-%m-%d')
            duration = (end - start).days + 1
            print(f"   ⏱️  活动持续: {duration} 天")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_data_range()
