tinymce.addI18n('kk',{
"Redo": "\u049a\u0430\u0439\u0442\u0430\u0440\u0443",
"Undo": "\u0411\u043e\u043b\u0434\u044b\u0440\u043c\u0430\u0443",
"Cut": "\u049a\u0438\u044b\u043f \u0430\u043b\u0443",
"Copy": "\u041a\u04e9\u0448\u0456\u0440\u0443",
"Paste": "\u049a\u043e\u044e",
"Select all": "\u0411\u0430\u0440\u043b\u044b\u0493\u044b\u043d \u0442\u0430\u04a3\u0434\u0430\u0443",
"New document": "\u0416\u0430\u04a3\u0430 \u049b\u04b1\u0436\u0430\u0442",
"Ok": "\u041e\u041a",
"Cancel": "\u0411\u0430\u0441 \u0442\u0430\u0440\u0442\u0443",
"Visual aids": "\u041a\u04e9\u0440\u043d\u0435\u043a\u0456 \u049b\u04b1\u0440\u0430\u043b\u0434\u0430\u0440",
"Bold": "\u049a\u0430\u043b\u044b\u04a3",
"Italic": "\u041a\u04e9\u043b\u0431\u0435\u0443",
"Underline": "\u0410\u0441\u0442\u044b \u0441\u044b\u0437\u044b\u043b\u0493\u0430\u043d",
"Strikethrough": "\u0421\u044b\u0437\u044b\u043b\u0493\u0430\u043d",
"Superscript": "\u0416\u043e\u043b \u04af\u0441\u0442\u0456",
"Subscript": "\u0416\u043e\u043b \u0430\u0441\u0442\u044b",
"Clear formatting": "\u041f\u0456\u0448\u0456\u043c\u0434\u0435\u0443\u0434\u0456 \u0442\u0430\u0437\u0430\u043b\u0430\u0443",
"Align left": "\u0421\u043e\u043b\u0493\u0430 \u0442\u0443\u0440\u0430\u043b\u0430\u0443",
"Align center": "\u041e\u0440\u0442\u0430\u0441\u044b\u043d\u0430 \u0442\u0443\u0440\u0430\u043b\u0430\u0443",
"Align right": "\u041e\u04a3\u0493\u0430 \u0442\u0443\u0440\u0430\u043b\u0430\u0443",
"Justify": "\u0415\u043d\u0456 \u0431\u043e\u0439\u044b\u043d\u0448\u0430 \u0442\u0443\u0440\u0430\u043b\u0430\u0443",
"Bullet list": "\u0422\u0430\u04a3\u0431\u0430\u043b\u0430\u043d\u0493\u0430\u043d \u0442\u0456\u0437\u0456\u043c",
"Numbered list": "\u041d\u04e9\u043c\u0456\u0440\u043b\u0435\u043d\u0433\u0435\u043d \u0442\u0456\u0437\u0456\u043c",
"Decrease indent": "\u0428\u0435\u0433\u0456\u043d\u0456\u0441\u0442\u0456 \u043a\u0435\u043c\u0456\u0442\u0443",
"Increase indent": "\u0428\u0435\u0433\u0456\u043d\u0456\u0441\u0442\u0456 \u0430\u0440\u0442\u0442\u044b\u0440\u0443",
"Close": "\u0416\u0430\u0431\u0443",
"Formats": "\u041f\u0456\u0448\u0456\u043c\u0434\u0435\u0440",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u0411\u0440\u0430\u0443\u0437\u0435\u0440\u0456\u04a3\u0456\u0437 \u0430\u0440\u0430\u043b\u044b\u049b \u0441\u0430\u049b\u0442\u0430\u0493\u044b\u0448\u049b\u0430 \u0442\u0456\u043a\u0435\u043b\u0435\u0439 \u049b\u0430\u0442\u044b\u043d\u0430\u0439 \u0430\u043b\u043c\u0430\u0439\u0434\u044b. Ctrl+X\/C\/V \u043f\u0435\u0440\u043d\u0435\u043b\u0435\u0440 \u0442\u0456\u0440\u043a\u0435\u0441\u0456\u043c\u0456\u043d \u043f\u0430\u0439\u0434\u0430\u043b\u0430\u043d\u044b\u04a3\u044b\u0437.",
"Headers": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430\u043b\u0430\u0440",
"Header 1": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430 1",
"Header 2": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430 2",
"Header 3": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430 3",
"Header 4": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430 4",
"Header 5": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430 5",
"Header 6": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430 6",
"Headings": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0442\u0430\u0440",
"Heading 1": "1-\u0442\u0430\u049b\u044b\u0440\u044b\u043f",
"Heading 2": "2-\u0442\u0430\u049b\u044b\u0440\u044b\u043f",
"Heading 3": "3-\u0442\u0430\u049b\u044b\u0440\u044b\u043f",
"Heading 4": "4-\u0442\u0430\u049b\u044b\u0440\u044b\u043f",
"Heading 5": "5-\u0442\u0430\u049b\u044b\u0440\u044b\u043f",
"Heading 6": "6-\u0442\u0430\u049b\u044b\u0440\u044b\u043f",
"Preformatted": "\u0410\u043b\u0434\u044b\u043d \u0430\u043b\u0430 \u043f\u0456\u0448\u0456\u043c\u0434\u0435\u043b\u0433\u0435\u043d",
"Div": "Div",
"Pre": "Pre",
"Code": "\u041a\u043e\u0434",
"Paragraph": "\u041f\u0430\u0440\u0430\u0433\u0440\u0430\u0444",
"Blockquote": "\u0414\u04d9\u0439\u0435\u043a\u0441\u04e9\u0437",
"Inline": "\u041a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0456\u043b\u0433\u0435\u043d",
"Blocks": "\u0411\u043b\u043e\u043a\u0442\u0430\u0440",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u041e\u0441\u044b \u043e\u043f\u0446\u0438\u044f \u04e9\u0448\u0456\u0440\u0456\u043b\u043c\u0435\u0433\u0435\u043d\u0448\u0435, \u0431\u0443\u0444\u0435\u0440\u0434\u0435\u0433\u0456 \u043c\u04d9\u0442\u0456\u043d \u043a\u04d9\u0434\u0456\u043c\u0433\u0456 \u043c\u04d9\u0442\u0456\u043d \u0440\u0435\u0442\u0456\u043d\u0434\u0435 \u049b\u043e\u0439\u044b\u043b\u0430\u0434\u044b.",
"Fonts": "\u049a\u0430\u0440\u0456\u043f\u0442\u0435\u0440",
"Font Sizes": "\u049a\u0430\u0440\u0456\u043f \u04e9\u043b\u0448\u0435\u043c\u0434\u0435\u0440\u0456",
"Class": "\u0421\u044b\u043d\u044b\u043f",
"Browse for an image": "\u041a\u0435\u0441\u043a\u0456\u043d\u0434\u0456 \u0448\u043e\u043b\u0443",
"OR": "\u041d\u0415\u041c\u0415\u0421\u0415",
"Drop an image here": "\u041a\u0435\u0441\u043a\u0456\u043d\u0434\u0456 \u043e\u0441\u044b \u0436\u0435\u0440\u0434\u0435 \u0442\u0430\u0441\u0442\u0430\u04a3\u044b\u0437",
"Upload": "\u0416\u04af\u043a\u0442\u0435\u043f \u0441\u0430\u043b\u0443",
"Block": "\u0411\u043b\u043e\u043a",
"Align": "\u0422\u0443\u0440\u0430\u043b\u0430\u0443",
"Default": "\u04d8\u0434\u0435\u043f\u043a\u0456",
"Circle": "\u0428\u0435\u04a3\u0431\u0435\u0440",
"Disc": "\u0414\u0438\u0441\u043a",
"Square": "\u0428\u0430\u0440\u0448\u044b",
"Lower Alpha": "\u041a\u0456\u0448\u0456 \u04d9\u0440\u0456\u043f\u0442\u0435\u0440",
"Lower Greek": "\u041a\u0456\u0448\u0456 \u0433\u0440\u0435\u043a \u04d9\u0440\u0456\u043f\u0442\u0435\u0440\u0456",
"Lower Roman": "\u041a\u0456\u0448\u0456 \u0440\u0438\u043c \u0446\u0438\u0444\u0440\u043b\u0430\u0440\u044b",
"Upper Alpha": "\u0411\u0430\u0441 \u04d9\u0440\u0456\u043f\u0442\u0435\u0440",
"Upper Roman": "\u0411\u0430\u0441 \u0440\u0438\u043c \u0446\u0438\u0444\u0440\u043b\u0430\u0440\u044b",
"Anchor...": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435...",
"Name": "\u0410\u0442\u044b",
"Id": "Id",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "Id \u0442\u0435\u043a \u049b\u0430\u043d\u0430 \u04d9\u0440\u0456\u043f\u0442\u0435\u043d \u0431\u0430\u0441\u0442\u0430\u043b\u044b\u043f, \u04d9\u0440\u0456\u043f\u0442\u0435\u0440, \u0441\u0430\u043d\u0434\u0430\u0440, \u0441\u044b\u0437\u044b\u049b\u0448\u0430\u043b\u0430\u0440, \u043d\u04af\u043a\u0442\u0435\u043b\u0435\u0440 \u0436\u04d9\u043d\u0435 \u0442.\u0431 \u0436\u0430\u043b\u0493\u0430\u0441\u0443\u044b \u0442\u0438\u0456\u0441.",
"You have unsaved changes are you sure you want to navigate away?": "\u0421\u0430\u049b\u0442\u0430\u043b\u043c\u0430\u0493\u0430\u043d \u04e9\u0437\u0433\u0435\u0440\u0456\u0441\u0442\u0435\u0440 \u0431\u0430\u0440. \u0421\u0456\u0437 \u0448\u044b\u043d\u044b\u043c\u0435\u043d \u0431\u0430\u0441\u049b\u0430 \u0436\u0435\u0440\u0433\u0435 \u043a\u0435\u0442\u0443\u0434\u0456 \u049b\u0430\u043b\u0430\u0439\u0441\u044b\u0437 \u0431\u0430?",
"Restore last draft": "\u0421\u043e\u04a3\u0493\u044b \u0441\u0430\u049b\u0442\u0430\u043b\u0493\u0430\u043d\u0434\u044b \u049b\u0430\u043b\u043f\u044b\u043d\u0430 \u043a\u0435\u043b\u0442\u0456\u0440\u0443",
"Special characters...": "\u0410\u0440\u043d\u0430\u0439\u044b \u0442\u0430\u04a3\u0431\u0430\u043b\u0430\u0440...",
"Source code": "\u0411\u0430\u0441\u0442\u0430\u043f\u049b\u044b \u043a\u043e\u0434",
"Insert\/Edit code sample": "\u041a\u043e\u0434 \u04af\u043b\u0433\u0456\u0441\u0456\u043d \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443\/\u0442\u04af\u0437\u0435\u0442\u0443",
"Language": "\u0422\u0456\u043b",
"Code sample...": "\u041a\u043e\u0434 \u04af\u043b\u0433\u0456\u0441\u0456...",
"Color Picker": "\u0422\u04af\u0441 \u0442\u0430\u04a3\u0434\u0430\u0443 \u049b\u04b1\u0440\u0430\u043b\u044b",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "\u0421\u043e\u043b\u0434\u0430\u043d \u043e\u04a3\u0493\u0430",
"Right to left": "\u041e\u04a3\u043d\u0430\u043d \u0441\u043e\u043b\u0493\u0430",
"Emoticons...": "\u042d\u043c\u043e\u0442\u0438\u043a\u043e\u043d\u0434\u0430\u0440...",
"Metadata and Document Properties": "\u041c\u0435\u0442\u0430\u0434\u0435\u0440\u0435\u043a\u0442\u0435\u0440 \u0436\u04d9\u043d\u0435 \u049b\u04b1\u0436\u0430\u0442 \u0441\u0438\u043f\u0430\u0442\u0442\u0430\u0440\u044b",
"Title": "\u0410\u0442\u0430\u0443\u044b",
"Keywords": "Meta-keywords",
"Description": "\u0421\u0438\u043f\u0430\u0442\u0442\u0430\u043c\u0430\u0441\u044b",
"Robots": "Meta-robots",
"Author": "Meta-author",
"Encoding": "Meta-charset",
"Fullscreen": "\u0422\u043e\u043b\u044b\u049b \u044d\u043a\u0440\u0430\u043d",
"Action": "\u04d8\u0440\u0435\u043a\u0435\u0442",
"Shortcut": "\u041f\u0435\u0440\u043d\u0435\u043b\u0435\u0440 \u0442\u0456\u0440\u043a\u0435\u0441\u0456\u043c\u0456",
"Help": "\u0410\u043d\u044b\u049b\u0442\u0430\u043c\u0430",
"Address": "\u041c\u0435\u043a\u0435\u043d\u0436\u0430\u0439",
"Focus to menubar": "\u041c\u04d9\u0437\u0456\u0440 \u0436\u043e\u043b\u0430\u0493\u044b\u043d \u0444\u043e\u043a\u0443\u0441\u0442\u0430\u0443",
"Focus to toolbar": "\u049a\u04b1\u0440\u0430\u043b\u0434\u0430\u0440 \u0442\u0430\u049b\u0442\u0430\u0441\u044b\u043d \u0444\u043e\u043a\u0443\u0441\u0442\u0430\u0443",
"Focus to element path": "\u042d\u043b\u0435\u043c\u0435\u043d\u0442 \u0436\u043e\u043b\u044b\u043d \u0444\u043e\u043a\u0443\u0441\u0442\u0430\u0443",
"Focus to contextual toolbar": "\u041c\u04d9\u0442\u0456\u043d\u043c\u04d9\u043d\u0434\u0456\u043a \u049b\u04b1\u0440\u0430\u043b\u0434\u0430\u0440 \u0442\u0430\u049b\u0442\u0430\u0441\u044b\u043d \u0444\u043e\u043a\u0443\u0441\u0442\u0430\u0443",
"Insert link (if link plugin activated)": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435\u043d\u0456 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443 (\u0441\u0456\u043b\u0442\u0435\u043c\u0435 \u049b\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u0456 \u0431\u0435\u043b\u0441\u0435\u043d\u0434\u0456\u0440\u0456\u043b\u0433\u0435\u043d \u0431\u043e\u043b\u0441\u0430)",
"Save (if save plugin activated)": "\u0421\u0430\u049b\u0442\u0430\u0443 (\u0441\u0430\u049b\u0442\u0430\u0443 \u049b\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u049b\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u0456 \u0431\u0435\u043b\u0441\u0435\u043d\u0434\u0456\u0440\u0456\u043b\u0433\u0435\u043d \u0431\u043e\u043b\u0441\u0430)",
"Find (if searchreplace plugin activated)": "\u0422\u0430\u0431\u0443 (\u0456\u0437\u0434\u0435\u0443\/\u0430\u0443\u044b\u0441\u0442\u044b\u0440\u0443 \u049b\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u0456 \u0431\u0435\u043b\u0441\u0435\u043d\u0434\u0456\u0440\u0456\u043b\u0433\u0435\u043d \u0431\u043e\u043b\u0441\u0430)",
"Plugins installed ({0}):": "\u041e\u0440\u043d\u0430\u0442\u044b\u043b\u0493\u0430\u043d \u049b\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u044c\u0434\u0435\u0440 ({0}):",
"Premium plugins:": "\u041f\u0440\u0435\u043c\u0438\u0443\u043c \u049b\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u044c\u0434\u0435\u0440:",
"Learn more...": "\u049a\u043e\u0441\u044b\u043c\u0448\u0430 \u043c\u04d9\u043b\u0456\u043c\u0435\u0442\u0442\u0435\u0440...",
"You are using {0}": "\u0421\u0456\u0437 {0} \u043f\u0430\u0439\u0434\u0430\u043b\u0430\u043d\u0443\u0434\u0430\u0441\u044b\u0437",
"Plugins": "\u049a\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u044c\u0434\u0435\u0440",
"Handy Shortcuts": "\u042b\u04a3\u0493\u0430\u0439\u043b\u044b \u043f\u0435\u0440\u043d\u0435\u043b\u0435\u0440 \u0442\u0456\u0440\u043a\u0435\u0441\u0456\u043c\u0434\u0435\u0440\u0456",
"Horizontal line": "\u041a\u04e9\u043b\u0434\u0435\u043d\u0435\u04a3 \u0441\u044b\u0437\u044b\u049b",
"Insert\/edit image": "\u0421\u0443\u0440\u0435\u0442 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443\/\u0442\u04af\u0437\u0435\u0442\u0443",
"Image description": "\u0421\u0443\u0440\u0435\u0442 \u0441\u0438\u043f\u0430\u0442\u0442\u0430\u043c\u0430\u0441\u044b",
"Source": "\u0410\u0434\u0440\u0435\u0441\u0456",
"Dimensions": "\u04e8\u043b\u0448\u0435\u043c\u0434\u0435\u0440\u0456",
"Constrain proportions": "\u041f\u0440\u043e\u043f\u043e\u0440\u0446\u0438\u044f\u043b\u0430\u0440\u0434\u044b \u0441\u0430\u049b\u0442\u0430\u0443",
"General": "\u0416\u0430\u043b\u043f\u044b",
"Advanced": "\u049a\u043e\u0441\u044b\u043c\u0448\u0430",
"Style": "\u0421\u0442\u0438\u043b\u0456",
"Vertical space": "\u0422\u0456\u043a \u043a\u0435\u04a3\u0434\u0456\u0433\u0456",
"Horizontal space": "\u041a\u04e9\u043b\u0434\u0435\u043d\u0435\u04a3\u0456\u043d\u0435\u043d \u049b\u0430\u043b\u0430\u0442\u044b\u043d \u043e\u0440\u044b\u043d",
"Border": "\u0416\u0438\u0435\u0433\u0456",
"Insert image": "\u0421\u0443\u0440\u0435\u0442 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"Image...": "\u041a\u0435\u0441\u043a\u0456\u043d...",
"Image list": "\u041a\u0435\u0441\u043a\u0456\u043d\u0434\u0435\u0440 \u0442\u0456\u0437\u0456\u043c\u0456",
"Rotate counterclockwise": "\u0421\u0430\u0493\u0430\u0442 \u0442\u0456\u043b\u0456\u043d\u0456\u04a3 \u0431\u0430\u0493\u044b\u0442\u044b\u043d\u0430 \u049b\u0430\u0440\u0441\u044b \u0431\u04b1\u0440\u0443",
"Rotate clockwise": "\u0421\u0430\u0493\u0430\u0442 \u0442\u0456\u043b\u0456\u043d\u0456\u04a3 \u0431\u0430\u0493\u044b\u0442\u044b\u043c\u0435\u043d \u0431\u04b1\u0440\u0443",
"Flip vertically": "\u0422\u0456\u0433\u0456\u043d\u0435\u043d \u0430\u0443\u0434\u0430\u0440\u0443",
"Flip horizontally": "\u041a\u04e9\u043b\u0434\u0435\u043d\u0435\u04a3\u043d\u0435\u043d \u0430\u0443\u0434\u0430\u0440\u0443",
"Edit image": "\u0421\u0443\u0440\u0435\u0442\u0442\u0456 \u04e9\u0437\u0433\u0435\u0440\u0442\u0443",
"Image options": "\u0421\u0443\u0440\u0435\u0442 \u0431\u0430\u043f\u0442\u0430\u0443\u043b\u0430\u0440\u044b",
"Zoom in": "\u0416\u0430\u049b\u044b\u043d\u0434\u0430\u0442\u0443",
"Zoom out": "\u0410\u043b\u044b\u0441\u0442\u0430\u0442\u0443",
"Crop": "\u041a\u0435\u0441\u0443",
"Resize": "\u04e8\u043b\u0448\u0435\u043c\u0456\u043d \u04e9\u0437\u0433\u0435\u0440\u0442\u0443",
"Orientation": "\u0411\u0430\u0493\u0434\u0430\u0440",
"Brightness": "\u0410\u0448\u044b\u049b\u0442\u0430\u0443",
"Sharpen": "\u041d\u0430\u049b\u0442\u044b\u043b\u0430\u0443",
"Contrast": "\u049a\u043e\u044e\u043b\u0430\u0442\u0443",
"Color levels": "\u0422\u04af\u0441 \u0434\u0435\u04a3\u0433\u0435\u0439\u043b\u0435\u0440\u0456",
"Gamma": "\u0413\u0430\u043c\u043c\u0430",
"Invert": "\u041a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"Apply": "\u0421\u0430\u049b\u0442\u0430\u0443",
"Back": "\u0410\u0440\u0442\u049b\u0430",
"Insert date\/time": "\u041a\u04af\u043d\/\u0443\u0430\u049b\u044b\u0442 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"Date\/time": "\u041a\u04af\u043d\/\u0443\u0430\u049b\u044b\u0442",
"Insert\/Edit Link": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443\/\u04e9\u04a3\u0434\u0435\u0443",
"Insert\/edit link": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443\/\u0442\u04af\u0437\u0435\u0442\u0443",
"Text to display": "\u041a\u04e9\u0440\u0441\u0435\u0442\u0456\u043b\u0435\u0442\u0456\u043d \u043c\u04d9\u0442\u0456\u043d",
"Url": "URL-\u0430\u0434\u0440\u0435\u0441\u0456",
"Open link in...": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435\u043d\u0456 \u0430\u0448\u0443...",
"Current window": "\u0410\u0493\u044b\u043c\u0434\u0430\u0493\u044b \u0442\u0435\u0440\u0435\u0437\u0435",
"None": "\u0416\u043e\u049b",
"New window": "\u0416\u0430\u04a3\u0430 \u0442\u0435\u0440\u0435\u0437\u0435",
"Remove link": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435\u043d\u0456 \u0430\u043b\u044b\u043f \u0442\u0430\u0441\u0442\u0430\u0443",
"Anchors": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435\u043b\u0435\u0440",
"Link...": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435...",
"Paste or type a link": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435\u043d\u0456 \u049b\u043e\u0439\u044b\u04a3\u044b\u0437 \u043d\u0435\u043c\u0435\u0441\u0435 \u0442\u0435\u0440\u0456\u04a3\u0456\u0437",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u0421\u0456\u0437 \u0435\u04a3\u0433\u0456\u0437\u0456\u043f \u0442\u04b1\u0440\u0493\u0430\u043d URL e-mail \u0430\u0434\u0440\u0435\u0441\u0456 \u0431\u043e\u043b\u044b\u043f \u0442\u0430\u0431\u044b\u043b\u0430\u0434\u044b. \u0410\u043b\u0434\u044b\u043d\u0430 mailto: \u043f\u0440\u0435\u0444\u0438\u043a\u0441\u0456\u043d \u049b\u043e\u0441\u0443\u0434\u044b \u049b\u0430\u043b\u0430\u0439\u0441\u044b\u0437 \u0431\u0430?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u0421\u0456\u0437 \u0435\u04a3\u0433\u0456\u0437\u0456\u043f \u0442\u04b1\u0440\u0493\u0430\u043d URL \u0441\u044b\u0440\u0442\u049b\u044b \u0441\u0456\u043b\u0442\u0435\u043c\u0435 \u0431\u043e\u043b\u044b\u043f \u0442\u0430\u0431\u044b\u043b\u0430\u0434\u044b. \u0410\u043b\u0434\u044b\u043d\u0430 http:\/\/ \u043f\u0440\u0435\u0444\u0438\u043a\u0441\u0456\u043d \u049b\u043e\u0441\u0443\u0434\u044b \u049b\u0430\u043b\u0430\u0439\u0441\u044b\u0437 \u0431\u0430?",
"Link list": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435\u043b\u0435\u0440 \u0442\u0456\u0437\u0456\u043c\u0456",
"Insert video": "\u0412\u0438\u0434\u0435\u043e \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"Insert\/edit video": "\u0412\u0438\u0434\u0435\u043e \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443\/\u0442\u04af\u0437\u0435\u0442\u0443",
"Insert\/edit media": "\u041c\u0435\u0434\u0438\u0430 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443\/\u0442\u04af\u0437\u0435\u0442\u0443",
"Alternative source": "\u049a\u043e\u0441\u044b\u043c\u0448\u0430 \u0430\u0434\u0440\u0435\u0441\u0456",
"Alternative source URL": "\u0411\u0430\u043b\u0430\u043c\u0430\u043b\u044b \u043a\u04e9\u0437\u0434\u0456\u04a3 URL \u043c\u0435\u043a\u0435\u043d\u0436\u0430\u0439\u044b",
"Media poster (Image URL)": "\u041c\u0435\u0434\u0438\u0430\u0444\u0430\u0439\u043b\u0434\u044b \u0436\u0430\u0440\u0438\u044f\u043b\u0430\u0443\u0448\u044b (\u043a\u0435\u0441\u043a\u0456\u043d\u043d\u0456\u04a3 URL \u043c\u0435\u043a\u0435\u043d\u0436\u0430\u0439\u044b)",
"Paste your embed code below:": "\u0422\u04e9\u043c\u0435\u043d\u0434\u0435\u0433\u0456 \u043a\u043e\u0434\u0442\u044b \u043a\u04e9\u0448\u0456\u0440\u0456\u043f \u0430\u043b\u044b\u043f, \u049b\u043e\u0439\u044b\u04a3\u044b\u0437:",
"Embed": "\u0415\u043d\u0434\u0456\u0440\u0443",
"Media...": "\u041c\u0435\u0434\u0438\u0430\u0444\u0430\u0439\u043b...",
"Nonbreaking space": "\u04ae\u0437\u0434\u0456\u043a\u0441\u0456\u0437 \u0431\u043e\u0441 \u043e\u0440\u044b\u043d",
"Page break": "\u0411\u0435\u0442 \u04af\u0437\u0456\u043b\u0456\u043c\u0456",
"Paste as text": "\u041c\u04d9\u0442\u0456\u043d \u0440\u0435\u0442\u0456\u043d\u0434\u0435 \u049b\u043e\u044e",
"Preview": "\u0410\u043b\u0434\u044b\u043d-\u0430\u043b\u0430 \u049b\u0430\u0440\u0430\u0443",
"Print...": "\u0411\u0430\u0441\u044b\u043f \u0448\u044b\u0493\u0430\u0440\u0443...",
"Save": "\u0421\u0430\u049b\u0442\u0430\u0443",
"Find": "\u0422\u0430\u0431\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u04d9\u0442\u0456\u043d",
"Replace with": "\u0410\u0443\u044b\u0441\u0442\u044b\u0440\u0430\u0442\u044b\u043d \u043c\u04d9\u0442\u0456\u043d",
"Replace": "\u0410\u0443\u044b\u0441\u0442\u044b\u0440\u0443",
"Replace all": "\u0411\u0430\u0440\u043b\u044b\u0493\u044b\u043d \u0430\u0443\u044b\u0441\u0442\u044b\u0440\u0443",
"Previous": "\u0410\u043b\u0434\u044b\u04a3\u0493\u044b",
"Next": "\u041a\u0435\u043b\u0435\u0441\u0456",
"Find and replace...": "\u0422\u0430\u0431\u0443 \u0436\u04d9\u043d\u0435 \u0430\u0443\u044b\u0441\u0442\u044b\u0440\u0443...",
"Could not find the specified string.": "\u041a\u04e9\u0440\u0441\u0435\u0442\u0456\u043b\u0433\u0435\u043d \u0436\u043e\u043b \u0442\u0430\u0431\u044b\u043b\u043c\u0430\u0434\u044b.",
"Match case": "\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0434\u0456 \u0435\u0441\u043a\u0435\u0440\u0443",
"Find whole words only": "\u0422\u0435\u043a \u0431\u04af\u0442\u0456\u043d \u0441\u04e9\u0437\u0434\u0435\u0440\u0434\u0456 \u0442\u0430\u0431\u0443",
"Spell check": "\u0415\u043c\u043b\u0435\u043d\u0456 \u0442\u0435\u043a\u0441\u0435\u0440\u0443",
"Ignore": "\u0415\u043b\u0435\u043c\u0435\u0443",
"Ignore all": "\u0415\u0448\u049b\u0430\u0439\u0441\u044b\u0441\u044b\u043d \u0435\u043b\u0435\u043c\u0435\u0443",
"Finish": "\u0410\u044f\u049b\u0442\u0430\u0443",
"Add to Dictionary": "\u0421\u04e9\u0437\u0434\u0456\u043a\u043a\u0435 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"Insert table": "\u041a\u0435\u0441\u0442\u0435 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"Table properties": "\u041a\u0435\u0441\u0442\u0435 \u0441\u0438\u043f\u0430\u0442\u0442\u0430\u0440\u044b",
"Delete table": "\u041a\u0435\u0441\u0442\u0435\u043d\u0456 \u0436\u043e\u044e",
"Cell": "\u04b0\u044f\u0448\u044b\u049b",
"Row": "\u0416\u043e\u043b",
"Column": "\u0411\u0430\u0493\u0430\u043d",
"Cell properties": "\u04b0\u044f\u0448\u044b\u049b \u0441\u0438\u043f\u0430\u0442\u0442\u0430\u0440\u044b",
"Merge cells": "\u04b0\u044f\u0448\u044b\u049b\u0442\u0430\u0440\u0434\u044b \u0431\u0456\u0440\u0456\u043a\u0442\u0456\u0440\u0443",
"Split cell": "\u04b0\u044f\u0448\u044b\u049b\u0442\u044b \u0431\u04e9\u043b\u0443",
"Insert row before": "\u04ae\u0441\u0442\u0456\u043d\u0435 \u0436\u043e\u043b \u049b\u043e\u0441\u0443",
"Insert row after": "\u0410\u0441\u0442\u044b\u043d\u0430 \u0436\u043e\u043b \u049b\u043e\u0441\u0443",
"Delete row": "\u0416\u043e\u043b\u0434\u044b \u0436\u043e\u044e",
"Row properties": "\u0416\u043e\u043b \u0441\u0438\u043f\u0430\u0442\u0442\u0430\u0440\u044b",
"Cut row": "\u0416\u043e\u043b\u0434\u044b \u049b\u0438\u044b\u043f \u0430\u043b\u0443",
"Copy row": "\u0416\u043e\u043b\u0434\u044b \u043a\u04e9\u0448\u0456\u0440\u0443",
"Paste row before": "\u0416\u043e\u043b\u0434\u044b\u04a3 \u04af\u0441\u0442\u0456\u043d\u0435 \u049b\u043e\u044e",
"Paste row after": "\u0416\u043e\u043b\u0434\u044b\u04a3 \u0430\u0441\u0442\u044b\u043d\u0430 \u049b\u043e\u044e",
"Insert column before": "\u0410\u043b\u0434\u044b\u043d\u0430 \u0431\u0430\u0493\u0430\u043d \u049b\u043e\u0441\u0443",
"Insert column after": "\u0410\u0440\u0442\u044b\u043d\u0430 \u0431\u0430\u0493\u0430\u043d \u049b\u043e\u0441\u0443",
"Delete column": "\u0411\u0430\u0493\u0430\u043d\u0434\u044b \u0436\u043e\u044e",
"Cols": "\u0411\u0430\u0493\u0430\u043d\u044b",
"Rows": "\u0416\u043e\u043b\u044b",
"Width": "\u04b0\u0437\u044b\u043d\u0434\u044b\u0493\u044b",
"Height": "\u0411\u0438\u0456\u043a\u0442\u0456\u0433\u0456",
"Cell spacing": "\u04b0\u044f\u0448\u044b\u049b \u0430\u0440\u0430\u043b\u044b\u0493\u044b",
"Cell padding": "\u04b0\u044f\u0448\u044b\u049b \u043a\u0435\u04a3\u0434\u0456\u0433\u0456",
"Show caption": "\u0416\u0430\u0437\u0431\u0430\u043d\u044b \u043a\u04e9\u0440\u0441\u0435\u0442\u0443",
"Left": "\u0421\u043e\u043b\u0493\u0430",
"Center": "\u041e\u0440\u0442\u0430\u0441\u044b\u043d\u0430",
"Right": "\u041e\u04a3\u0493\u0430",
"Cell type": "\u04b0\u044f\u0448\u044b\u049b \u0442\u0438\u043f\u0456",
"Scope": "\u0410\u0443\u043c\u0430\u0493\u044b",
"Alignment": "\u041e\u0440\u043d\u0430\u043b\u0430\u0441\u0443\u044b",
"H Align": "\u041a\u04e9\u043b\u0434\u0435\u043d\u0435\u04a3\u043d\u0435\u043d \u0442\u0443\u0440\u0430\u043b\u0430\u0443",
"V Align": "\u0422\u0456\u0433\u0456\u043d\u0435\u043d \u0442\u0443\u0440\u0430\u043b\u0430\u0443",
"Top": "\u04ae\u0441\u0442\u0456",
"Middle": "\u041e\u0440\u0442\u0430\u0441\u044b",
"Bottom": "\u0410\u0441\u0442\u044b",
"Header cell": "\u0422\u0430\u049b\u044b\u0440\u044b\u043f\u0448\u0430 \u04b1\u044f\u0448\u044b\u049b",
"Row group": "\u0416\u043e\u043b \u0442\u043e\u0431\u044b",
"Column group": "\u0411\u0430\u0493\u0430\u043d \u0442\u043e\u0431\u044b",
"Row type": "\u0416\u043e\u043b \u0442\u0438\u043f\u0456",
"Header": "\u0411\u0430\u0441 \u0436\u0430\u0493\u044b",
"Body": "\u041d\u0435\u0433\u0456\u0437\u0433\u0456 \u0431\u04e9\u043b\u0456\u0433\u0456",
"Footer": "\u0410\u044f\u049b \u0436\u0430\u0493\u044b",
"Border color": "\u0416\u0438\u0435\u043a \u0442\u04af\u0441\u0456",
"Insert template...": "\u04ae\u043b\u0433\u0456 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443...",
"Templates": "\u04ae\u043b\u0433\u0456\u043b\u0435\u0440",
"Template": "\u04ae\u043b\u0433\u0456",
"Text color": "\u041c\u04d9\u0442\u0456\u043d \u0442\u04af\u0441\u0456",
"Background color": "\u04e8\u04a3\u0456\u043d\u0456\u04a3 \u0442\u04af\u0441\u0456",
"Custom...": "\u04e8\u0437\u0433\u0435\u0440\u0442\u0443",
"Custom color": "\u0422\u04af\u0441 \u04e9\u0437\u0433\u0435\u0440\u0442\u0443",
"No color": "\u0422\u04af\u0441\u0441\u0456\u0437",
"Remove color": "\u0422\u04af\u0441\u0442\u0456 \u0436\u043e\u044e",
"Table of Contents": "\u041c\u0430\u0437\u043c\u04b1\u043d\u0434\u0430\u0440 \u043a\u0435\u0441\u0442\u0435\u0441\u0456",
"Show blocks": "\u0411\u043b\u043e\u043a\u0442\u0430\u0440\u0434\u044b \u043a\u04e9\u0440\u0441\u0435\u0442\u0443",
"Show invisible characters": "\u041a\u04e9\u0440\u0456\u043d\u0431\u0435\u0439\u0442\u0456\u043d \u0442\u0430\u04a3\u0431\u0430\u043b\u0430\u0440\u0434\u044b \u043a\u04e9\u0440\u0441\u0435\u0442\u0443",
"Word count": "\u0421\u04e9\u0437 \u0441\u0430\u043d\u044b",
"Words: {0}": "\u0421\u04e9\u0437 \u0441\u0430\u043d\u044b: {0}",
"{0} words": "{0} \u0441\u04e9\u0437",
"File": "\u0424\u0430\u0439\u043b",
"Edit": "\u0422\u04af\u0437\u0435\u0442\u0443",
"Insert": "\u041a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"View": "\u041a\u04e9\u0440\u0456\u043d\u0456\u0441",
"Format": "\u0424\u043e\u0440\u043c\u0430\u0442",
"Table": "\u041a\u0435\u0441\u0442\u0435",
"Tools": "\u049a\u04b1\u0440\u0430\u043b\u0434\u0430\u0440",
"Powered by {0}": "{0} \u0442\u0435\u0445\u043d\u043e\u043b\u043e\u0433\u0438\u044f\u0441\u044b\u043d\u0430 \u043d\u0435\u0433\u0456\u0437\u0434\u0435\u043b\u0433\u0435\u043d",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u0424\u043e\u0440\u043c\u0430\u0442\u0442\u0430\u043b\u0493\u0430\u043d \u043c\u04d9\u0442\u0456\u043d \u0430\u0443\u043c\u0430\u0493\u044b. \u041c\u0435\u043d\u044e \u043a\u04e9\u0440\u0441\u0435\u0442\u0443 \u04af\u0448\u0456\u043d ALT-F9 \u0431\u0430\u0441\u044b\u04a3\u044b\u0437. \u049a\u04b1\u0440\u0430\u043b\u0434\u0430\u0440 \u043f\u0430\u043d\u0435\u043b\u0456\u043d \u043a\u04e9\u0440\u0441\u0435\u0442\u0443 \u04af\u0448\u0456\u043d ALT-F10 \u0431\u0430\u0441\u044b\u04a3\u044b\u0437. \u041a\u04e9\u043c\u0435\u043a \u0430\u043b\u0443 \u04af\u0448\u0456\u043d ALT-0 \u0431\u0430\u0441\u044b\u04a3\u044b\u0437.",
"Image title": "\u041a\u0435\u0441\u043a\u0456\u043d \u0430\u0442\u0430\u0443\u044b",
"Border width": "\u0416\u0438\u0435\u043a \u0435\u043d\u0456",
"Border style": "\u0416\u0438\u0435\u043a \u043c\u04d9\u043d\u0435\u0440\u0456",
"Error": "\u049a\u0430\u0442\u0435",
"Warn": "\u0415\u0441\u043a\u0435\u0440\u0442\u0443",
"Valid": "\u0416\u0430\u0440\u0430\u043c\u0434\u044b",
"To open the popup, press Shift+Enter": "\u049a\u0430\u043b\u049b\u044b\u043c\u0430\u043b\u044b \u0442\u0435\u0440\u0435\u0437\u0435\u043d\u0456 \u0430\u0448\u0443 \u04af\u0448\u0456\u043d Shift+Enter \u0431\u0430\u0441\u044b\u04a3\u044b\u0437",
"Rich Text Area. Press ALT-0 for help.": "\u041f\u0456\u0448\u0456\u043c\u0434\u0435\u043b\u0433\u0435\u043d \u043c\u04d9\u0442\u0456\u043d \u0430\u0443\u043c\u0430\u0493\u044b. \u0410\u043d\u044b\u049b\u0442\u0430\u043c\u0430 \u0430\u043b\u0443 \u04af\u0448\u0456\u043d ALT-0 \u0431\u0430\u0441\u044b\u04a3\u044b\u0437.",
"System Font": "\u0416\u04af\u0439\u0435 \u049b\u0430\u0440\u043f\u0456",
"Failed to upload image: {0}": "\u041a\u0435\u0441\u043a\u0456\u043d \u0436\u04af\u043a\u0442\u0435\u043f \u0441\u0430\u043b\u044b\u043d\u0431\u0430\u0434\u044b: {0}",
"Failed to load plugin: {0} from url {1}": "\u049a\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u044c \u0436\u04af\u043a\u0442\u0435\u043b\u043c\u0435\u0434\u0456: {0} {1} URL \u043c\u0435\u043a\u0435\u043d\u0436\u0430\u0439\u044b\u043d\u0430\u043d",
"Failed to load plugin url: {0}": "\u049a\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u044c \u0436\u04af\u043a\u0442\u0435\u043b\u043c\u0435\u0434\u0456 URL \u043c\u0435\u043a\u0435\u043d\u0436\u0430\u0439\u044b: {0}",
"Failed to initialize plugin: {0}": "\u049a\u043e\u0441\u044b\u043b\u0430\u0442\u044b\u043d \u043c\u043e\u0434\u0443\u043b\u044c \u0431\u0430\u043f\u0442\u0430\u043d\u0434\u044b\u0440\u044b\u043b\u043c\u0430\u0434\u044b: {0}",
"example": "\u043c\u044b\u0441\u0430\u043b",
"Search": "\u0406\u0437\u0434\u0435\u0443",
"All": "\u0411\u0430\u0440\u043b\u044b\u0493\u044b",
"Currency": "\u0412\u0430\u043b\u044e\u0442\u0430",
"Text": "\u041c\u04d9\u0442\u0456\u043d",
"Quotations": "\u0422\u044b\u0440\u043d\u0430\u049b\u0448\u0430\u043b\u0430\u0440",
"Mathematical": "\u041c\u0430\u0442\u0435\u043c\u0430\u0442\u0438\u043a\u0430\u043b\u044b\u049b",
"Extended Latin": "\u041a\u0435\u04a3\u0435\u0439\u0442\u0456\u043b\u0433\u0435\u043d \u043b\u0430\u0442\u044b\u043d",
"Symbols": "\u0422\u0430\u04a3\u0431\u0430\u043b\u0430\u0440",
"Arrows": "\u041a\u04e9\u0440\u0441\u0435\u0442\u043a\u0456\u043b\u0435\u0440",
"User Defined": "\u041f\u0430\u0439\u0434\u0430\u043b\u0430\u043d\u0443\u0448\u044b \u0430\u043d\u044b\u049b\u0442\u0430\u0493\u0430\u043d",
"dollar sign": "\u0434\u043e\u043b\u043b\u0430\u0440 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"currency sign": "\u0432\u0430\u043b\u044e\u0442\u0430 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"euro-currency sign": "\u0435\u0443\u0440\u043e \u0432\u0430\u043b\u044e\u0442\u0430\u0441\u044b\u043d\u044b\u04a3 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"colon sign": "\u049b\u043e\u0441 \u043d\u04af\u043a\u0442\u0435 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"cruzeiro sign": "\u043a\u0440\u0443\u0437\u0435\u0439\u0440\u043e \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"french franc sign": "\u0444\u0440\u0430\u043d\u0446\u0443\u0437\u0434\u044b\u049b \u0444\u0440\u0430\u043d\u043a \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"lira sign": "\u043b\u0438\u0440\u0430 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"mill sign": "\u043c\u0438\u043b\u043b \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"naira sign": "\u043d\u0430\u0439\u0440\u0430 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"peseta sign": "\u043f\u0435\u0441\u0435\u0442\u0430 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"rupee sign": "\u0440\u0443\u043f\u0438\u044f \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"won sign": "\u0432\u043e\u043d \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"new sheqel sign": "\u0436\u0430\u04a3\u0430 \u0448\u0435\u043a\u0435\u043b\u044c \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"dong sign": "\u0434\u043e\u043d\u0433 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"kip sign": "\u043a\u0438\u043f \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"tugrik sign": "\u0442\u0443\u0433\u0440\u0438\u043a \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"drachma sign": "\u0434\u0440\u0430\u0445\u043c\u0430 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"german penny symbol": "\u0433\u0435\u0440\u043c\u0430\u043d\u0434\u044b\u049b \u043f\u0435\u043d\u043d\u0438 \u0442\u0430\u04a3\u0431\u0430\u0441\u044b",
"peso sign": "\u043f\u0435\u0441\u043e \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"guarani sign": "\u0433\u0443\u0430\u0440\u0430\u043d\u0438 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"austral sign": "\u0430\u0443\u0441\u0442\u0440\u0430\u043b \u0431\u0435\u0433\u043b\u0456\u0441\u0456",
"hryvnia sign": "\u0433\u0440\u0438\u0432\u043d\u0430 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"cedi sign": "\u0441\u0435\u0434\u0438 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"livre tournois sign": "\u0442\u0443\u0440 \u043b\u0438\u0432\u0440\u044b \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"spesmilo sign": "\u0441\u043f\u0435\u0441\u043c\u0438\u043b\u043e \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"tenge sign": "\u0442\u0435\u04a3\u0433\u0435 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"indian rupee sign": "\u04af\u043d\u0434\u0456 \u0440\u0443\u043f\u0438\u044f\u0441\u044b \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"turkish lira sign": "\u0442\u04af\u0440\u0456\u043a \u043b\u0438\u0440\u0430\u0441\u044b \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"nordic mark sign": "\u0441\u043a\u0430\u043d\u0434\u0438\u043d\u0430\u0432\u0438\u044f\u043b\u044b\u049b \u043c\u0430\u0440\u043a\u0430 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"manat sign": "\u043c\u0430\u043d\u0430\u0442 \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"ruble sign": "\u0440\u0443\u0431\u043b\u044c \u0431\u0435\u043b\u0433\u0456\u0441\u0456",
"yen character": "\u0439\u0435\u043d\u0430 \u0442\u0430\u04a3\u0431\u0430\u0441\u044b",
"yuan character": "\u044e\u0430\u043d\u044c \u0442\u0430\u04a3\u0431\u0430\u0441\u044b",
"yuan character, in hong kong and taiwan": "\u044e\u0430\u043d\u044c \u0442\u0430\u04a3\u0431\u0430\u0441\u044b, \u0413\u043e\u043d\u043a\u043e\u043d\u0433 \u043f\u0435\u043d \u0422\u0430\u0439\u0432\u0430\u043d\u044c\u0434\u0430",
"yen\/yuan character variant one": "\u0439\u0435\u043d\u0430\/\u044e\u0430\u043d\u044c \u0442\u0430\u04a3\u0431\u0430\u0441\u044b\u043d\u044b\u04a3 \u0431\u0456\u0440\u0456\u043d\u0448\u0456 \u043d\u04b1\u0441\u049b\u0430\u0441\u044b",
"Loading emoticons...": "\u042d\u043c\u043e\u0442\u0438\u043a\u043e\u043d\u0434\u0430\u0440 \u0436\u04af\u043a\u0442\u0435\u043b\u0443\u0434\u0435...",
"Could not load emoticons": "\u042d\u043c\u043e\u0442\u0438\u043a\u043e\u043d\u0434\u0430\u0440\u0434\u044b \u0436\u04af\u043a\u0442\u0435\u0443 \u043c\u04af\u043c\u043a\u0456\u043d \u0431\u043e\u043b\u043c\u0430\u0434\u044b",
"People": "\u0410\u0434\u0430\u043c\u0434\u0430\u0440",
"Animals and Nature": "\u0416\u0430\u043d\u0443\u0430\u0440\u043b\u0430\u0440 \u0436\u04d9\u043d\u0435 \u0442\u0430\u0431\u0438\u0493\u0430\u0442",
"Food and Drink": "\u0422\u0430\u0493\u0430\u043c\u0434\u0430\u0440 \u0436\u04d9\u043d\u0435 \u0441\u0443\u0441\u044b\u043d\u0434\u0430\u0440",
"Activity": "\u0411\u0435\u043b\u0441\u0435\u043d\u0434\u0456\u043b\u0456\u043a",
"Travel and Places": "\u0421\u0430\u044f\u0445\u0430\u0442 \u0436\u04d9\u043d\u0435 \u043e\u0440\u044b\u043d\u0434\u0430\u0440",
"Objects": "\u041d\u044b\u0441\u0430\u043d\u0434\u0430\u0440",
"Flags": "\u0422\u0443\u043b\u0430\u0440",
"Characters": "\u0422\u0430\u04a3\u0431\u0430\u043b\u0430\u0440",
"Characters (no spaces)": "\u0422\u0430\u04a3\u0431\u0430\u043b\u0430\u0440 (\u043e\u0440\u044b\u043d\u0434\u0430\u0440\u0441\u044b\u0437)",
"Error: Form submit field collision.": "\u049a\u0430\u0442\u0435: \u043f\u0456\u0448\u0456\u043d\u0434\u0456 \u0436\u0456\u0431\u0435\u0440\u0443 \u04e9\u0440\u0456\u0441\u0456\u043d\u0456\u04a3 \u049b\u0430\u0439\u0448\u044b\u043b\u044b\u0493\u044b.",
"Error: No form element found.": "\u049a\u0430\u0442\u0435: \u043f\u0456\u0448\u0456\u043d \u044d\u043b\u0435\u043c\u0435\u043d\u0442\u0456 \u0442\u0430\u0431\u044b\u043b\u043c\u0430\u0434\u044b.",
"Update": "\u0416\u0430\u04a3\u0430\u0440\u0442\u0443",
"Color swatch": "\u0422\u04af\u0441 \u04af\u043b\u0433\u0456\u0441\u0456",
"Turquoise": "\u041a\u04e9\u0433\u0456\u043b\u0434\u0456\u0440",
"Green": "\u0416\u0430\u0441\u044b\u043b",
"Blue": "\u041a\u04e9\u043a",
"Purple": "\u041a\u04af\u043b\u0433\u0456\u043d",
"Navy Blue": "\u041a\u04af\u04a3\u0433\u0456\u0440\u0442 \u043a\u04e9\u043a",
"Dark Turquoise": "\u041a\u04af\u04a3\u0433\u0456\u0440\u0442 \u043a\u04e9\u0433\u0456\u043b\u0434\u0456\u0440",
"Dark Green": "\u041a\u04af\u04a3\u0433\u0456\u0440\u0442 \u0436\u0430\u0441\u044b\u043b",
"Medium Blue": "\u041e\u0440\u0442\u0430\u0448\u0430 \u043a\u04e9\u043a",
"Medium Purple": "\u041e\u0440\u0442\u0430\u0448\u0430 \u043a\u04af\u043b\u0433\u0456\u043d",
"Midnight Blue": "\u0422\u04af\u043d\u0433\u0456 \u043a\u04e9\u043a",
"Yellow": "\u0421\u0430\u0440\u044b",
"Orange": "\u0421\u0430\u0440\u0493\u044b\u0448",
"Red": "\u049a\u044b\u0437\u044b\u043b",
"Light Gray": "\u0410\u0448\u044b\u049b \u0441\u04b1\u0440",
"Gray": "\u0421\u04b1\u0440",
"Dark Yellow": "\u041a\u04af\u04a3\u0433\u0456\u0440\u0442 \u0441\u0430\u0440\u044b",
"Dark Orange": "\u041a\u04af\u04a3\u0433\u0456\u0440\u0442 \u0441\u0430\u0440\u0493\u044b\u0448",
"Dark Red": "\u041a\u04af\u04a3\u0433\u0456\u0440\u0442 \u049b\u044b\u0437\u044b\u043b",
"Medium Gray": "\u041e\u0440\u0442\u0430\u0448\u0430 \u0441\u04b1\u0440",
"Dark Gray": "\u041a\u04af\u04a3\u0433\u0456\u0440\u0442 \u0441\u04b1\u0440",
"Black": "\u049a\u0430\u0440\u0430",
"White": "\u0410\u049b",
"Switch to or from fullscreen mode": "\u0422\u043e\u043b\u044b\u049b \u044d\u043a\u0440\u0430\u043d \u0440\u0435\u0436\u0438\u043c\u0456\u043d\u0435 \u043d\u0435\u043c\u0435\u0441\u0435 \u043e\u0434\u0430\u043d \u0430\u0443\u044b\u0441\u0443",
"Open help dialog": "\u0410\u043d\u044b\u049b\u0442\u0430\u043c\u0430 \u0434\u0438\u0430\u043b\u043e\u0433\u0442\u044b\u049b \u0442\u0435\u0440\u0435\u0437\u0435\u0441\u0456\u043d \u0430\u0448\u0443",
"history": "\u0442\u0430\u0440\u0438\u0445",
"styles": "\u0441\u0442\u0438\u043b\u044c\u0434\u0435\u0440",
"formatting": "\u043f\u0456\u0448\u0456\u043c\u0434\u0435\u0443",
"alignment": "\u0442\u0443\u0440\u0430\u043b\u0430\u0443",
"indentation": "\u0448\u0435\u0433\u0456\u043d\u0456\u0441",
"permanent pen": "\u043f\u0435\u0440\u043c\u0430\u043d\u0435\u043d\u0442\u0442\u0456\u043a \u043c\u0430\u0440\u043a\u0435\u0440",
"comments": "\u0442\u04af\u0441\u0456\u043d\u0434\u0456\u0440\u043c\u0435\u043b\u0435\u0440",
"Anchor": "\u0411\u0435\u0442\u0431\u0435\u043b\u0433\u0456",
"Special character": "\u0410\u0440\u043d\u0430\u0439\u044b \u0442\u0430\u04a3\u0431\u0430",
"Color": "\u0422\u04af\u0441",
"Emoticons": "\u0421\u043c\u0430\u0439\u043b\u0438\u043a\u0442\u0430\u0440",
"Document properties": "\u049a\u04b1\u0436\u0430\u0442 \u0441\u0438\u043f\u0430\u0442\u0442\u0430\u0440\u044b",
"Image": "\u0421\u0443\u0440\u0435\u0442",
"Insert link": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443",
"Link": "\u0421\u0456\u043b\u0442\u0435\u043c\u0435",
"Target": "\u0410\u0448\u044b\u043b\u0430\u0442\u044b\u043d \u0436\u0435\u0440\u0456",
"Media": "\u041c\u0435\u0434\u0438\u0430",
"Poster": "\u041f\u043e\u0441\u0442\u0435\u0440\u0456",
"Print": "\u0411\u0430\u0441\u044b\u043f \u0448\u044b\u0493\u0430\u0440\u0443",
"Whole words": "\u0422\u04b1\u0442\u0430\u0441 \u0441\u04e9\u0437\u0434\u0435\u0440",
"Find and replace": "\u0422\u0430\u0431\u0443 \u0436\u04d9\u043d\u0435 \u0430\u0443\u044b\u0441\u0442\u044b\u0440\u0443",
"Prev": "\u0410\u043b\u0434\u044b\u04a3\u0493\u044b",
"Spellcheck": "\u0415\u043c\u043b\u0435 \u0442\u0435\u043a\u0441\u0435\u0440\u0443",
"Caption": "\u0410\u0442\u0430\u0443\u044b",
"Insert template": "\u04ae\u043b\u0433\u0456 \u043a\u0456\u0440\u0456\u0441\u0442\u0456\u0440\u0443"
});