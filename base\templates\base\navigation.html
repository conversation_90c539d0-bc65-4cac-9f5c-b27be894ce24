{% load i18n %}

<nav class="bg-color px-4 py-2.5 dark:bg-gray-800 dark:border-gray-700 fixed left-0 right-0 top-0 z-40">
    <div class="flex flex-wrap justify-between items-center">
      <div class="flex justify-start items-center">
        <button id="menuBtn"
          class="p-2 mr-2 text-gray-600 rounded-lg md:ml-64 cursor-pointer hover:text-gray-900 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
        >
          <svg
            aria-hidden="true"
            class="w-6 h-6"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
              clip-rule="evenodd"
            ></path>
          </svg>
          <svg
            aria-hidden="true"
            class="hidden w-6 h-6"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            ></path>
          </svg>
          <span class="sr-only">Toggle sidebar</span>
        </button>
      </div>
      <div class="flex items-center lg:order-2">
        <button type="button" id="user-menu-button" data-dropdown-toggle="dropdownNavbar" aria-expanded="false" style="font-size: large;"
          class="flex items-center font-medium justify-between w-full py-2 px-3 text-gray-600 rounded-lg hover:text-gray-900 md:hover:bg-transparent md:border-0 md:hover:border-blue-700 md:p-0 md:w-auto dark:text-white md:dark:hover:border-blue-500 dark:focus:text-white dark:border-gray-700 dark:hover:bg-gray-700 md:dark:hover:bg-transparent">
          {{ logged_in_user_data.user_full_name }} 
          <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
          </svg>
        </button>
        <!-- Dropdown menu -->
        <div id="dropdownNavbar" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow-lg w-44 dark:bg-gray-700">
          <ul class="py-2 text-sm shadow-lg text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
          <li>
            <!-- <form method="post"> -->
              {% csrf_token %}
                <!-- <div class="pt-1"> -->
                  <span onclick="signout('{{logged_in_user_data.user_id}}')" class="block cursor-pointer pr-0 pt-1 pb-1 pl-3 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">{% trans 'Sign out' %}</span>
                <!-- </div> -->
            <!-- </form> -->
          </li>
          </ul>
      </div>
    </div>

  </nav>