#!/usr/bin/env python3
"""
对比Excel文件和数据库中的家庭组
"""

from hku_local_config import HKU_LOCAL_CONFIG
import psycopg2
import pandas as pd

def compare_family_groups():
    """对比Excel和数据库中的家庭组"""
    
    try:
        # 1. 读取Excel文件中的家庭组
        print("📖 读取Excel文件中的家庭组...")
        excel_df = pd.read_csv('family_teams_randomization.csv')
        excel_families = set(excel_df['家庭聯盟名稱'].dropna().unique())
        
        print(f"📊 Excel文件统计:")
        print(f"   总家庭组数: {len(excel_families)}")
        print(f"   前10个家庭组: {list(excel_families)[:10]}")
        
        # 2. 查询数据库中的家庭组
        print(f"\n🔍 查询数据库中的家庭组...")
        conn = psycopg2.connect(**HKU_LOCAL_CONFIG)
        cursor = conn.cursor()
        
        # 查询所有家庭组
        cursor.execute("""
            SELECT
                fg.family_name,
                c.name as category_name,
                fg.created_on,
                COUNT(fm.member_id) as member_count
            FROM family_familygroups fg
            LEFT JOIN family_category c ON fg.category_id = c.id
            LEFT JOIN family_familymembers fm ON fg.id = fm.family_group_id
                AND fm.is_deleted = false AND fm.is_member = true
            GROUP BY fg.id, fg.family_name, c.name, fg.created_on
            ORDER BY fg.created_on
        """)
        
        db_families = cursor.fetchall()
        db_family_names = set(family[0] for family in db_families)
        
        print(f"📊 数据库统计:")
        print(f"   总家庭组数: {len(db_family_names)}")
        
        # 按类别统计
        food_families = set()
        sports_families = set()
        no_category_families = set()
        
        for family_name, category, created_on, member_count in db_families:
            if category == 'Food':
                food_families.add(family_name)
            elif category == 'Sports':
                sports_families.add(family_name)
            else:
                no_category_families.add(family_name)
        
        print(f"   Food类别: {len(food_families)} 个")
        print(f"   Sports类别: {len(sports_families)} 个")
        print(f"   无类别: {len(no_category_families)} 个")
        
        # 3. 对比分析
        print(f"\n🔍 对比分析:")
        
        # Excel中有但数据库中没有的
        excel_only = excel_families - db_family_names
        print(f"   Excel中有但数据库中没有: {len(excel_only)} 个")
        if excel_only:
            for family in sorted(excel_only)[:10]:
                print(f"     {family}")
            if len(excel_only) > 10:
                print(f"     ... 还有 {len(excel_only) - 10} 个")
        
        # 数据库中有但Excel中没有的
        db_only = db_family_names - excel_families
        print(f"   数据库中有但Excel中没有: {len(db_only)} 个")
        if db_only:
            for family in sorted(db_only)[:10]:
                print(f"     {family}")
            if len(db_only) > 10:
                print(f"     ... 还有 {len(db_only) - 10} 个")
        
        # 共同的家庭组
        common_families = excel_families & db_family_names
        print(f"   共同的家庭组: {len(common_families)} 个")
        
        # 4. 检查完整日期范围的数据
        print(f"\n📅 检查完整日期范围的quiz数据...")
        
        # 查询从3月4号到5月2号的数据
        cursor.execute("""
            SELECT
                MIN(uq.date) as min_date,
                MAX(uq.date) as max_date,
                COUNT(*) as total_records,
                COUNT(DISTINCT fg.family_name) as unique_families
            FROM question_userquestion uq
            JOIN question_question q ON uq.question_id = q.id
            JOIN auth_user au ON uq.user_id = au.id
            JOIN family_familymembers fm ON au.id = fm.member_id
            JOIN family_familygroups fg ON fm.family_group_id = fg.id
            WHERE uq.answered = true
            AND uq.is_deleted = false
            AND q.is_deleted = false
            AND fm.is_deleted = false
            AND fm.is_member = true
            AND uq.date BETWEEN '2025-03-04' AND '2025-05-02'
        """)
        
        full_range_data = cursor.fetchone()
        min_date, max_date, total_records, unique_families = full_range_data
        
        print(f"   完整范围 (2025-03-04 到 2025-05-02):")
        print(f"     实际数据范围: {min_date} 到 {max_date}")
        print(f"     总quiz记录: {total_records}")
        print(f"     参与家庭组: {unique_families}")
        
        # 5. 检查是否需要重新导出
        print(f"\n📋 结论:")
        
        if total_records > 4801:  # 比我们之前导出的多
            print(f"✅ 发现更多数据！需要使用完整日期范围重新导出")
            print(f"   之前导出: 4,801条记录")
            print(f"   完整范围: {total_records}条记录")
            print(f"   增加了: {total_records - 4801}条记录")
        else:
            print(f"ℹ️  完整日期范围的数据与之前导出的相同")
        
        if len(db_family_names) != 138:
            print(f"⚠️  数据库中有{len(db_family_names)}个家庭组，不是Excel中的137个")
            print(f"   这可能解释了为什么之前显示114个而不是138个")
        
        cursor.close()
        conn.close()
        
        return {
            'excel_families': len(excel_families),
            'db_families': len(db_family_names),
            'common_families': len(common_families),
            'total_records': total_records,
            'unique_families': unique_families
        }
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    compare_family_groups()
