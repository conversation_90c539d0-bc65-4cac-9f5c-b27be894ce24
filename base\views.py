from django.shortcuts import render, redirect
from base.models import ExceptionLogs
# Create your views here.
from django.views import View
from base import utils
from rest_framework.views import APIView
from rest_framework.response import Response
import traceback
from rest_framework import status
from family.models import FamilyMembers, FamilyGroups, FamilyGroupFile, WeeklyUploadFile
from django.contrib.auth.models import User
from user_authentication.models import UserProfile
from base import utils
from datetime import datetime
from base.serializers import HomeDetailsSerializer
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from base import storage_backends
from question.models import Question, UserQuestion, QuizStep, Leaderboard, UserSteps, QuizDates
from user_authentication.models import UserCheckin
from django.db.models import Sum, Count, Q
from decouple import config
from family.models import Category
from .utils import custom_data_response, custom_error_response

def index(request):
    if "backend_user_id" not in request.session:
        return redirect("signin")
    
    logged_in_user_data = utils.get_loggedin_user_details(request)
    return redirect("users")
    # return render(request, 'base/base.html', {"logged_in_user_data": logged_in_user_data})


def create_from_exceptions(user_id, error_msg, traceback):
    try:
        ExceptionLogs.objects.create(
            error_type=type(error_msg),
            error_msg=str(error_msg),
            traceback=str(traceback),
            user_id=user_id,
            )
    except Exception as e:
        print("error", e)
        

class HomeDetails(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]

    action_serializers = {
        "home_details": HomeDetailsSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)
    
    @action(methods=['post'], detail=False, url_path='home-details')
    def home_details(self, request):
        try:
            user_id = request.data.get("user_id")
            user_exist = UserProfile.objects.filter(user_id=user_id, is_deleted=False).values("user__first_name", "user__last_name").first()
            if not user_exist:
                return Response({
                    "success": False,
                    "message": "User does not exist."
                }, status=status.HTTP_400_BAD_REQUEST)

            family_group = FamilyMembers.objects.filter(member_id=user_id).values("family_group_id", "family_group__is_quiz_starts").first()
            if not family_group:
                return Response({"success": False, "message": "User does not join any family group."}, status=status.HTTP_400_BAD_REQUEST)
            
            is_family_group_selfie = False
            if datetime.now().weekday() == int(config("WEEK_DAY")):
                if WeeklyUploadFile.objects.filter(family_group_id=family_group["family_group_id"], created_on__date=datetime.now().date()).exists():
                    is_family_group_selfie = True
            
            family_members = FamilyMembers.objects.filter(family_group_id=family_group["family_group_id"], is_deleted=False).values(
                            "id",
                            "member_id",
                            "member__first_name",
                            "member__last_name",
                            "member__email",
                            "family_group_id",
                            "family_group__family_name",
                            "family_group__category__name",
                            "family_group__is_quiz_starts",
                            "family_group__icon_url",
                            ).order_by("-id")
            
            user_data = {}
            is_quiz_start = False
            user_id_pending_que_count = {}
            if family_members:
                # is_quiz_start = True if family_group and family_group["family_group__is_quiz_starts"] else False
                member_ids = list(family_members.values_list("member_id", flat=True))
                is_quiz_start = True if UserQuestion.objects.filter(user_id__in=member_ids).exists() else False
                users = UserProfile.objects.filter(user_id__in=member_ids, is_deleted=False).values("user_id", "country_code", "phone_number", "role", "age_group")
                for user in users:
                    user_data[user["user_id"]] = {"country_code": user["country_code"], "phone_number": user["phone_number"], "role": user["role"], "age_group": user["age_group"]}
                    
                user_questions = UserQuestion.active_objects.filter(user_id__in=member_ids, date=datetime.now().date(), answered=False, question__quiz_type__in=["regular", "Regular"]).values('user_id').annotate(user_question_count=Count('user_id'))
                for user_que in user_questions:
                    user_id_pending_que_count[user_que["user_id"]] = user_que["user_question_count"]
            
            # family_group_file = FamilyGroupFile.objects.filter(family_group_id=family_group["family_group_id"]).first()

            is_daily_quiz_completed = False
            is_bonus_quiz_completed = False
            is_step_uploaded = False
            questions = UserQuestion.active_objects.filter(
                (Q( question__quiz_type__in = ["regular", "Regular"]) | Q( question__quiz_type__in = ["bonus", "Bonus"])), date = datetime.now().date(), user=request.user)
            if questions:
                regular_questions = questions.filter(question__quiz_type__in = ["regular", "Regular"], answered=False).count()
                bonus_questions= questions.filter(question__quiz_type__in = ["bonus", "Bonus"], answered=False).count()
                
                if regular_questions == 0:
                    is_daily_quiz_completed = True
                if bonus_questions == 0:
                    is_bonus_quiz_completed = True
            if QuizStep.objects.filter(created_at = datetime.now().date(), user_id = user_id).exists():
                is_step_uploaded = True
            is_check_in = False
            if UserCheckin.objects.filter(user__pk = user_id ,created_on__date = datetime.now().date()).exists():
                is_check_in = True
            current_date = datetime.now().date()
            week_start_date = QuizStep.get_most_recent_monday(current_date)
            quiz_steps = None
            user_steps = None
            steps = None
            if QuizStep.objects.filter(user = self.request.user, week_start_date = week_start_date).exists():
                quiz_steps = QuizStep.objects.get(user = self.request.user, week_start_date = week_start_date)
                if UserSteps.objects.filter(user = self.request.user, date = current_date).exists():
                    user_steps = UserSteps.objects.get(user = self.request.user, date = current_date)
                    if user_steps in quiz_steps.steps.all():
                        steps = user_steps.steps
            try:
                dates = QuizDates.objects.first()
                if not dates:
                    return custom_error_response(message="Quiz dates are not configured.")
            except QuizDates.DoesNotExist:
                return custom_error_response(message="Quiz dates are not configured.")
            start_date = False
            end_date = False
            is_quiz_finished = False
            if datetime.now().date() == dates.start_date:
                start_date = True
            if datetime.now().date() == dates.end_date:
                end_date=True
            if datetime.now().date() > dates.end_date:
                is_quiz_finished = True
            
            response = {
                    "family": {},
                    "is_daily_quiz_completed": is_daily_quiz_completed,
                    "is_bonus_quiz_completed": is_bonus_quiz_completed,
                    "is_sunday": True if datetime.now().weekday() == int(config("WEEK_DAY")) else False,
                    "is_cancel_family_member": False if is_quiz_start else True,
                    # "is_cancel_family_member": True,
                    "is_family_group_selfie": is_family_group_selfie,
                    # "user": {}
                    "is_check_in": is_check_in,
                    "daily_steps" : steps if steps else None,
                    "is_starting_day": start_date,
                    "is_ending_day": end_date,
                    "is_quiz_finished": is_quiz_finished,
                }
            response['is_step_uploaded'] = False
            if quiz_steps:
                response['is_step_uploaded'] = True if quiz_steps.image else False
            
            family_data = {
                "id": family_members[0]["family_group_id"] if family_members else None,
                "name": family_members[0]["family_group__family_name"] if family_members else None,
                "category": family_members[0]["family_group__category__name"] if family_members else None,
                "member_details": []
            }
            family_data['profile_pic'] = None
            # if family_group_file and family_group_file.file:
                # family_data['profile_pic'] = family_group_file.file.url
            family_data['profile_pic'] = family_members[0]["family_group__icon_url"] if family_members and family_members[0] else ""
            
            category = Category.objects.get(name = family_members[0]["family_group__category__name"])
            leaderboards = Leaderboard.objects.filter(category = category).annotate(
                total_leaderboard_score=Sum('user_leaderboard__score'),
            ).order_by('-score')
            
            family_group_leaderboard = Leaderboard.objects.filter(family_group_id=family_members[0]["family_group_id"], category = category).first()
            if family_group_leaderboard:
                leaderboard_ids = list(leaderboards.values_list('id', flat=True).order_by("-score"))
                family_group_id = family_group_leaderboard.id
                if family_group_id in leaderboard_ids:
                    position = leaderboard_ids.index(family_group_id) + 1
                    family_data["leaderboard_position"] = position
                    family_data["score"] = family_group_leaderboard.score
                else:
                    family_data["leaderboard_position"] = "Not Ranked"
            else:
                family_data["leaderboard_position"] = None
           
            for family_member in family_members:
                is_quiz_completed = True
                if family_member["member_id"] in user_id_pending_que_count and user_id_pending_que_count[family_member["member_id"]] > 0:
                    is_quiz_completed = False

                # full_name = utils.get_user_full_name(family_member["member__first_name"], family_member["member__last_name"])
                family_data["member_details"].append({
                    "id": family_member["member_id"],
                    # "name": full_name,
                    "first_name": family_member["member__first_name"],
                    "last_name": family_member["member__last_name"],
                    "email":family_member['member__email'],
                    "role": user_data[family_member["member_id"]]["role"] if family_member["member_id"] in user_data and user_data[family_member["member_id"]]["role"] else "",
                    "age_group": user_data[family_member["member_id"]]["age_group"] if family_member["member_id"] in user_data and user_data[family_member["member_id"]]["age_group"] else "",
                    "country_code": user_data[family_member["member_id"]]["country_code"] if family_member["member_id"] in user_data and user_data[family_member["member_id"]]["country_code"] else "",
                    "phone_number": user_data[family_member["member_id"]]["phone_number"] if family_member["member_id"] in user_data and user_data[family_member["member_id"]]["phone_number"] else "",
                    "score": "",
                    "steps": "",
                    "is_quiz_completed": is_quiz_completed,
                    "is_logged_in_user": True if int(family_member["member_id"]) == int(user_id) else False,
                })           

            family_data["member_details"] = sorted(family_data["member_details"], key=lambda x: x['is_logged_in_user'], reverse=True)
            response["family"] = family_data
            response["weekly_family_photo_score"] = settings.WEEKLY_FAMILY_PHOTO_SCORE
            response["user_daily_checkin_score"] = settings.USER_DAILY_CHECKIN_SCORE
            # response["data"]["user"] = user_profile_data
            
            return Response({
                'success': True,
                'message': 'Fetch home details successfully.', "data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            