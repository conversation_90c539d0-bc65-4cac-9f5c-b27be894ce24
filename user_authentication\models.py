from django.db import models
from django.contrib.auth.models import User

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    country_code = models.CharField(max_length=10,blank=True,null=True)
    phone_number = models.CharField(max_length=19, blank=True, null=True)
    role = models.CharField(max_length=100, null=True, blank=True)
    age_group = models.CharField(max_length=100, null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    is_verify = models.BooleanField(default=False)
    email = models.EmailField(max_length=50, null=True, blank=True)
    is_pwd_change = models.BooleanField(default=False)
    school = models.CharField(max_length=128, null=True, blank=True)
    grade = models.Char<PERSON>ield(max_length=128, null=True, blank=True)
    checkin_steps_score = models.IntegerField(default=0)
    family_group = models.Cha<PERSON><PERSON><PERSON>(max_length=128, null=True, blank=True)
    class_number = models.Char<PERSON><PERSON>(max_length=128, null=True, blank=True)
    who_is_leader = models.Char<PERSON><PERSON>(max_length=128, null=True, blank=True)

    def __str__(self):
        return self.user.username
   
class PhoneNumOtp(models.Model):
    country_code = models.CharField(max_length=10,blank=True,null=True)
    phone_number = models.CharField(max_length=19, blank=True, null=True)
    email = models.EmailField(max_length=50, null=True, blank=True)
    otp = models.CharField(max_length=4,blank=True, null=True)
    otp_expiry = models.DateTimeField(blank=True, null=True)
    is_verify = models.BooleanField(default=False)

class UserCheckin(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.created_on}"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            