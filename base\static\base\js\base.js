function signout(user_id) {
    var formData = new FormData();
    formData.append('user_id', user_id);
    formData.append('csrfmiddlewaretoken', $("input[name=csrfmiddlewaretoken]").val());
    axios({
        method: "post",
        url: "/signout/",
        data: formData,
        headers: { "Content-Type": "multipart/form-data" },
    })
    .then(function (response) {
        if (response.data.code == 0) {
            showMessage('appMsg', 'danger', response.data.msg, 10);
        } else {
            window.location.href = "/signin/";
        }
    })
    .catch(function (response) {
        showMessage('appMsg', 'danger', 'Something went wrong!', 5);
    });
}

function showMessage(id, type, msg, time) {
    var time_in_sec = 10;
    if (time) {
        time_in_sec = time;
    }
    // $('#' + id)
    //     .text(msg)
    //     .removeClass();

    if (type == 'info') {
        $('#' + id)
            .html(`<div class="p-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 flex justify-between shadow-lg border-l-blue-800 border-l-4" role="alert">
                <span class="font-medium">` + msg + `</span>
                <span onclick="closeMsg('` + id + `')">
                    <svg class="fill-current h-6 w-6 text-blue-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </span>
                </div>`)
            .show();
    } else if (type == 'danger') {
        $('#' + id)
            .html(`<div class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
                <span class="font-medium">` + msg + `</span>
                <span onclick="closeMsg('` + id + `')">
                    <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </span>
                </div>`)
            .show();
    } else if (type == 'success') {
        $('#' + id)
            .html(`<div class="p-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 flex justify-between shadow-lg border-l-green-800 border-l-4" role="alert">
                <span class="font-medium">` + msg + `</span>
                <span onclick="closeMsg('` + id + `')">
                    <svg class="fill-current h-6 w-6 text-green-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </span>
                </div>`)
            .show();
    } else if (type == 'warning') {
        $('#' + id)
            .html(`<div class="p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 flex justify-between shadow-lg border-l-yellow-800 border-l-4" role="alert">
                <span class="font-medium">` + msg + `</span>
                <span onclick="closeMsg('` + id + `')">
                    <svg class="fill-current h-6 w-6 text-yellow-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </span>
                </div>`)
            .show();
    } else {
        $('#' + id)
            .html(`<div class="p-4 text-sm text-gray-800 rounded-lg bg-gray-50 dark:bg-gray-800 dark:text-gray-300 flex justify-between shadow-lg border-l-gray-800 border-l-4" role="alert">
                <span class="font-medium">` + msg + `</span>
                <span onclick="closeMsg('` + id + `')">
                    <svg class="fill-current h-6 w-6 text-gray-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </span>
                </div>`)
            .show();
    }

    setTimeout(function () {
        $('#' + id).hide();
        closeMsg();
    }, time_in_sec * 1000);
};

function isValidInput(event) {
    var value = event.target.value ? event.target.value : $("#" + event.srcElement.id).val();
    // Get the character code of the pressed key
    var charCode = (event.which) ? event.which : event.keyCode;
    // Get the index of the dot in the current value
    var dotIndex = value.indexOf('.');

    // Allow digits (0-9), dot (.) if it doesn't exist already.
    if ((charCode >= 48 && charCode <= 57) || (charCode == 46 && dotIndex !== -1)) {
        return true;
    }

    // Prevent any other characters
    return false;
}

function handleTextPaste(event) {
    setTimeout(() => {
        let pastedText = event.target.value;
        if (!/^-?\d*\.?\d*$/.test(pastedText)) {
            event.target.value = '';
        }
    }, 0);
}

function handleIntTextPaste(event) {
  setTimeout(() => {
      const inputValue = event.target.value;
      if (/[^0-9]/.test(inputValue)) {
          event.target.value = '';
      }
  }, 0);
}

function showConfirmationDialogue(msg, callback) {
    $('#modalMsg').html(msg);
    $('#confirmDialogueButton').click();

    $('#confirmBtnClick').off('click').on('click', function () {
        if (callback) callback(true);
    });
    
    $('#cancelBtnClick').off('click').on('click', function () {
        if (callback) callback(false);
    });
}

function closeMsg(id) {
    $('#' + id).hide();
    $('#appMsg').hide();
}

function openStaticModal(body, title, confirm_btn_txt, processing_btn_txt, info_msg, callback) {
    $('#modalTitle').text(title);
    $('#modalConfirmBtn').text(confirm_btn_txt);
    $('#modalProcessingBtn').text(processing_btn_txt);
    $('#modalBody').html(body);
    $('#infoMsg').html(info_msg);
    $('#staticModal').click();
    
    $('#modalConfirmBtn').off('click').on('click', function () {
        if (callback) callback(true);
    });
    
    $('#modalCloseBtn').off('click').on('click', function () {
        if (callback) callback(false);
    });
}

$('.menu-link[href="' + window.location.pathname + '"]').removeClass('bg-gray-200 text-blue-500').addClass('bg-blue-200 shadow-lg');
// $('.menu-link[href="' + window.location.pathname + '"]').removeClass('text-gray-900').addClass('bg-gray-200 text-blue-500 shadow-lg');
// $('.menu-link[href="' + window.location.pathname + '"]').find('svg').removeClass('text-gray-800').addClass('text-blue-500');

function scrollToTop() {
  window.scrollTo({
      top: 0,
      behavior: 'smooth'
  });
}