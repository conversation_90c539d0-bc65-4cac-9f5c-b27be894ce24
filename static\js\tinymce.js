tinymce.init({
    selector: 'textarea#id_prize_info',
    plugins: 'advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste code help wordcount',
    valid_elements: 'h1,h2,h3,h4,h5,h6,p,table,thead,tbody,tr,th,td', // Allow only h2 and p tags
    menubar: false, // Hide the menu bar
    toolbar: 'styleselect |  link image media | table',
    image_uploadtab: true, // Enable the "Upload" tab in the image dialog
    images_upload_url: '/your-upload-endpoint', // Replace with the URL where you want to handle image uploads
    imagetools_toolbar: "rotateleft rotateright | flipv fliph | editimage imageoptions",
    file_picker_types: 'image', // Only allow image files to be selected
    file_picker_callback: function (cb, value, meta) {
      var input = document.createElement('input');
      input.setAttribute('type', 'file');
      input.setAttribute('accept', 'image/*');
      input.onchange = function () {
        var file = this.files[0];
        var reader = new FileReader();
        reader.onload = function () {
          var imgDataUri = reader.result;
          cb(imgDataUri, { title: file.name });
        };
        reader.readAsDataURL(file);
      };
      input.click();
    },
    // other configurations...
});





// tinymce.init({
//     selector: 'textarea', // Replace 'textarea' with the ID or class of your textarea element
//     //language: 'zh_CN',
//     plugins: 'advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste code help wordcount',
//     toolbar: 'undo redo | formatselect | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | outdent indent |  numlist bullist | forecolor backcolor removeformat | | subscript superscript | link image media | charmap emoticons | preview code | table | searchreplace fullscreen | insertdatetime | page_size',
//     height: 600, // Set the height of the editor as per your requirement
//     page_size_units: 'mm',
//     page_size_width: 210, // A4 width in millimeters
//     page_size_height: 297, // A4 height in millimeters
//     menubar: true, // Show the menubar with all options
//     content_style: 'body { font-family: Arial, SimSun, PMingLiU, sans-serif; font-size: 14px; }', // Define the default content style
//     image_uploadtab: true, // Enable the "Upload" tab in the image dialog
//     //images_upload_url: 'admin/edit-contract/<int:id>/', // Replace with the URL where you want to handle image uploads
//     imagetools_toolbar: "rotateleft rotateright | flipv fliph | editimage imageoptions",
//     file_picker_types: 'image', // Only allow image files to be selected
//     file_picker_callback: function (cb, value, meta) {
//       var input = document.createElement('input');
//       input.setAttribute('type', 'file');
//       input.setAttribute('accept', 'image/*');
//       input.onchange = function () {
//         var file = this.files[0];
//         var reader = new FileReader();
//         reader.onload = function () {
//           var imgDataUri = reader.result;
//           cb(imgDataUri, { title: file.name });
//         };
//         reader.readAsDataURL(file);
//       };
//       input.click();
//     },
//   });