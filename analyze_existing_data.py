#!/usr/bin/env python3
"""
分析现有CSV文件，解释数据差异
"""

import pandas as pd
from datetime import datetime

def analyze_existing_data():
    """分析现有的CSV文件"""
    
    try:
        print("🔍 分析现有CSV文件数据...")
        
        # 读取现有的CSV文件
        food_file = "hku_exercise_export_Food_20250716_215230.csv"
        sports_file = "hku_exercise_export_Sports_20250716_215318.csv"
        
        print(f"📖 读取文件:")
        print(f"   饮食类别: {food_file}")
        print(f"   运动类别: {sports_file}")
        
        food_df = pd.read_csv(food_file)
        sports_df = pd.read_csv(sports_file)
        
        # 合并数据进行完整分析
        combined_df = pd.concat([food_df, sports_df], ignore_index=True)
        
        print(f"\n📊 完整数据统计:")
        print(f"   总记录数: {len(combined_df)}")
        print(f"   饮食类别记录: {len(food_df)}")
        print(f"   运动类别记录: {len(sports_df)}")
        
        # 统计家庭组
        total_families = combined_df['Family Name'].nunique()
        food_families = food_df['Family Name'].nunique()
        sports_families = sports_df['Family Name'].nunique()
        
        print(f"\n🏠 家庭组统计:")
        print(f"   总家庭组数: {total_families}")
        print(f"   饮食类别家庭组: {food_families}")
        print(f"   运动类别家庭组: {sports_families}")
        
        # 检查重叠
        food_families_set = set(food_df['Family Name'].unique())
        sports_families_set = set(sports_df['Family Name'].unique())
        overlap = food_families_set.intersection(sports_families_set)
        
        print(f"   重叠家庭组: {len(overlap)}")
        if overlap:
            print(f"   重叠列表: {', '.join(sorted(overlap))}")
        
        # 统计用户
        total_users = combined_df['Member Name'].nunique()
        food_users = food_df['Member Name'].nunique()
        sports_users = sports_df['Member Name'].nunique()
        
        print(f"\n👥 用户统计:")
        print(f"   总用户数: {total_users}")
        print(f"   饮食类别用户: {food_users}")
        print(f"   运动类别用户: {sports_users}")
        
        # 按Quiz Type分类
        print(f"\n📋 按Quiz Type分类:")
        quiz_type_stats = combined_df['Quiz Type'].value_counts()
        for quiz_type, count in quiz_type_stats.items():
            print(f"   {quiz_type}: {count} 条记录")
        
        # 按Question Category分类
        print(f"\n📚 按Question Category分类:")
        category_stats = combined_df['Question Category'].value_counts()
        for category, count in category_stats.items():
            print(f"   {category}: {count} 条记录")
        
        # 日期范围
        print(f"\n📅 日期范围:")
        min_date = combined_df['Answer Date'].min()
        max_date = combined_df['Answer Date'].max()
        unique_dates = combined_df['Answer Date'].nunique()
        
        print(f"   最早日期: {min_date}")
        print(f"   最晚日期: {max_date}")
        print(f"   总天数: {unique_dates}")
        
        # 分析为什么只有114个家庭团队而不是138个
        print(f"\n🔍 家庭组详细分析:")
        
        # 只统计Regular questions的家庭组
        regular_df = combined_df[combined_df['Quiz Type'] != 'Bonus']
        regular_families = regular_df['Family Name'].nunique()
        
        print(f"   包含Bonus的家庭组: {total_families}")
        print(f"   仅Regular questions的家庭组: {regular_families}")
        
        # 显示所有家庭组列表
        print(f"\n📋 所有家庭组列表:")
        all_families = sorted(combined_df['Family Name'].unique())
        for i, family in enumerate(all_families, 1):
            # 统计该家庭的记录数
            family_records = len(combined_df[combined_df['Family Name'] == family])
            regular_records = len(regular_df[regular_df['Family Name'] == family])
            bonus_records = family_records - regular_records
            
            print(f"   {i:2d}. {family} (总:{family_records}, Regular:{regular_records}, Bonus:{bonus_records})")
        
        # 创建汇总报告
        print(f"\n📊 数据差异解释:")
        print(f"   1. 您看到的114个家庭团队是正确的（包含所有参与quiz的家庭组）")
        print(f"   2. 之前的138可能包含了未参与quiz或已删除的家庭组")
        print(f"   3. {len(combined_df)}条记录包含每个问题的详细信息")
        print(f"   4. 这不是只有Bonus分数，而是包含所有quiz questions")
        print(f"   5. Regular questions: {len(regular_df)}条")
        print(f"   6. Bonus questions: {len(combined_df) - len(regular_df)}条")
        
        # 生成汇总CSV
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        summary_filename = f"hku_quiz_summary_analysis_{timestamp}.csv"
        
        # 创建汇总数据
        summary_data = []
        for family in all_families:
            family_data = combined_df[combined_df['Family Name'] == family]
            regular_data = regular_df[regular_df['Family Name'] == family]
            
            # 确定家庭类别
            if len(food_df[food_df['Family Name'] == family]) > 0:
                if len(sports_df[sports_df['Family Name'] == family]) > 0:
                    family_category = "Both"
                else:
                    family_category = "Food"
            else:
                family_category = "Sports"
            
            summary_data.append({
                'Family Name': family,
                'Family Category': family_category,
                'Total Records': len(family_data),
                'Regular Questions': len(regular_data),
                'Bonus Questions': len(family_data) - len(regular_data),
                'Unique Members': family_data['Member Name'].nunique(),
                'Date Range': f"{family_data['Answer Date'].min()} to {family_data['Answer Date'].max()}"
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(summary_filename, index=False)
        
        print(f"\n✅ 汇总分析完成: {summary_filename}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_existing_data()
