tinymce.addI18n('es_419',{
"Redo": "<PERSON><PERSON><PERSON>",
"Undo": "<PERSON><PERSON><PERSON>",
"Cut": "<PERSON><PERSON><PERSON>",
"Copy": "<PERSON><PERSON><PERSON>",
"Paste": "<PERSON>eg<PERSON>",
"Select all": "Seleccionar todo",
"New document": "Nuevo documento",
"Ok": "Ok",
"Cancel": "Cancelar",
"Visual aids": "Ayudas visuales",
"Bold": "Negrita",
"Italic": "Cursiva",
"Underline": "Subrayado",
"Strikethrough": "Tachado",
"Superscript": "Super\u00edndice",
"Subscript": "Sub\u00edndice",
"Clear formatting": "Limpiar formato",
"Align left": "Alinear a la izquierda",
"Align center": "Centrar",
"Align right": "Alinear a la derecha",
"Justify": "Justificar",
"Bullet list": "Lista de vi\u00f1etas",
"Numbered list": "Lista numerada",
"Decrease indent": "<PERSON><PERSON><PERSON><PERSON><PERSON> sangr\u00eda",
"Increase indent": "Aumentar sangr\u00eda",
"Close": "Cerrar",
"Formats": "Formatos",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Tu navegador no soporta acceso directo al portapapeles. Favor usar los comandos de teclado Ctrl+X\/C\/V",
"Headers": "Encabezados",
"Header 1": "Encabezado 1",
"Header 2": "Encabezado 2",
"Header 3": "Encabezado 3",
"Header 4": "Encabezado 4",
"Header 5": "Encabezado 5",
"Header 6": "Encabezado 6",
"Headings": "T\u00edtulos",
"Heading 1": "T\u00edtulo 1",
"Heading 2": "T\u00edtulo 2",
"Heading 3": "T\u00edtulo 3",
"Heading 4": "T\u00edtulo 4",
"Heading 5": "T\u00edtulo 5",
"Heading 6": "T\u00edtulo 6",
"Preformatted": "Preformateado",
"Div": "Div",
"Pre": "Pre",
"Code": "C\u00f3digo",
"Paragraph": "P\u00e1rrafo",
"Blockquote": "Cita",
"Inline": "En l\u00ednea",
"Blocks": "Bloques",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.",
"Fonts": "Fonts",
"Font Sizes": "Tama\u00f1os de Fuente",
"Class": "Class",
"Browse for an image": "Examinar imagen",
"OR": "O",
"Drop an image here": "Arrastrar imagen aqu\u00ed",
"Upload": "Subir",
"Block": "Bloque",
"Align": "Alinear",
"Default": "Default",
"Circle": "Circle",
"Disc": "Disc",
"Square": "Square",
"Lower Alpha": "Lower Alpha",
"Lower Greek": "Lower Greek",
"Lower Roman": "Lower Roman",
"Upper Alpha": "Upper Alpha",
"Upper Roman": "Upper Roman",
"Anchor...": "Anchor...",
"Name": "Name",
"Id": "Id",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.",
"You have unsaved changes are you sure you want to navigate away?": "You have unsaved changes are you sure you want to navigate away?",
"Restore last draft": "Restore last draft",
"Special character...": "Special character...",
"Source code": "Source code",
"Insert\/Edit code sample": "Insert\/Edit code sample",
"Language": "Language",
"Code sample...": "Code sample...",
"Color Picker": "Color Picker",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "Left to right",
"Right to left": "Right to left",
"Emoticons": "Emoticons",
"Emoticons...": "Emoticons...",
"Metadata and Document Properties": "Metadata and Document Properties",
"Title": "Title",
"Keywords": "Keywords",
"Description": "Description",
"Robots": "Robots",
"Author": "Author",
"Encoding": "Encoding",
"Fullscreen": "Fullscreen",
"Action": "Action",
"Shortcut": "Shortcut",
"Help": "Help",
"Address": "Address",
"Focus to menubar": "Focus to menubar",
"Focus to toolbar": "Focus to toolbar",
"Focus to element path": "Focus to element path",
"Focus to contextual toolbar": "Focus to contextual toolbar",
"Insert link (if link plugin activated)": "Insert link (if link plugin activated)",
"Save (if save plugin activated)": "Save (if save plugin activated)",
"Find (if searchreplace plugin activated)": "Find (if searchreplace plugin activated)",
"Plugins installed ({0}):": "Plugins installed ({0}):",
"Premium plugins:": "Premium plugins:",
"Learn more...": "Learn more...",
"You are using {0}": "You are using {0}",
"Plugins": "Plugins",
"Handy Shortcuts": "Handy Shortcuts",
"Horizontal line": "Horizontal line",
"Insert\/edit image": "Insert\/edit image",
"Alternative description": "Descripci\u00f3n alternativa",
"Accessibility": "Accesibilidad",
"Image is decorative": "La imagen es decorativa",
"Source": "Source",
"Dimensions": "Dimensions",
"Constrain proportions": "Constrain proportions",
"General": "General",
"Advanced": "Advanced",
"Style": "Style",
"Vertical space": "Vertical space",
"Horizontal space": "Horizontal space",
"Border": "Border",
"Insert image": "Insert image",
"Image...": "Image...",
"Image list": "Image list",
"Rotate counterclockwise": "Rotate counterclockwise",
"Rotate clockwise": "Rotate clockwise",
"Flip vertically": "Flip vertically",
"Flip horizontally": "Flip horizontally",
"Edit image": "Edit image",
"Image options": "Image options",
"Zoom in": "Zoom in",
"Zoom out": "Zoom out",
"Crop": "Crop",
"Resize": "Resize",
"Orientation": "Orientation",
"Brightness": "Brightness",
"Sharpen": "Sharpen",
"Contrast": "Contrast",
"Color levels": "Color levels",
"Gamma": "Gamma",
"Invert": "Invert",
"Apply": "Apply",
"Back": "Back",
"Insert date\/time": "Insert date\/time",
"Date\/time": "Date\/time",
"Insert\/edit link": "Insert\/edit link",
"Text to display": "Text to display",
"Url": "Url",
"Open link in...": "Open link in...",
"Current window": "Current window",
"None": "None",
"New window": "New window",
"Open link": "Enlace abierto",
"Remove link": "Remove link",
"Anchors": "Anchors",
"Link...": "Link...",
"Paste or type a link": "Paste or type a link",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?",
"The URL you entered seems to be an external link. Do you want to add the required https:\/\/ prefix?": "La URL que ingres\u00f3 parece ser un enlace externo. \u00bfDesea agregar el prefijo https:\/\/ requerido?",
"Link list": "Link list",
"Insert video": "Insert video",
"Insert\/edit video": "Insert\/edit video",
"Insert\/edit media": "Insert\/edit media",
"Alternative source": "Alternative source",
"Alternative source URL": "Alternative source URL",
"Media poster (Image URL)": "Media poster (Image URL)",
"Paste your embed code below:": "Paste your embed code below:",
"Embed": "Embed",
"Media...": "Media...",
"Nonbreaking space": "Nonbreaking space",
"Page break": "Page break",
"Paste as text": "Paste as text",
"Preview": "Preview",
"Print...": "Print...",
"Save": "Save",
"Find": "Find",
"Replace with": "Replace with",
"Replace": "Replace",
"Replace all": "Replace all",
"Previous": "Previous",
"Next": "Next",
"Find and Replace": "Encontrar y Reemplazar",
"Find and replace...": "Find and replace...",
"Could not find the specified string.": "Could not find the specified string.",
"Match case": "Match case",
"Find whole words only": "Find whole words only",
"Find in selection": "Encontrar en la selecci\u00f3n",
"Spellcheck": "Spellcheck",
"Spellcheck Language": "Spellcheck Language",
"No misspellings found.": "No se encontraron errores ortogr\u00e1ficos.",
"Ignore": "Ignore",
"Ignore all": "Ignore all",
"Finish": "Finish",
"Add to Dictionary": "Add to Dictionary",
"Insert table": "Insert table",
"Table properties": "Table properties",
"Delete table": "Delete table",
"Cell": "Cell",
"Row": "Row",
"Column": "Column",
"Cell properties": "Cell properties",
"Merge cells": "Merge cells",
"Split cell": "Split cell",
"Insert row before": "Insert row before",
"Insert row after": "Insert row after",
"Delete row": "Delete row",
"Row properties": "Row properties",
"Cut row": "Cut row",
"Copy row": "Copy row",
"Paste row before": "Paste row before",
"Paste row after": "Paste row after",
"Insert column before": "Insert column before",
"Insert column after": "Insert column after",
"Delete column": "Delete column",
"Cols": "Cols",
"Rows": "Rows",
"Width": "Width",
"Height": "Height",
"Cell spacing": "Cell spacing",
"Cell padding": "Cell padding",
"Caption": "Caption",
"Show caption": "Show caption",
"Left": "Left",
"Center": "Center",
"Right": "Right",
"Cell type": "Cell type",
"Scope": "Scope",
"Alignment": "Alignment",
"H Align": "H Align",
"V Align": "V Align",
"Top": "Top",
"Middle": "Middle",
"Bottom": "Bottom",
"Header cell": "Header cell",
"Row group": "Row group",
"Column group": "Column group",
"Row type": "Row type",
"Header": "Header",
"Body": "Body",
"Footer": "Footer",
"Border color": "Border color",
"Insert template...": "Insert template...",
"Templates": "Templates",
"Template": "Template",
"Text color": "Text color",
"Background color": "Background color",
"Custom...": "Custom...",
"Custom color": "Custom color",
"No color": "No color",
"Remove color": "Remove color",
"Table of Contents": "Table of Contents",
"Show blocks": "Show blocks",
"Show invisible characters": "Show invisible characters",
"Word count": "Word count",
"Count": "Count",
"Document": "Document",
"Selection": "Selection",
"Words": "Words",
"Words: {0}": "Words: {0}",
"{0} words": "{0} words",
"File": "File",
"Edit": "Edit",
"Insert": "Insert",
"View": "View",
"Format": "Format",
"Table": "Table",
"Tools": "Tools",
"Powered by {0}": "Powered by {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help",
"Image title": "Image title",
"Border width": "Border width",
"Border style": "Border style",
"Error": "Error",
"Warn": "Warn",
"Valid": "Valid",
"To open the popup, press Shift+Enter": "To open the popup, press Shift+Enter",
"Rich Text Area. Press ALT-0 for help.": "Rich Text Area. Press ALT-0 for help.",
"System Font": "System Font",
"Failed to upload image: {0}": "Failed to upload image: {0}",
"Failed to load plugin: {0} from url {1}": "Failed to load plugin: {0} from url {1}",
"Failed to load plugin url: {0}": "Failed to load plugin url: {0}",
"Failed to initialize plugin: {0}": "Failed to initialize plugin: {0}",
"example": "example",
"Search": "Search",
"All": "All",
"Currency": "Currency",
"Text": "Text",
"Quotations": "Quotations",
"Mathematical": "Mathematical",
"Extended Latin": "Extended Latin",
"Symbols": "Symbols",
"Arrows": "Arrows",
"User Defined": "User Defined",
"dollar sign": "dollar sign",
"currency sign": "currency sign",
"euro-currency sign": "euro-currency sign",
"colon sign": "colon sign",
"cruzeiro sign": "cruzeiro sign",
"french franc sign": "french franc sign",
"lira sign": "lira sign",
"mill sign": "mill sign",
"naira sign": "naira sign",
"peseta sign": "peseta sign",
"rupee sign": "rupee sign",
"won sign": "won sign",
"new sheqel sign": "new sheqel sign",
"dong sign": "dong sign",
"kip sign": "kip sign",
"tugrik sign": "tugrik sign",
"drachma sign": "drachma sign",
"german penny symbol": "german penny symbol",
"peso sign": "peso sign",
"guarani sign": "guarani sign",
"austral sign": "austral sign",
"hryvnia sign": "hryvnia sign",
"cedi sign": "cedi sign",
"livre tournois sign": "livre tournois sign",
"spesmilo sign": "spesmilo sign",
"tenge sign": "tenge sign",
"indian rupee sign": "indian rupee sign",
"turkish lira sign": "turkish lira sign",
"nordic mark sign": "nordic mark sign",
"manat sign": "manat sign",
"ruble sign": "ruble sign",
"yen character": "yen character",
"yuan character": "yuan character",
"yuan character, in hong kong and taiwan": "yuan character, in hong kong and taiwan",
"yen\/yuan character variant one": "yen\/yuan character variant one",
"Loading emoticons...": "Loading emoticons...",
"Could not load emoticons": "Could not load emoticons",
"People": "People",
"Animals and Nature": "Animals and Nature",
"Food and Drink": "Food and Drink",
"Activity": "Activity",
"Travel and Places": "Travel and Places",
"Objects": "Objects",
"Flags": "Flags",
"Characters": "Characters",
"Characters (no spaces)": "Characters (no spaces)",
"{0} characters": "{0} characters",
"Error: Form submit field collision.": "Error: Form submit field collision.",
"Error: No form element found.": "Error: No form element found.",
"Update": "Update",
"Color swatch": "Color swatch",
"Turquoise": "Turquoise",
"Green": "Green",
"Blue": "Blue",
"Purple": "Purple",
"Navy Blue": "Navy Blue",
"Dark Turquoise": "Dark Turquoise",
"Dark Green": "Dark Green",
"Medium Blue": "Medium Blue",
"Medium Purple": "Medium Purple",
"Midnight Blue": "Midnight Blue",
"Yellow": "Yellow",
"Orange": "Orange",
"Red": "Red",
"Light Gray": "Light Gray",
"Gray": "Gray",
"Dark Yellow": "Dark Yellow",
"Dark Orange": "Dark Orange",
"Dark Red": "Dark Red",
"Medium Gray": "Medium Gray",
"Dark Gray": "Dark Gray",
"Light Green": "Light Green",
"Light Yellow": "Light Yellow",
"Light Red": "Light Red",
"Light Purple": "Light Purple",
"Light Blue": "Light Blue",
"Dark Purple": "Dark Purple",
"Dark Blue": "Dark Blue",
"Black": "Black",
"White": "White",
"Switch to or from fullscreen mode": "Switch to or from fullscreen mode",
"Open help dialog": "Open help dialog",
"history": "history",
"styles": "styles",
"formatting": "formatting",
"alignment": "alignment",
"indentation": "indentation",
"Font": "Font",
"Size": "Size",
"More...": "More...",
"Select...": "Select...",
"Preferences": "Preferences",
"Yes": "Yes",
"No": "No",
"Keyboard Navigation": "Keyboard Navigation",
"Version": "Version",
"Code view": "Vista de c\u00f3digo",
"Open popup menu for split buttons": "Abrir men\u00fa emergente para botones divididos",
"List Properties": "Propiedades de Lista",
"List properties...": "Propiedades de lista...",
"Start list at number": "Iniciar lista en el n\u00famero",
"Line height": "Altura de la l\u00ednea",
"comments": "comments",
"Format Painter": "Format Painter",
"Insert\/edit iframe": "Insert\/edit iframe",
"Capitalization": "Capitalization",
"lowercase": "lowercase",
"UPPERCASE": "UPPERCASE",
"Title Case": "Title Case",
"permanent pen": "permanent pen",
"Permanent Pen Properties": "Permanent Pen Properties",
"Permanent pen properties...": "Permanent pen properties...",
"case change": "Cambiar May\u00fasculas y Min\u00fasculas",
"page embed": "p\u00e1gina incrustada",
"Advanced sort...": "Orden avanzado...",
"Advanced Sort": "Orden Avanzado",
"Sort table by column ascending": "Ordenar tabla por columna ascendente",
"Sort table by column descending": "Ordenar tabla por columna descendente",
"Sort": "Ordenar",
"Order": "Orden",
"Sort by": "Ordenar por",
"Ascending": "Ascendente",
"Descending": "Descendiente",
"Column {0}": "Columna {0}",
"Row {0}": "Fila {0}",
"Spellcheck...": "Corrector...",
"Misspelled word": "Palabra mal escrita",
"Suggestions": "Sugerencias",
"Change": "Cambiar",
"Finding word suggestions": "Encontrar sugerencias de palabras",
"Success": "\u00c9xito",
"Repair": "Reparar",
"Issue {0} of {1}": "Problema {0} de {1}",
"Images must be marked as decorative or have an alternative text description": "Las im\u00e1genes deben estar marcadas como decorativas o tener una descripci\u00f3n de texto alternativa",
"Images must have an alternative text description. Decorative images are not allowed.": "Las im\u00e1genes deben tener una descripci\u00f3n de texto alternativa. No se permiten im\u00e1genes decorativas.",
"Or provide alternative text:": "O proporcione texto alternativo:",
"Make image decorative:": "Hacer la imagen decorativa:",
"ID attribute must be unique": "El atributo de ID debe ser \u00fanico",
"Make ID unique": "Hacer que ID sea \u00fanica",
"Keep this ID and remove all others": "Conserve esta ID y elimine todas las dem\u00e1s",
"Remove this ID": "Eliminar esta ID",
"Remove all IDs": "Eliminar todos los ID",
"Checklist": "Lista de Verificaci\u00f3n",
"Anchor": "Anchor",
"Special character": "Special character",
"Code sample": "Code sample",
"Color": "Color",
"Document properties": "Document properties",
"Image description": "Image description",
"Image": "Image",
"Insert link": "Insert link",
"Target": "Target",
"Link": "Link",
"Poster": "Poster",
"Media": "Media",
"Print": "Print",
"Prev": "Prev",
"Find and replace": "Find and replace",
"Whole words": "Whole words",
"Insert template": "Insert template"
});