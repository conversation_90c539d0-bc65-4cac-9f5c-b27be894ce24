from django.db import models
from family.models import FamilyGroups
from django.contrib.auth.models import User
from family.models import Category
from utils.BaseModel import BaseModel
from django.db.models import Sum
from datetime import datetime, date, timedelta
from user_authentication.models import UserProfile
import os
from family.models import WeeklyUploadFile
from tinymce.models import HTMLField
from family.models import FamilyGroups, FamilyMembers
# Create your models here.

QUESTION_TYPE_CHOICES = [
    ('text', 'Text'),
    ('image', 'Image'),
    ('mcq_text', 'MCQ Text'),     
    ('mcq_image', 'MCQ Image')
]

OPTIONS_TYPE_CHOICES = [
    ('image_upload', 'Image Upload'),
    ('text_input', 'Text Input'),
    ('text_option', 'Text Option'),
    ('image_option', 'Image Option')
]

class QuizTypeChoices(models.TextChoices):
    regular = "regular", "Regular"
    bonus = "bonus", "Bonus"

# QUIZ_TYPE_CHOICES = [
#     ('regular', 'Regular'),
#     ('bonus', 'Bonus'),
# ]

class Question(BaseModel, models.Model):
    question_no = models.CharField(max_length=200, null= True, blank=True)
    question = models.TextField(null=True, blank=True)
    type = models.CharField(max_length=20, choices=QUESTION_TYPE_CHOICES)
    option_type = models.CharField(max_length=20, choices=OPTIONS_TYPE_CHOICES)
    option = models.JSONField(null=True, blank=True)
    answer = models.CharField(max_length=255, help_text="Key for the Option, for eg :- A | B | C | D")
    explanation = models.TextField(null=True, blank=True)
    category = models.CharField(max_length=200)
    shuffle = models.BooleanField(default=False)
    score = models.IntegerField(null=True, blank=True)
    quiz_type = models.CharField(max_length=20, choices=QuizTypeChoices)
    date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.id} - {self.question} - {self.option_type} - {self.is_deleted}"
    

class UserQuestion(BaseModel, models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    date = models.DateField(null=True, blank=True)
    answered = models.BooleanField(default=False)
    is_correct = models.BooleanField(default=False)
    selected_answer = models.CharField(max_length=200, null=True, blank=True)

    def __str__(self):
        return f"{self.id} - {self.question.id} - {self.date}"
    

class AnswerText(BaseModel, models.Model):
    question = models.ForeignKey(UserQuestion, related_name='answer_text', on_delete=models.CASCADE)
    answer = models.TextField(null=True, blank=True) 

    def __str__(self):
        return f"{self.id}"

class AnswerImage(BaseModel, models.Model):
    question  = models.ForeignKey(UserQuestion, related_name='answer_image', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='answer/image')

    def __str__(self):
        return f"{self.id}"

class QuestionImage(BaseModel, models.Model):
    question = models.ForeignKey(Question, related_name="question_image", on_delete=models.CASCADE)
    image = models.ImageField(upload_to='question/image')

    def __str__(self):
        return f"{self.id} - question id - {self.question.id}"

class OptionImages(BaseModel, models.Model):
    question = models.ForeignKey(Question, related_name="option_image", on_delete=models.CASCADE)
    image = models.ImageField(upload_to='option/image')
    key = models.CharField(max_length=20)

    def __str__(self):
        return f"{self.id}"

class UserLeaderboard(BaseModel, models.Model):
    SCORE_TYPE = (
        ('Quiz', 'Quiz'),
        ('Check-In', "Check-In"),
        ('Food-Step-SS', "Food-Step-SS"),
    )
    type = models.CharField(max_length=200, choices=SCORE_TYPE, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    score = models.IntegerField(null=True, blank=True, default=0)

    def __str__(self):
        return f"{self.user} - {self.score}"
class Leaderboard(BaseModel, models.Model):
    user_leaderboard = models.ManyToManyField(UserLeaderboard)
    weekly_file = models.ManyToManyField(WeeklyUploadFile)
    family_group = models.ForeignKey(FamilyGroups, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    score = models.IntegerField(null=True, blank=True, default=0)

    def update_score(self):
        # Calculate the total score of related UserLeaderboard instances
        selfie_score = self.weekly_file.aggregate(total_score=Sum('score'))['total_score'] or 0
        user_score = self.user_leaderboard.aggregate(total_score=Sum('score'))['total_score'] or 0
        total = int(selfie_score) + int(user_score)
        return total

class UserSteps(BaseModel, models.Model):
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)
    steps = models.BigIntegerField(default=0)
    date = models.DateField(blank=True, null=True)

    def __str__(self):
        return f"Step on {self.date} - {self.steps}"

class UserStepImage(BaseModel, models.Model):
    image = models.ImageField(upload_to="question/step")
    score = models.IntegerField(default=0)

class QuizStep(BaseModel, models.Model):
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)
    steps = models.ManyToManyField(UserSteps)
    image = models.ForeignKey(UserStepImage,null=True, blank=True, on_delete=models.CASCADE)
    week_start_date = models.DateField(null=True, blank=True)
    steps_score = models.BigIntegerField(default=0)

    def update_steps_score(self):
        """
        Calculate and update the steps_score based on the steps within the week.
        and apply the multipliers on the age groups
        """
        if self.week_start_date:
            end_date = self.week_start_date + timedelta(days=6)
            # Get all UserSteps associated with this QuizStep within the date range
            weekly_steps = self.steps.filter(
                created_at__date__range=[self.week_start_date, end_date]
            ).aggregate(total_steps=models.Sum('steps'))
            
            score = 0
            if self.image and self.image.image:
                score = self.image.score

            steps_score = weekly_steps['total_steps'] or 0
            try:
                user_profile = UserProfile.objects.get(user=self.user, is_deleted=False)
            except UserProfile.DoesNotExist:
                raise ValueError("UserProfile for the user does not exist or has been deleted.")
            
            try:
                family_member = FamilyMembers.objects.get(member=user_profile.user, is_deleted=False)
            except FamilyMembers.DoesNotExist:
                raise ValueError("Family member record does not exist or has been deleted for this user.")

     
            total_members = FamilyMembers.objects.filter(
                    family_group=family_member.family_group,
                    is_deleted=False,
                    is_member=True
                ).count()

            # Apply multipliers based on age group
            if user_profile.age_group == "71-80":
                steps_score *= 1.5
            elif user_profile.age_group == ">81":
                steps_score *= 2
                
            self.steps_score = int(steps_score) / total_members + int(score)
            self.save()

    @staticmethod
    def get_most_recent_monday(d):
        """Given a date, return the most recent Monday."""
        return d - timedelta(days=d.weekday())

    def save(self, *args, **kwargs):
        if not self.week_start_date:
            self.week_start_date = self.get_most_recent_monday(datetime.now().date())
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Step on {self.created_at} - {self.user}"
    

class StepsLeaderboard(BaseModel, models.Model):
    user_leaderboard = models.ManyToManyField(QuizStep)
    family_group = models.ForeignKey(FamilyGroups, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    score = models.BigIntegerField(null=True, blank=True, default=0)

    def update_score(self):
        # Calculate the total score of related UserLeaderboard instances
        total = self.user_leaderboard.aggregate(total_score=Sum('steps_score'))['total_score'] or 0
        self.score = total
        return total

class Info(BaseModel, models.Model):
    prize_info = HTMLField(null=True, blank=True)
    leaderboard_info = HTMLField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.pk and Info.objects.exists():
            raise ValueError("There can only be one Info instance.")
        return super().save(*args, **kwargs)

    @classmethod
    def load(cls):
        obj, created = cls.objects.get_or_create(pk=1)
        return obj
    
class QuizDates(BaseModel, models.Model):
    start_date = models.DateField()
    end_date = models.DateField()

    def save(self, *args, **kwargs):
        if not self.pk and QuizDates.objects.exists():
            raise ValueError("There can only be one Info instance.")
        return super().save(*args, **kwargs)

    @classmethod
    def load(cls):
        obj, created = cls.objects.get_or_create(pk=1)
        return obj
    

class UserFoodStepImage(BaseModel, models.Model):
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)    
    image = models.ImageField(upload_to="question/food/step")
    
    UPLOAD_TYPE_CHOICES = (
        ('Before Quiz', 'Before Quiz'),
        ('After Quiz', "After Quiz"),
    )
    upload_type = models.CharField(max_length=128, choices=UPLOAD_TYPE_CHOICES, null=True, blank=True)
