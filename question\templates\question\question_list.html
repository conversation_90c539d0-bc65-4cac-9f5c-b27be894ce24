{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Question list' %}{% endblock %}
{% block content %}
{% load static %}

{% for message in messages %}
{% comment %} <div id="toast-success" class="flex items-center w-full max-w-full p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-lg dark:text-gray-400 dark:bg-gray-800" role="alert"> {% endcomment %}
    {% if message.tags == 'error' %}
    <div id="questionError" class="error-msg p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
        <span class="font-medium">{{message}}</span>
        <span onclick="closeMsg('questionError')">
            <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
    </div>
    {% elif message.tags == "success"%}
    <div id="questionSuccess" class="error-msg p-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 flex justify-between shadow-lg border-l-green-800 border-l-4" role="alert">
        <span class="font-medium">{{message}}</span>
        <span onclick="closeMsg('questionSuccess')">
            <svg class="fill-current h-6 w-6 text-green-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
    </div>
    {% else %}
    <div id="questionWarn" class="error-msg p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 flex justify-between shadow-lg border-l-yellow-800 border-l-4" role="alert">
        <span class="font-medium">{{message}}</span>
        <span onclick="closeMsg('questionWarn')">
            <svg class="fill-current h-6 w-6 text-yellow-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
    </div>
    {% endif %}
{% comment %} <div class="ms-3 text-sm font-normal">{{message}}</div>
    <button type="button" class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" data-dismiss-target="#toast-success" aria-label="Close">
        <span class="sr-only">Close</span>
        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
        </svg>
    </button>
</div> {% endcomment %}
{% endfor %}
<div class="p-4">       
        <div class="mb-3 flex justify-between">
            <h1 class="text-2xl font-bold mb-4">{% trans 'Question List' %}</h1>
            <div class="content-center">
                <button data-modal-target="import-question-modal" data-modal-toggle="import-question-modal" type="button" class="text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg">{% trans 'Import'%}</button>
                <a href="/assign_to_new_users/" id="id_assign_to_new_users" class="text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg">{% trans 'Assign to New Users' %}</a>
            </div>

            <!-- Main modal -->
            <div id="import-question-modal" tabindex="-1" aria-hidden="true" data-modal-backdrop="static" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-baseline w-full md:inset-0 h-[calc(100%-4rem)] max-h-full">
                <div class="relative p-4 w-full max-w-2xl max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-700">
                        <!-- Modal header -->
                        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                Import Questions
                            </h3>
                            <button type="button" class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="import-question-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <form id="import-form" class="space-y-4" enctype="multipart/form-data" action="/import-question/" method="post">
                            {% csrf_token %}
                            <div class="p-4 md:p-5 space-y-4 !mt-auto">
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white" for="id_csv_file">Upload file</label>
                                <input class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400" id="id_csv_file" name="csv_file" type="file">
                            </div>
                            <div id="infoMsg" class="!-mt-2.5 mb-2 mr-6 ml-6">Only <b>.csv</b> file is allowed. <a class="text-blue-500 hover:underline" href="/download-import-questions-samplefile/">Click here</a> to download sample file.</div>
                            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600 justify-end">
                                <button type="submit" class="text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg">{% trans 'Import' %}</button>
                                <button id="modalCloseBtn" data-modal-hide="import-question-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-primary-300 hover:text-gray-900 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600 shadow-lg">
                                    {% trans 'Close' %}
                                </button>
                            </div>
                        </form>


                            {% comment %} <script>
                                document.getElementById('import-form').addEventListener('submit', function(e) {
                                    e.preventDefault();
                                    document.getElementById('import-form').classList.add('hidden');
                                    document.getElementById('spinner').classList.remove('hidden');
                                    this.submit();
                                });
                            </script> {% endcomment %}


                            <script>
                                document.getElementById('import-form').addEventListener('submit', function(e) {
                                    e.preventDefault();
                                    document.getElementById('import-question-modal').classList.add('hidden');
                                    document.getElementById('spinner').classList.remove('hidden');
                                    setTimeout(() => {
                                        this.submit();
                                    }, 2000); // Adding a delay of 2 seconds before submitting the form
                                });

                                document.getElementById('id_assign_to_new_users').addEventListener('click', () => {
                                    document.getElementById('spinner').classList.remove('hidden');
                                });
                            </script>

                        <!-- </div> -->
                    </div>
                </div>
            </div> 
        </div>

        <div class="flex flex-col md:flex-row w-full mx-auto mb-3 justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
            <!-- Page length dropdown -->
            <div id="dropdown" class="flex items-center space-x-2 z-10 w-full md:w-auto">
                <span>Show</span>
                <select id="id_pageLength" name="page_length" onchange="changePageLength()" class="shadow-lg w-fit bg-white hover:bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>
    
            <div id="date-range-picker" date-rangepicker class="flex items-center">
                <div class="relative flex">
                    <div class="inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                        <svg class="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400 shadow-lg" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                        </svg>
                    </div>
                    <input id="datepicker-range-start" name="start" type="text" style="width: 130px;" value="{{ from_date }}" class="date-picker flex-grow bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 mr-0.5 shadow-lg md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500 rounded-lg" placeholder="Select start date">
                </div>
                <span class="text-gray-500" style="margin: 0px 5px 0px 5px;"> to </span>
                <div class="relative">
                    <input id="datepicker-range-end" name="end" type="text" style="width: 130px;" value="{{ to_date }}" class="date-picker flex-grow bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 mr-0.5 shadow-lg md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500 rounded-lg" placeholder="Select end date">
                </div>
                <button type="button" id="id_clearSearch" onclick="clearSearch()" title="Clear" class="{% if not from_date or not to_date %} hidden {% endif %} p-2.5 mb-2 md:mb-0 text-sm font-medium text-gray-600 bg-transparent border-none cursor-pointer focus:outline-none focus:ring-0 md:rounded-none">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 6l8 8M14 6l-8 8" />
                    </svg>
                    <span class="sr-only">Clear</span>
                </button>
                <button type="button" onclick="searchUsersQueAnsByDate()" class="shadow-lg p-2.5 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 border border-gray-300">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                    <span class="sr-only">Search</span>
                </button>
            </div>
        </div>
     
            
            <div class="relative w-full shadow-lg rounded-lg overflow-x-auto sm:rounded-lg">
                <div class="table-container">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                        <thead class=" text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                            <tr>
                                <th scope="col" class="px-6 py-3">
                                    <div class="flex items-center">
                                        Updated On
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    <div class="flex items-center">
                                        Question
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Type
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Option Type
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Option A
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Option B
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Option C
                                    </div>
                                </th>
                                <!-- <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Option D
                                    </div>
                                </th> -->
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Answer
                                    </div>
                                </th>
                                <!-- <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Explanation
                                    </div>
                                </th> -->
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Category
                                    </div>
                                </th>
                                <!-- <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Shuffle
                                    </div>
                                </th> -->
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Score
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        Quiz Type
                                    </div>
                                </th>
                                {% comment %} <th scope="col" class="px-6 py-3 ">
                                    <div class="flex items-center">
                                        View detail
                                    </div>
                                </th> {% endcomment %}
                            </tr>
                        </thead>
                        <tbody>
                            {% if data %}
                                {% for dt in data %}
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.updated_at}}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {% if dt.question %}
                                                {{dt.question}}
                                            {% elif dt.que_image %}
                                                <img src="{{dt.que_image}}" class="image-style rounded border" alt="">
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.type}}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.option_type}}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {% if dt.option_a and dt.is_user_ans_img %}
                                                <img src="{{dt.option_a}}" class="image-style rounded border" alt="">
                                            {% else %}
                                            {{dt.option_a}}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {% if dt.option_b and dt.is_user_ans_img %}
                                                <img src="{{dt.option_b}}" class="image-style rounded border" alt="">
                                            {% else %}
                                            {{dt.option_b}}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {% if dt.option_c and dt.is_user_ans_img %}
                                                <img src="{{dt.option_c}}" class="image-style rounded border" alt="">
                                            {% else %}
                                            {{dt.option_c}}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <!-- <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {% if dt.option_d and dt.is_user_ans_img %}
                                                <img src="{{dt.option_d}}" class="image-style rounded border" alt="">
                                            {% else %}
                                            {{dt.option_d}}
                                            {% endif %}
                                        </div>
                                    </td> -->
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {% if dt.answer and dt.is_user_ans_img %}
                                                <img src="{{dt.answer}}" class="image-style rounded border" alt="">
                                            {% else %}
                                            {{dt.answer}}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <!-- <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.explanation}}
                                        </div>
                                    </td> -->
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.category}}
                                        </div>
                                    </td>
                                    <!-- <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.shuffle}}
                                        </div>
                                    </td> -->
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.score}}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            {{dt.quiz_type}}
                                        </div>
                                    </td>
    
                                    {% comment %} <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <a type="submit" href="{% url "question_detail" question.id %}" class="w-fit text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">View
                                            </a>
                                        </div>
                                    </td> {% endcomment %}
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="11" class="px-6 py-4 text-base">Data is not available.</td></tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
    
            <nav class="flex flex-col bg-gray-50 md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4" aria-label="Table navigation">
                <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                    Showing
                    <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
                    of
                    <span class="font-semibold text-gray-900 dark:text-white">{{ total_items }}</span>
                </span>
                <ul class="inline-flex items-stretch -space-x-px">
                    {% if page_obj.has_previous %}
                    <li>
                        <a href="?page={{ page_obj.previous_page_number }}&length={{ page_length }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <span class="sr-only">Previous</span>
                            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                    {% endif %}
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li>
                            <a
                            class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                {{ num }}
                            </a>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %} <li>
                            <a href="?page={{ num }}&length={{ page_length }}"
                            class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                {{ num }}
                            </a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    {% if page_obj.has_next %}
                    <li>
                        <a href="?page={{ page_obj.next_page_number }}&length={{ page_length }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <span class="sr-only">Next</span>
                            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
</div>
<script src="{% static 'question/js/question_list.js'%}?v=0.4"></script>
<script>

    $(document).ready(function() { 
        setTimeout(() => {
            $('.error-msg').hide();
        }, 10000);

        var dropdown = document.getElementById('id_pageLength');
        var page_length = '{{ page_length }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === page_length) {
                dropdown.options[i].selected = true;
                break;
            }
        }
    });

</script>
{% endblock content %}
