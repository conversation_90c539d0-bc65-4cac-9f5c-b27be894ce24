from storages.backends.s3boto3 import S3Boto3Storage
from django.conf import settings

class StaticStorage(S3Boto3Storage):
    static_bucket = settings.STATIC_BUCKET_NAME

    if static_bucket:
        bucket_name = static_bucket
        custom_domain = settings.STATIC_CLOUDFRONT_DOMAIN if hasattr(settings, 'STATIC_CLOUDFRONT_DOMAIN') else ''

    location = settings.STATIC_LOCATION if hasattr(settings, 'STATIC_LOCATION') else 'static'


class MediaStorage(S3Boto3Storage):
    static_bucket = settings.MEDIA_BUCKET_NAME
 
    if static_bucket:
        bucket_name = static_bucket
        custom_domain = settings.MEDIA_CLOUDFRONT_DOMAIN if hasattr(settings, 'MEDIA_CLOUDFRONT_DOMAIN') else ''

    location = settings.PUBLIC_MEDIA_LOCATION if hasattr(settings, 'PUBLIC_MEDIA_LOCATION') else 'media'
