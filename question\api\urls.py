from rest_framework.routers import Default<PERSON>outer
from django.urls import path, include
from .api import QuestionViewset, LeaderBoardView, UploadSteps, UploadFoodStepScrnShot

router = DefaultRouter()
router.register(r'quiz', QuestionViewset, basename="question_list")
router.register(r'', LeaderBoardView, basename='leaderboard')
router.register('', UploadSteps, basename='daily_steps')
router.register('', UploadFoodStepScrnShot, basename='upload_food_step_ss')


urlpatterns = [    
    path('api/', include(router.urls))
]
