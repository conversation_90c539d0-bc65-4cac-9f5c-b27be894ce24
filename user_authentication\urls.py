from django.urls import path, include
from .views import SendOtpView 
from rest_framework.routers import DefaultRouter
from user_authentication.views import *

router = DefaultRouter()

############################################################
#API Router Urls
############################################################
router.register('', LoginView, basename="login")
router.register('', LogoutView, basename="logout")
router.register('', SendOtpView, basename="send_otp")
router.register('', MemberSendOtpView, basename="member_send_otp")
router.register('', VerifyOTP, basename='verify_otp')
router.register('', ChangePwView, basename='change_password')
router.register('profile', UserProfileDetail, basename='profile')
router.register('', InviteFamilyMember, basename="invite_family_member")
router.register('', CancelInvitation, basename="cancel_family_member")
router.register('', UserCheck_in, basename="checkin")
router.register('', DeactivateMemberView, basename="deactivate_member")




urlpatterns = [
    ############################################################
    #api urls
    ############################################################
    path('api/', include(router.urls)),
    path('api/reinvite-family-member/', ReInviteMember.as_view(), name='reinvite_family_member'),


    ############################################################
    #Admin Template urls
    ############################################################
    path('signin/', signin, name="signin"),
    path('signout/', signout, name="signout"),
    path('users/', get_users, name="users"),
    path('user/<int:id>/', get_upd_user_profile, name="user"),
    path('delete-user/', delete_user, name="delete_user"),
    path('import-usersdata/', import_usersdata, name="import-usersdata"),
    path('export-userdata/', user_list_export, name="export-userdata"),
    path('download-import-users-samplefile/', download_import_users_samplefile, name="download_import_users_samplefile"),

]