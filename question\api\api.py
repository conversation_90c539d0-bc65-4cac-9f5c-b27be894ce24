from rest_framework import viewsets, status
from rest_framework.decorators import action
from ..models import (
    UserQuestion, AnswerImage, AnswerText, Leaderboard, QuizStep, Info, StepsLeaderboard, UserLeaderboard, UserSteps, UserStepImage, QuizDates, UserFoodStepImage
)
from .serializer import (
    UserQuestionSerializer, UserAnswerSerializer, LeaderBoardViewSerializer, StepsLeaderBoardViewSerializer, GetUserSteps, InfoSerializer, 
    UserStepsSerializer, QuizStepsSerializer
)
from base.utils import custom_data_response, custom_error_response
from django.db.models import Case, When, <PERSON><PERSON>anField, Value, Exists
from rest_framework.permissions import IsAuthenticated, AllowAny
from datetime import datetime
from django.db.models import Q
from base.views import create_from_exceptions
import traceback, logging
from family.models import FamilyMembers
from user_authentication.models import UserProfile
from django.db import transaction
from decouple import config
from ..helper import get_most_recent_monday
from django.db import DataError
from django.conf import settings
from base import utils
from rest_framework.response import Response 

logger = logging.getLogger('django')


class QuestionViewset(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]

    action_serializers = {
        'get_question': UserQuestionSerializer,
        'send_answer': UserAnswerSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['get'], detail=False, url_path='get_question/(?P<quiz_type>[^/.]+)')
    def get_question(self, request, quiz_type):
        try:
            questions = UserQuestion.active_objects.filter(
                (Q(answered = True) | Q(answered = False)),
                date = datetime.now().date(),
                user=request.user,
                question__quiz_type__iexact = quiz_type
            )
            total_questions = questions.filter((Q(answered = True) | Q(answered = False))).count()
            answered_question = questions.filter(answered = True).count()
            user_questions = questions.filter(answered = False).order_by('?').first()
            if user_questions:
                serialized_user_question  = UserQuestionSerializer(user_questions)
                data = serialized_user_question.data
                data["total_question"] = total_questions
                data['current_question'] = answered_question +1
                return custom_data_response(data=data)
            else:
                question_count = questions.filter(answered = False).count()
                if question_count == 0:
                    question_scores = questions.filter(
                        (Q(is_correct = True) | Q(is_correct = False)),
                        answered = True)
                    if question_scores.count() > 0:
                        correct  = questions.filter(is_correct = True)
                        incorrect = questions.filter(is_correct = False)
                        score = 0
                        bonus_score = 0
                        for question in correct: 
                            score += question.question.score           
                        if str(quiz_type).lower() == "bonus":
                            for bonus_question in incorrect:
                                bonus_score += bonus_question.question.score
                            score +=bonus_score
                        summery = {
                            "type": "result",
                            "score": score,
                            "correct": correct.count(),
                            "incorrect": incorrect.count(),
                        }
                        return custom_data_response(data=summery, message="Quiz is completed.")
                return custom_data_response(data=None, message="No questions found")
        except Exception as e:
            logger.error(f"Error in get_question api  => \n\n{e}\n\n")
            create_from_exceptions(request.user, e, traceback.format_exc())
            return custom_error_response(
                message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(methods=['post'], detail=False, url_path='skip_questions/(?P<quiz_type>[^/.]+)')
    def skip_questions(self, request, quiz_type):
        try:
            skip = request.data.get("skip")
            if skip:
                questions = UserQuestion.active_objects.filter(
                    (Q(answered = True) | Q(answered = False)),
                    date = datetime.now().date(),
                    user=request.user,
                    question__quiz_type__iexact = quiz_type
                )
                for question in questions:
                    question.answered = True
                UserQuestion.objects.bulk_update(questions, ['answered'])
                return custom_data_response(data=None, message="Bonus question is skipped")
        except Exception as e:
            logger.error(f"Error in skip_questions api  => \n\n{e}\n\n")
            create_from_exceptions(request.user, e, traceback.format_exc())
            return custom_error_response(
                message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_leaderboard(self):
        family_member = FamilyMembers.objects.filter(member_id = self.request.user).first()
        try:
            leader_board = Leaderboard.objects.get(family_group=family_member.family_group, category = family_member.family_group.category)
        except Leaderboard.DoesNotExist:
            leader_board = Leaderboard.objects.create(family_group=family_member.family_group, category = family_member.family_group.category)
        return leader_board
    
    def update_score(self, user_leaderboard, user_question, leader_board):
        user_leaderboard.score = int(user_question.question.score)
        user_leaderboard.save()
        leader_board.score = leader_board.update_score()
        leader_board.save()
    
    def image_upload(self, request, image, user_question, user_leaderboard, leader_board):
        try:
            AnswerImage.objects.create(image = image, question = user_question)
            if user_question.question.explanation:
                data  = {"explanation": user_question.question.explanation}
                if int(user_question.question.score):
                    self.update_score(user_leaderboard, user_question, leader_board)
            return custom_data_response(data=data if data else None, message="Answer submitted successfully")
        except Exception as e:
            logger.error(f"Error in send_answer(image_upload) api  => \n\n{e}\n\n")
            create_from_exceptions(request.user, e, traceback.format_exc())
            return custom_error_response(
            message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    def text_input(self, request, answer, user_question, user_leaderboard, leader_board):
        try: 
            AnswerText.objects.create(answer = answer, question = user_question)
            if user_question.question.explanation:
                data  = {"explanation": user_question.question.explanation}
                if int(user_question.question.score):
                    self.update_score(user_leaderboard, user_question, leader_board)
            return custom_data_response(data=data if data else None, message="Answer submitted successfully")
        except Exception as e:
            logger.error(f"Error in send_answer(text_input) api  => \n\n{e}\n\n")
            create_from_exceptions(request.user, e, traceback.format_exc())
            return custom_error_response(
            message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
            
    @transaction.atomic
    @action(methods=['post'], detail=False, url_path='send_answer/(?P<id>[^/.]+)/(?P<quiz_type>[^/.]+)')
    def send_answer(self, request, id, quiz_type):
        try:
            user_leaderboard = UserLeaderboard.objects.create(user = self.request.user, type="Quiz")
            leader_board = self.get_leaderboard()
            leader_board.user_leaderboard.add(user_leaderboard)
            answer = request.data.get('answer')
            answer_type  = request.data.get('answer_type')
            user_question = UserQuestion.objects.get(id = id)
            is_correct = False
            if answer_type == "image_upload":
                image = request.FILES.get('image')
                self.image_upload(request, image, user_question, user_leaderboard, leader_board)
                is_correct = True
            if answer_type == "text_input":
                answer = request.data.get("answer")
                self.text_input(request, answer, user_question, user_leaderboard, leader_board)
                is_correct = True
            if str(quiz_type).lower() == "bonus":
                if int(user_question.question.score):
                    self.update_score(user_leaderboard, user_question, leader_board)
                    is_correct = True
            else:
                if user_question.question.answer == answer:
                    if int(user_question.question.score):
                        self.update_score(user_leaderboard, user_question, leader_board)
                    is_correct = True
            user_question.selected_answer = answer
            user_question.answered = True
            user_question.is_correct = is_correct
            user_question.save()
            data = {
                "is_correct": is_correct,
                "correct_answer": user_question.question.answer,
                "explanation": user_question.question.explanation
            }
            return custom_data_response(data=data, message="Answer submitted successfully")
        except Exception as e:
            logger.error(f"Error in send_answer api  => \n\n{e}\n\n")
            create_from_exceptions(request.user, e, traceback.format_exc())
            return custom_error_response(
                message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )  
class LeaderBoardView(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    
    action_serializers = {
        "leaderboard": LeaderBoardViewSerializer
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)
    
    @action(methods=['get'], detail=False, url_path="leaderboard/(?P<category>[^/.]+)")
    def leaderboard(self, request, category):
        try:
            infos = Info.objects.all().first()
            serialized_info = InfoSerializer(infos)

            family_member = FamilyMembers.objects.filter(member_id = self.request.user).first()            
            if category == "Steps":
                #This is only for the sports (As per requirement) we are not showing the steps count to the food category
                leader_board = StepsLeaderboard.objects.filter(
                    (Q(category__name__iexact = "sports") | Q(category__name__iexact = "sport"))
                    ).annotate(
                    is_self=Case(
                        When(family_group__id=family_member.family_group.id, then=Value(True)),
                        default=Value(False),
                        output_field=BooleanField()
                    )).order_by('-score') 
                serialized_leader_board = StepsLeaderBoardViewSerializer(leader_board, many=True)
            else:
                leader_board = Leaderboard.objects.filter(category__name__iexact = category).order_by('-score').annotate(
                is_self=Case(
                    When(family_group__id=family_member.family_group.id, then=Value(True)),
                    default=Value(False),
                    output_field=BooleanField()
                ))
                serialized_leader_board = LeaderBoardViewSerializer(leader_board, many=True)
            data = serialized_info.data
            data['leaderboard'] = serialized_leader_board.data
            data['category'] = category
            return custom_data_response(data=data)
        except Exception as e:
            logger.error(f"Error in leaderboard api  => \n\n{e}\n\n")
            create_from_exceptions(request.user, e, traceback.format_exc())
            return custom_error_response(
                message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
class UploadSteps(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]

    action_serializers = {
        'daily_steps':GetUserSteps,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)
    
    def get_steps_leaderboard(self):
        family_member = FamilyMembers.objects.filter(member_id=self.request.user).first()
        if not family_member:
            raise ValueError("Family member not found for the user.")
        try:
            leader_board = StepsLeaderboard.objects.get(family_group=family_member.family_group, category = family_member.family_group.category)
        except StepsLeaderboard.DoesNotExist:
            leader_board = StepsLeaderboard.objects.create(family_group=family_member.family_group, category = family_member.family_group.category)
        return leader_board

    def update_score(self, leader_board):
        leader_board.score = leader_board.update_score()
        leader_board.save()
    
    @action(methods=['post'], detail=False ,url_path='daily-steps') 
    def daily_steps(self,request):
        leader_board = self.get_steps_leaderboard()
        current_date = datetime.now().date()
        week_start_date = get_most_recent_monday(current_date)
        try:
            steps = request.data.get("steps")
            if not steps:
                return custom_error_response(message="Steps input required.")
            try:
                user_steps, user_steps_created = UserSteps.objects.get_or_create(date = current_date, user = self.request.user,  defaults={'steps': steps})
                if not user_steps_created:
                    user_steps.steps = steps 
                    user_steps.save()
                quiz_steps, quiz_steps_created = QuizStep.objects.get_or_create(user = self.request.user,  week_start_date = week_start_date)
            except DataError as e:
                return custom_error_response(message="User input exceeded expected input.")
            quiz_steps.steps.add(user_steps)
            quiz_steps.update_steps_score()
            quiz_steps.save()
            leader_board.user_leaderboard.add(quiz_steps)
            self.update_score(leader_board)
            data = {
                "steps": user_steps.steps,
                "score": quiz_steps.steps_score
            }
            return custom_data_response(data=data, message="Steps added successfully.")
        except Exception as e:
            logger.error(f"Error in daily_steps api  => \n\n{e}\n\n")
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return custom_error_response(
                message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    @action(methods=['get'], detail=False, url_path="weekly-steps")
    def weekly_steps(self, request):
        try:
            current_date = datetime.now().date()
            week_start_date = get_most_recent_monday(current_date)
            quiz_steps = QuizStep.objects.filter(user = self.request.user, week_start_date = week_start_date)
            if quiz_steps:
                serialized_quiz_step = QuizStepsSerializer(quiz_steps, many=True)
                return custom_data_response(data=serialized_quiz_step.data[0])
            return custom_data_response(message="No weekly steps found.")
        except Exception as e:
            logger.error(f"Error in weekly_steps api  => \n\n{e}\n\n")
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return custom_error_response(
                message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    @action(methods=['post'], detail=False, url_path="")
    def upload_weekly_steps(self, request):
        try:
            leader_board = self.get_steps_leaderboard()
            current_date = datetime.now().date()
            week_start_date = get_most_recent_monday(current_date)
            data = request.data.get('steps')
            data = eval(data)
            file = request.FILES.get('file')
            
            required_fields = {'steps': 'Steps'}
            missing_fields = [value for key,value in required_fields.items() if not request.data.get(key)]
            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return custom_data_response(message=message)
            
            if file and not utils.validate_file_extension(file.name):
                return utils.custom_error_response(message='Steps screenshot must be in jpg, jpeg, heic, png.')
            try: 
                quiz_steps = QuizStep.objects.get(user=self.request.user, week_start_date=week_start_date)
            except QuizStep.DoesNotExist:
                quiz_steps = QuizStep.objects.create(user=self.request.user, week_start_date=week_start_date)
                
            for steps_data in data:
                try:
                    user_steps = UserSteps.objects.get(user=self.request.user, date=steps_data['date'])
                except UserSteps.DoesNotExist:
                    user_steps = UserSteps.objects.create(user=self.request.user, date=steps_data['date'])

                user_steps.steps = int(steps_data['steps']) if steps_data.get('steps') else 0
                user_steps.save()
                quiz_steps.steps.add(user_steps)

            #100 extra points for the upload_weekly_steps
            # extra_points, created = UserSteps.objects.get_or_create(user = self.request.user, date = current_date)
            # if created:
            #     extra_points.steps = 100
            #     user_steps.save()
            #     quiz_steps.steps.add(extra_points)
            # else:
            #     user_steps.steps += 100
            #     user_steps.save()
            #     if quiz_step_created:
            #         quiz_steps.steps.add(user_steps)
            try:
                if quiz_steps.image:
                    step_image = UserStepImage.objects.get(id=quiz_steps.image.id)
                    step_image.image.delete()
                else:
                    step_image = UserStepImage.objects.create()
            except UserStepImage.DoesNotExist:
                step_image = UserStepImage.objects.create()
            step_image.image = file
            step_image.score = int(settings.STEPS_SCREENSHOT_SCORE)
            step_image.save()
            quiz_steps.image = step_image
            quiz_steps.save()
            leader_board.user_leaderboard.add(quiz_steps)
            quiz_steps.update_steps_score()
            self.update_score(leader_board)
            return custom_data_response(message="Data updated successfully")
        except Exception as e:
            logger.error(f"Error in upload_weekly_steps api  => \n\n{e}\n\n")
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return custom_error_response(
                message=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
            
class UploadFoodStepScrnShot(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    
    @action(methods=['post'], detail=False, url_path='upload_food_step_ss') 
    def upload_food_step_ss(self,request):
        try:
            with transaction.atomic():
                data = request.data
                before_quiz_file = request.FILES.get('before_quiz_file')
                after_quiz_file = request.FILES.get('after_quiz_file')
                user_id = request.data.get("user_id")
                
                required_fields = {'user_id': 'User Id'} 
                missing_fields = [value for key, value in required_fields.items() if not data.get(key)]
                if missing_fields:
                    message = utils.get_required_msg(missing_fields)
                    return custom_error_response(message=message)
                    
                if before_quiz_file and not utils.validate_file_extension(before_quiz_file.name) or after_quiz_file and not utils.validate_file_extension(after_quiz_file.name):
                    return utils.custom_error_response(message='Steps screenshot must be in jpg, jpeg, heic, png.')

                if not utils.check_user_exist(user_id):
                    return custom_error_response(message="The user does not exist or is inactive.")

                if str(utils.get_family_member_category(user_id)).lower() != "food":
                    return custom_error_response(message="Only member with Food category can upload screenshots.")
                
                quiz_end_data = QuizDates.objects.values("end_date").order_by("-id").first()
                if not quiz_end_data or not quiz_end_data["end_date"]:
                    return custom_error_response(message="Quiz end date is not defined.")
                
                today_date = datetime.now().date()
                if not quiz_end_data["end_date"] == today_date:
                    return custom_error_response(message="Food steps screenshots can only be uploaded at the end of the game.")
                
                # Delete existed today data and minus family selfie score. 
                if UserFoodStepImage.objects.filter(created_at__date=today_date, user_id=user_id, upload_type__in=["Before Quiz", "After Quiz"]).count() == 2:
                    UserFoodStepImage.objects.filter(created_at__date=today_date, user_id=user_id).delete()

                    if UserLeaderboard.objects.filter(created_at__date=today_date, user_id=user_id, type="Food-Step-SS").exists():
                        UserLeaderboard.objects.filter(created_at__date=today_date, user_id=user_id, type="Food-Step-SS").delete()
                        user_leaderboard = utils.get_leaderboard(int(user_id))
                        user_leaderboard.score = user_leaderboard.update_score()
                        user_leaderboard.save()
                
                # Create data and add family selfie score.    
                UserFoodStepImage.objects.bulk_create([
                    UserFoodStepImage(user_id=int(user_id), image=before_quiz_file, upload_type="Before Quiz"),
                    UserFoodStepImage(user_id=int(user_id), image=after_quiz_file, upload_type="After Quiz")
                ])
                
                family_selfie = UserLeaderboard.objects.create(user_id=user_id, type="Food-Step-SS", score=int(settings.FOOD_SCREENSHOT_SCORE))
                user_leaderboard = utils.get_leaderboard(int(user_id))
                user_leaderboard.user_leaderboard.add(family_selfie)
                user_leaderboard.score = user_leaderboard.update_score()
                user_leaderboard.save()

                return Response({"success": True, "message": "Steps screenshot uploaded successfully."}, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)