# Generated by Django 5.0.6 on 2024-07-03 05:20

import django.db.models.deletion
import family.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('family', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FamilyGroupFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(blank=True, null=True, upload_to=family.models.get_family_grp_file_path)),
                ('file_name', models.CharField(blank=True, max_length=100, null=True)),
                ('family_group', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='family.familygroups')),
            ],
        ),
    ]
