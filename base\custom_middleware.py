from django.conf import settings
from django.shortcuts import redirect
from django.urls import resolve
from django.utils.deprecation import MiddlewareMixin
import traceback
from base.views import *


class LoginRequiredMiddleware(MiddlewareMixin):
    def process_view(self, request, view_func, view_args, view_kwargs):
        try:
            assert hasattr(request, 'user'), "The Login Required middleware requires authentication middleware to be installed. Edit your MIDDLEWARE setting to insert 'django.contrib.auth.middleware.AuthenticationMiddleware'."
            
            # Bypass login check for login, logout, and other non-protected views
            if request.user.is_authenticated or resolve(request.path_info).url_name in ['signin'] or "api/" in resolve(request.path_info).route:
                return None
            
            return redirect("/signin/")
        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())