function searchUsersQueAnsByDate(user_id, type) {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    var selectedLength = $('#id_pageLength').val();
    var page_length = 10;
    if (selectedLength) {
        page_length = selectedLength;
    }
    if (start_date && end_date) {
        window.location = "/member_wise_que_ans/" + user_id + "/?page=1&length=" + page_length + "&start_date=" + start_date + "&end_date=" + end_date + "&type=" + type;
    } else {
        window.location = "/member_wise_que_ans/" + user_id + "/?page=1&length=" + page_length;
    }
}

function changePageLength(user_id, type) {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    var selectedLength = $('#id_pageLength').val();
    if (selectedLength) {
        var url = "";
        if(start_date && end_date) {
            url = "&start_date=" + start_date + "&end_date=" + end_date;
        }
        window.location = "/member_wise_que_ans/" + user_id + "/?page=1&length=" + selectedLength + url + "&type=" + type;
    } else {
        window.location = "/member_wise_que_ans/" + user_id + "/?page=1&length=10";
    }
}

function clearSearch(user_id, type) {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    if (start_date || end_date) {
        var selectedLength = $('#id_pageLength').val();
        var page_length = 10;
        if (selectedLength) {
            page_length = selectedLength;
        }
        window.location = "/member_wise_que_ans/" + user_id + "/?page=1&length=" + page_length + "&type=" + type;
    }
}

// $('.datepicker-cell').on("input change", function () {
//     console.log('//////////');
//     var start_date = $('#datepicker-range-start').val();
//     var end_date = $('#datepicker-range-end').val();
//     if (start_date || end_date) {
//         $('#id_clearSearch').show();
//     } else {
//         $('#id_clearSearch').hide();
//     }
// })

var step_id = 0;
var user_id = 0;
function editUserSteps(steps_id) {    
    axios({
        method: "get",
        url: "/get-usersteps/" + steps_id + "/",
        headers: { "Content-Type": "multipart/form-data" },
    })
    .then(function (response) {
        if (response.data.code == 0) {
            showMessage('appMsg', 'danger', response.data.msg, 10);
        } else {
            step_id = response.data.id;
            user_id = response.data.user_id;
            var steps_data = response.data.data;
            var data_length = steps_data.length;
            var body = ``;
            for (let index = 0; index < data_length; index++) {
                body += `
                        <div style="width: 66.666667%;">
                            <div class="mb-5 flex items-center">
                                <label style="width: 40%;" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white ">` + steps_data[index].date + `</label>
                                <input type="number" min="0" max="9999999999" name="user_steps" value="` + steps_data[index].steps +`" 
                                    oninput="validateStepsInput(this)" 
                                    onpaste="handleTextPaste(event)" 
                                    onkeypress="return isValidInput(event)" 
                                    class="inputField bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required="">
                            </div>
                        </div>
                        `
            }
            var title = "Update Steps";
            
            setTimeout(() => {
                $('#id_user_full_name').html("&nbsp-&nbsp" + response.data.user_full_name);
                $('#id_user_total_steps').html("&nbsp(&nbsp" + response.data.total_steps + "&nbsp)");
                $('#userStepsModalTitle').text(title);
                $('#userStepsModalBody').html(body);
                $('#userStepsStaticModal').click();
            }, 0);
        }
    })
    .catch(function (response) {
        showMessage('idUserStepsModalMsg', 'danger', 'Something went wrong!', 10);
    });
}
function validateStepsInput(input) {
    var errorMessage = input.nextElementSibling;
    if (input.value.length > 10) {
        input.value = input.value.slice(0, 10);
    } else {
        errorMessage.innerText = "";
    }
}

$('#userStepsModalBody').on('input change', 'input[name="user_steps"]', function(event) {
    var total_steps = 0;
    $('#userStepsModalBody input[name="user_steps"]').each(function() {
        var steps = $(this).val();
        if (steps > 0) {
            total_steps += parseInt(steps);
        }
    });
    if (total_steps > 0) {
        $('#id_user_total_steps').html("&nbsp(&nbsp" + total_steps + "&nbsp)");
    }
})

function saveUserStepsData() {
    var user_steps_data = [];
    var error_steps = false;
    $('#userStepsModalBody input[name="user_steps"]').each(function() {
        var date = $(this).siblings('label').text();
        var steps = $(this).val();
        if (steps < 0 || !steps) {
            error_steps = true;
            return;
        }
        user_steps_data.push({ date: date, steps: parseInt(steps) });
    });

    if (error_steps) {
        showMessage('idUserStepsModalMsg', 'info', 'Steps cannot be negative or blank.', 10);
        return;
    }

    var formData = new FormData();
    formData.append('user_steps_data', JSON.stringify(user_steps_data));
    formData.append('step_id', step_id);
    formData.append('user_id', user_id);
    formData.append('csrfmiddlewaretoken', $("input[name=csrfmiddlewaretoken]").val());
    axios({
        method: "post",
        url: "/update-usersteps/",
        data: formData,
        headers: { "Content-Type": "multipart/form-data" },
    })
    .then(function (response) {
        if (response.data.code == 0) {
            showMessage('idUserStepsModalMsg', 'danger', response.data.msg, 10);
        } else {
            window.location.href = "/member_wise_que_ans/" + response.data.user_id + "/?type=steps";
        }
    })
    .catch(function (response) {
        showMessage('idUserStepsModalMsg', 'danger', 'Something went wrong!', 10);
    });
}