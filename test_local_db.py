#!/usr/bin/env python3
"""
测试本地数据库连接
"""

import psycopg2

def test_local_database():
    """测试本地数据库连接"""
    
    # 本地数据库配置
    local_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'test',
        'user': 'postgres',
        'password': '8915841'
    }
    
    print("🔍 测试本地数据库连接...")
    print(f"📡 主机: {local_config['host']}")
    print(f"🔌 端口: {local_config['port']}")
    print(f"🗄️  数据库: {local_config['database']}")
    print(f"👤 用户: {local_config['user']}")
    print(f"🔑 密码: ***")
    
    try:
        print(f"\n⏳ 尝试连接本地数据库...")
        conn = psycopg2.connect(**local_config)
        print(f"✅ 本地数据库连接成功!")
        
        cursor = conn.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"📊 PostgreSQL版本: {version[0]}")
        
        # 检查数据库中的表
        print(f"\n🔍 检查数据库中的表...")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        if tables:
            print(f"📋 找到 {len(tables)} 个表:")
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table[0]};")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table[0]}: {count} 条记录")
        else:
            print(f"❌ 没有找到任何表")
            print(f"💡 这可能是一个空的数据库")
        
        # 检查是否有HKU相关的表
        hku_tables = [
            'question_userquestion',
            'question_question', 
            'auth_user',
            'family_familymembers',
            'family_familygroups',
            'family_category'
        ]
        
        print(f"\n🔍 检查HKU相关表...")
        found_hku_tables = []
        
        for table in hku_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} 条记录")
                found_hku_tables.append(table)
            except Exception as e:
                print(f"   ❌ {table}: 不存在")
        
        if found_hku_tables:
            print(f"\n🎉 找到HKU相关表! 可以进行数据导出")
            
            # 测试一个简单的quiz查询
            if 'question_userquestion' in found_hku_tables:
                try:
                    cursor.execute("""
                        SELECT COUNT(*) 
                        FROM question_userquestion 
                        WHERE answered = true
                    """)
                    quiz_count = cursor.fetchone()[0]
                    print(f"📊 已回答的quiz问题: {quiz_count}")
                    
                    if quiz_count > 0:
                        cursor.execute("""
                            SELECT MIN(date), MAX(date)
                            FROM question_userquestion 
                            WHERE answered = true
                        """)
                        min_date, max_date = cursor.fetchone()
                        print(f"📅 数据日期范围: {min_date} 到 {max_date}")
                        
                except Exception as e:
                    print(f"❌ Quiz查询失败: {e}")
        else:
            print(f"\n❌ 没有找到HKU相关表")
            print(f"💡 这可能不是HKU项目的数据库")
        
        cursor.close()
        conn.close()
        
        return found_hku_tables
        
    except psycopg2.OperationalError as e:
        print(f"❌ 本地数据库连接失败: {e}")
        print(f"\n🔧 可能的原因:")
        print(f"   1. PostgreSQL服务未启动")
        print(f"   2. 数据库'test'不存在")
        print(f"   3. 用户'postgres'密码错误")
        print(f"   4. 端口5432被占用或配置错误")
        return None
        
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return None

def create_local_config_file(has_hku_tables):
    """创建本地数据库配置文件"""
    
    if has_hku_tables:
        config_content = '''#!/usr/bin/env python3
"""
本地数据库配置 - 已验证可用
"""

LOCAL_DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'test',
    'user': 'postgres',
    'password': '8915841'
}

def get_local_db_config():
    """获取本地数据库配置"""
    return LOCAL_DB_CONFIG
'''
        
        with open('local_db_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"\n📁 本地数据库配置已保存到: local_db_config.py")
        print(f"💡 现在可以使用本地数据库进行导出了!")

def main():
    """主函数"""
    print("🔧 本地数据库连接测试")
    print("=" * 50)
    
    hku_tables = test_local_database()
    
    if hku_tables:
        create_local_config_file(True)
        print(f"\n🎉 本地数据库测试成功!")
        print(f"📋 下一步可以:")
        print(f"   1. 修改导出脚本使用本地数据库")
        print(f"   2. 运行数据导出")
        print(f"   3. 生成所需的CSV文件")
    else:
        print(f"\n❌ 本地数据库测试失败或没有HKU数据")

if __name__ == "__main__":
    main()
