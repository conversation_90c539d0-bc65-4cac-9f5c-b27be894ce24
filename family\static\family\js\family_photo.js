function searchFamilyPhotoByDate(family_group_id) {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    var selectedLength = $('#id_pageLength').val();
    var page_length = 10;
    if (selectedLength) {
        page_length = selectedLength;
    }
    if (start_date && end_date) {
        window.location = "/family-photos/" + family_group_id + "/?page=1&length=" + page_length + "&start_date=" + start_date + "&end_date=" + end_date;
    } else {
        window.location = "/family-photos/" + family_group_id + "/?page=1&length=" + page_length;
    }
}

function changePageLength(family_group_id) {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    var selectedLength = $('#id_pageLength').val();
    if (selectedLength) {
        var url = "";
        if(start_date && end_date) {
            url = "&start_date=" + start_date + "&end_date=" + end_date;
        }
        window.location = "/family-photos/" + family_group_id + "/?page=1&length=" + selectedLength + url;
    } else {
        window.location = "/family-photos/" + family_group_id + "/?page=1&length=10";
    }
}

function clearSearch(family_group_id) {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    if (start_date || end_date) {
        var selectedLength = $('#id_pageLength').val();
        var page_length = 10;
        if (selectedLength) {
            page_length = selectedLength;
        }
        window.location = "/family-photos/" + family_group_id + "/?page=1&length=" + page_length;
    }
}

function deleteWeeklyFamilyPhoto(id) {
    var msg = "Are you sure you want to delete this item? <br>This will deduct family score in leaderboard.";
    showConfirmationDialogue(msg, function (is_confirm) {
        if (is_confirm) {
            var formData = new FormData();
            formData.append('id', id);
            formData.append('csrfmiddlewaretoken', $("input[name=csrfmiddlewaretoken]").val());
            axios({
                method: "post",
                url: "/delete-weekly-family-photo/",
                data: formData,
                headers: { "Content-Type": "multipart/form-data" },
            })
            .then(function (response) {
                if (response.data.code == 0) {
                    $('#cancelBtnClick').click();
                    showMessage('appMsg', 'danger', response.data.msg, 10);
                } else {
                    window.location.href = "/family-photos/" + id +"/";
                }
            })
            .catch(function (response) {
                showMessage('appMsg', 'danger', 'Something went wrong!', 5);
            });
        }
    })
}