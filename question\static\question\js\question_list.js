function searchUsersQueAnsByDate() {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    var selectedLength = $('#id_pageLength').val();
    var page_length = 10;
    if (selectedLength) {
        page_length = selectedLength;
    }
    if (start_date && end_date) {
        window.location = "/question-list/?page=1&length=" + page_length + "&start_date=" + start_date + "&end_date=" + end_date;
    } else {
        window.location = "/question-list/?page=1&length=" + page_length;
    }
}

function changePageLength() {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    var selectedLength = $('#id_pageLength').val();
    if (selectedLength) {
        var url = "";
        if(start_date && end_date) {
            url = "&start_date=" + start_date + "&end_date=" + end_date;
        }
        window.location = "/question-list/?page=1&length=" + selectedLength + url;
    } else {
        window.location = "/question-list/?page=1&length=10";
    }
}

function clearSearch() {
    var start_date = $('#datepicker-range-start').val();
    var end_date = $('#datepicker-range-end').val();
    if (start_date || end_date) {
        var selectedLength = $('#id_pageLength').val();
        var page_length = 10;
        if (selectedLength) {
            page_length = selectedLength;
        }
        window.location = "/question-list/?page=1&length=" + page_length;
    }
}