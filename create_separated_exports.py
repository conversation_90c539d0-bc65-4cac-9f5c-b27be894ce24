#!/usr/bin/env python3
"""
创建分离的Diet和Exercise导出文件
基于我们已有的CSV文件
"""

import csv
import pandas as pd
from datetime import datetime

def separate_diet_exercise():
    """分离Diet和Exercise数据"""
    
    try:
        print("🔄 正在分离Diet和Exercise数据...")
        
        # 读取现有的CSV文件
        food_file = "hku_exercise_export_Food_20250716_215230.csv"
        sports_file = "hku_exercise_export_Sports_20250716_215318.csv"
        
        print(f"📖 读取饮食类别文件: {food_file}")
        food_df = pd.read_csv(food_file)
        
        print(f"📖 读取运动类别文件: {sports_file}")
        sports_df = pd.read_csv(sports_file)
        
        # 统计信息
        print(f"\n📊 数据统计:")
        print(f"   饮食类别记录数: {len(food_df)}")
        print(f"   运动类别记录数: {len(sports_df)}")
        
        # 只统计Regular questions的家庭组（排除Bonus questions）
        food_regular = food_df[food_df['Quiz Type'] != 'Bonus']
        sports_regular = sports_df[sports_df['Quiz Type'] != 'Bonus']

        food_families = food_regular['Family Name'].nunique()
        sports_families = sports_regular['Family Name'].nunique()

        print(f"   饮食类别家庭组数 (仅Regular): {food_families}")
        print(f"   运动类别家庭组数 (仅Regular): {sports_families}")

        # 显示家庭组列表（仅Regular questions）
        print(f"\n🏠 饮食类别家庭组 (仅Regular questions):")
        food_family_list = sorted(food_regular['Family Name'].unique())
        for i, family in enumerate(food_family_list, 1):
            print(f"   {i:2d}. {family}")

        print(f"\n🏃 运动类别家庭组 (仅Regular questions):")
        sports_family_list = sorted(sports_regular['Family Name'].unique())
        for i, family in enumerate(sports_family_list, 1):
            print(f"   {i:2d}. {family}")

        # 检查重叠
        food_set = set(food_family_list)
        sports_set = set(sports_family_list)
        overlap = food_set.intersection(sports_set)

        if overlap:
            print(f"\n⚠️  重叠的家庭组 (同时参与两个类别): {len(overlap)}个")
            for family in sorted(overlap):
                print(f"     {family}")
        else:
            print(f"\n✅ 没有重叠的家庭组，分离正确")
        
        # 转换为详细分数格式
        def convert_to_detailed_format(df, category_name):
            """转换为详细分数格式"""
            detailed_data = []
            
            # 按用户和日期分组
            grouped = df.groupby(['Answer Date', 'Family Name', 'Member Name'])
            
            for (date, family, member), group in grouped:
                # 计算各项分数
                checkin_score = group['Daily Checkin Score'].iloc[0] if 'Daily Checkin Score' in group.columns else 100
                quiz_score = group['Quiz Score'].sum()  # 所有问题的分数总和
                
                # 分离Regular和Bonus分数
                regular_quiz = group[group['Quiz Type'] != 'Bonus']['Question Score'].sum()
                bonus_quiz = group[group['Quiz Type'] == 'Bonus']['Question Score'].sum()
                
                # Food/Steps分数
                if category_name == 'Diet':
                    food_steps_score = group['Food Screenshot Score'].iloc[0] if 'Food Screenshot Score' in group.columns else 500
                else:
                    food_steps_score = group['Steps Screenshot Score'].iloc[0] if 'Steps Screenshot Score' in group.columns else 500
                
                # 家庭照片分数
                photo_score = group['Weekly Family Photo Score'].iloc[0] if 'Weekly Family Photo Score' in group.columns else 0
                
                # 总分
                total_score = checkin_score + regular_quiz + bonus_quiz + food_steps_score
                date_family_score = total_score + photo_score
                
                detailed_data.append({
                    'Date': date,
                    'Category': category_name,
                    'Family Group Name': family,
                    'Family Member': member,
                    'Checkin Score': checkin_score,
                    'Quiz Score': regular_quiz,
                    'Bonus Question': bonus_quiz,
                    'Food/Steps Score': food_steps_score,
                    'Family Photo Score': photo_score,
                    'Total Score': total_score,
                    'Date Family Group Score': date_family_score,
                    'Total Family Group Score': 0  # 需要从其他表获取
                })
            
            return detailed_data
        
        # 转换数据（只包含各自类别的家庭组）
        print(f"\n🔄 转换饮食类别数据...")
        # 只包含饮食类别的家庭组数据
        food_only_df = food_df[food_df['Family Name'].isin(food_family_list)]
        diet_detailed = convert_to_detailed_format(food_only_df, 'Diet')

        print(f"🔄 转换运动类别数据...")
        # 只包含运动类别的家庭组数据
        sports_only_df = sports_df[sports_df['Family Name'].isin(sports_family_list)]
        exercise_detailed = convert_to_detailed_format(sports_only_df, 'Exercise')
        
        # 生成CSV文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Diet文件
        diet_filename = f"hku_detailed_scores_Diet_{timestamp}.csv"
        fieldnames = [
            'Date', 'Category', 'Family Group Name', 'Family Member', 'Checkin Score', 
            'Quiz Score', 'Bonus Question', 'Food/Steps Score', 'Family Photo Score',
            'Total Score', 'Date Family Group Score', 'Total Family Group Score'
        ]
        
        with open(diet_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(diet_detailed)
        
        print(f"✅ Diet类别导出完成: {diet_filename}")
        print(f"📊 记录数: {len(diet_detailed)}")
        
        # Exercise文件
        exercise_filename = f"hku_detailed_scores_Exercise_{timestamp}.csv"
        
        with open(exercise_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(exercise_detailed)
        
        print(f"✅ Exercise类别导出完成: {exercise_filename}")
        print(f"📊 记录数: {len(exercise_detailed)}")
        
        print(f"\n🎉 分离完成!")
        print(f"📁 Diet文件: {diet_filename} ({food_families}个家庭组)")
        print(f"📁 Exercise文件: {exercise_filename} ({sports_families}个家庭组)")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    separate_diet_exercise()
