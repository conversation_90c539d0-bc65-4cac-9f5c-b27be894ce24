#!/usr/bin/env python3
"""
测试本地数据库的不同连接方式
"""

import psycopg2

def test_different_configs():
    """测试不同的数据库配置"""
    
    configs = [
        # 配置1: 原始配置
        {
            'name': '原始配置',
            'config': {
                'host': 'localhost',
                'port': 5432,
                'database': 'test',
                'user': 'postgres',
                'password': '8915841'
            }
        },
        # 配置2: 使用127.0.0.1
        {
            'name': '使用127.0.0.1',
            'config': {
                'host': '127.0.0.1',
                'port': 5432,
                'database': 'test',
                'user': 'postgres',
                'password': '8915841'
            }
        },
        # 配置3: 尝试postgres数据库
        {
            'name': '连接postgres数据库',
            'config': {
                'host': 'localhost',
                'port': 5432,
                'database': 'postgres',
                'user': 'postgres',
                'password': '8915841'
            }
        },
        # 配置4: 127.0.0.1 + postgres数据库
        {
            'name': '127.0.0.1 + postgres数据库',
            'config': {
                'host': '127.0.0.1',
                'port': 5432,
                'database': 'postgres',
                'user': 'postgres',
                'password': '8915841'
            }
        },
        # 配置5: 不指定密码（如果有信任认证）
        {
            'name': '无密码连接',
            'config': {
                'host': 'localhost',
                'port': 5432,
                'database': 'postgres',
                'user': 'postgres'
            }
        }
    ]
    
    for i, test_case in enumerate(configs, 1):
        print(f"\n🔄 测试配置 {i}: {test_case['name']}")
        config = test_case['config']
        
        print(f"   主机: {config['host']}")
        print(f"   端口: {config['port']}")
        print(f"   数据库: {config['database']}")
        print(f"   用户: {config['user']}")
        print(f"   密码: {'***' if 'password' in config else '无'}")
        
        try:
            conn = psycopg2.connect(**config)
            print(f"   ✅ 连接成功!")
            
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"   📊 版本: {version[0][:50]}...")
            
            # 列出所有数据库
            cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false;")
            databases = cursor.fetchall()
            print(f"   🗄️  可用数据库: {', '.join([db[0] for db in databases])}")
            
            cursor.close()
            conn.close()
            
            return config  # 返回第一个成功的配置
            
        except psycopg2.OperationalError as e:
            print(f"   ❌ 连接失败: {e}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    return None

def test_hku_database(working_config):
    """测试是否有HKU数据库"""
    
    print(f"\n🔍 检查是否有HKU相关数据库...")
    
    try:
        conn = psycopg2.connect(**working_config)
        cursor = conn.cursor()
        
        # 列出所有数据库
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false;")
        databases = [db[0] for db in cursor.fetchall()]
        
        print(f"📋 所有数据库: {', '.join(databases)}")
        
        # 检查可能的HKU数据库名
        possible_hku_dbs = ['hku', 'hku_staging', 'hku_production', 'student_quiz', 'quiz']
        
        for db_name in possible_hku_dbs:
            if db_name in databases:
                print(f"✅ 找到可能的HKU数据库: {db_name}")
                
                # 测试连接到这个数据库
                hku_config = working_config.copy()
                hku_config['database'] = db_name
                
                try:
                    hku_conn = psycopg2.connect(**hku_config)
                    hku_cursor = hku_conn.cursor()
                    
                    # 检查HKU相关表
                    hku_cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name LIKE '%question%' OR table_name LIKE '%family%'
                        ORDER BY table_name;
                    """)
                    
                    hku_tables = hku_cursor.fetchall()
                    if hku_tables:
                        print(f"   🎉 找到HKU相关表:")
                        for table in hku_tables:
                            hku_cursor.execute(f"SELECT COUNT(*) FROM {table[0]};")
                            count = hku_cursor.fetchone()[0]
                            print(f"     ✅ {table[0]}: {count} 条记录")
                        
                        hku_cursor.close()
                        hku_conn.close()
                        cursor.close()
                        conn.close()
                        
                        return hku_config
                    
                    hku_cursor.close()
                    hku_conn.close()
                    
                except Exception as e:
                    print(f"   ❌ 连接{db_name}失败: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
    
    return None

def main():
    """主函数"""
    print("🔧 本地数据库连接测试（多种配置）")
    print("=" * 60)
    
    # 测试不同配置
    working_config = test_different_configs()
    
    if working_config:
        print(f"\n🎉 找到可用的数据库配置!")
        
        # 检查HKU数据库
        hku_config = test_hku_database(working_config)
        
        if hku_config:
            print(f"\n🎉 找到HKU数据库!")
            print(f"📋 HKU数据库配置:")
            for key, value in hku_config.items():
                if key != 'password':
                    print(f"   {key}: {value}")
                else:
                    print(f"   {key}: ***")
            
            # 保存配置
            config_content = f'''# HKU本地数据库配置
HKU_LOCAL_CONFIG = {{
    'host': '{hku_config['host']}',
    'port': {hku_config['port']},
    'database': '{hku_config['database']}',
    'user': '{hku_config['user']}',
    'password': '{hku_config['password']}'
}}
'''
            
            with open('hku_local_config.py', 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print(f"\n📁 HKU配置已保存到: hku_local_config.py")
            print(f"💡 现在可以使用本地HKU数据库进行导出了!")
            
        else:
            print(f"\n❌ 没有找到HKU相关数据库")
            print(f"💡 可能需要导入HKU数据或使用其他数据库")
    else:
        print(f"\n❌ 所有数据库配置都失败了")
        print(f"💡 请检查PostgreSQL服务和认证设置")

if __name__ == "__main__":
    main()
