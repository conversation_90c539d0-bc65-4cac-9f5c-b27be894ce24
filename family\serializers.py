from rest_framework import serializers
from family.models import FamilyGroups, FamilyGroupFile ,WeeklyUploadFile

class FamilyGroupFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = FamilyGroupFile
        fields = ['file', 'file_name']

class FamilyGroupSerializer(serializers.ModelSerializer):
    # file = serializers.FileField(required=False, allow_null=True)
    # file_name = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = FamilyGroups
        fields = ['leader_id', 'family_id', 'family_name', 'category', 'icon_url']

    # def create(self, validated_data):
        # file_data = {'file': validated_data.pop('file', None),
        #     'file_name': validated_data.pop('file_name', None),}
        # family_group = FamilyGroups.objects.create(**validated_data)
        # if file_data['file']:
        #     FamilyGroupFile.objects.create(family_group=family_group, **file_data)
        # return family_group

class CreateFamilyGroupSerializer(serializers.Serializer):
    leader_id = serializers.IntegerField()
    family_group_name = serializers.CharField(max_length =200)

class WeeklyUploadFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = WeeklyUploadFile
        fields = ['id', 'family_group' ,'file', 'file_name','score']
        read_only_fields = ['id']