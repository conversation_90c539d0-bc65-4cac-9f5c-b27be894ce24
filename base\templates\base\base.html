<!DOCTYPE html>
{% load compress %}
{% load static %}
{% csrf_token %}
{% load i18n %}


<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans 'Home' %}{% endblock %}</title>

    <link href="{% static 'fontawesomefree/css/fontawesome.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'fontawesomefree/css/brands.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'fontawesomefree/css/solid.css' %}" rel="stylesheet" type="text/css">
    <!-- <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"> -->

    <script src="{% static 'base/js/jquery-3.7.1.min.js' %}?v=0.1"></script>
    <script src="{% static 'base/js/axios.min.js' %}?v=0.1"></script>
    <script src="{% static 'base/js/jquery-3.7.1.slim.min.js'%}?v=0.1"></script>

    {% compress css %}
    <link rel="stylesheet" href="{% static 'src/output.css' %}">
    {% endcompress %}
</head>

<style>
    .image-style{
        width: 45px;
    }
    .image-style:hover{
        position: relative;
        transition: transform .3s;
        transform: scale(5);
        z-index: 1;
        box-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    .tab-active{
        background-color: rgb(195 221 253 / var(--tw-bg-opacity));
        border-bottom: 2px solid rgb(28 100 242 / 100%);
        font-weight: 500;
    }
    input[type="search"]::-webkit-search-cancel-button {
        display: none;                 /* To hide default clear button in search bar */
    }

    .table-container {
        width: 100%;
        max-height: 620px; /* Adjust the height as needed */
        overflow-y: auto;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .thead th {
        position: sticky;
        top: 0;
        z-index: 1; /* Ensure the header is above the table content */
        --tw-bg-opacity: 1;
        background-color: rgb(195 221 253 / var(--tw-bg-opacity));
    }

    .bg-color {
        background-color: #eff5ff;
    }
    .bg-color-sidebar {
        background-color: #e1eeff;
    }
</style>

<body class="bg-color" id="body">    
    <div id="spinner" class="h-screen flex items-center justify-center">
        <div role="status">
            <svg aria-hidden="true" class="inline w-20 h-20 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
            </svg>
            <span class="sr-only">Loading...</span>
        </div>
    </div>

      {% include "base/navigation.html" %}
      {% include "base/sidebar.html" %}
      
      <main id="dataView" class="p-4 md:ml-64 h-auto" style="margin-top: 3.5rem;">

        {% include "base/alerts.html" %}
            <div id="appMsg">
            </div>
            <div id="content" class="antialiased bg-color dark:bg-gray-900">
            {% block content %}
            
            {% endblock content %}
            </div>
         
          </main>
    <!-- </div> -->

    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.js"></script>
    <script src="{% static 'fontawesomefree/js/all.min.js' %}"></script>
    <script src="{% static 'base/js/base.js'%}?v=0.4"></script>
    
    <script>
        $('.menu-link[href="' + window.location.pathname + '"]').removeClass('bg-gray-200 text-blue-500').addClass('bg-blue-200 shadow-lg border-l-4 border-blue-600');
        $('.menu-link[href="' + window.location.pathname + '"]').find('svg').removeClass('text-blue-500');

        document.addEventListener("DOMContentLoaded", function() {
          // Example of showing spinner during AJAX requests
          // Replace with your actual AJAX function or loading logic
          document.querySelector('#spinner').classList.remove('hidden'); // Show spinner
          document.querySelector('#content').classList.add('hidden');
          setTimeout(function() {
            document.querySelector('#spinner').classList.add('hidden'); // Hide spinner after delay (simulating content load)
            document.querySelector('#content').classList.remove('hidden');
          }, 1); // Replace 3000 with actual delay in milliseconds
        });

        $('#menuBtn').on("click", function() {
            {% comment %} document.getElementById('drawer-navigation').classList.toggle("md:translate-x-0");
            document.getElementById('menuBtn').classList.toggle("md:ml-64");
            document.getElementById('dataView').classList.toggle("md:ml-64"); {% endcomment %}

            var drawerNavigation = document.getElementById('drawer-navigation');
            var menuButton = document.getElementById('menuBtn');
            var dataView = document.getElementById('dataView');
            
            // Check the screen width to determine if it's a mobile or larger screen
            if (window.innerWidth >= 768) {
                // For screens md: and larger (>=768px), toggle the md:translate-x-0 class
                drawerNavigation.classList.toggle("md:translate-x-0");
                menuButton.classList.toggle("md:ml-64");
                dataView.classList.toggle("md:ml-64");
                
            } else {
                // For mobile screens (<768px), toggle the -translate-x-full class
                drawerNavigation.classList.toggle("-translate-x-full");
                if (menuButton.classList.contains('ml-64')) {
                    menuButton.classList.remove("ml-64");
                    {% comment %} dataView.classList.remove("ml-64"); {% endcomment %}
                } else {
                    menuButton.classList.add("ml-64");
                    {% comment %} dataView.classList.add("ml-64"); {% endcomment %}
                }
            }
        })
      </script>
</body>

</html>