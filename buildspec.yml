version: 0.2

environment_variables:
  plaintext:
    DJANGO_SETTINGS_MODULE: core.settings
    DJANGO_SECRET_KEY: nosecret
    ALLOWED_HOSTS: '*'

actions:
  variables:
    namespace: .env

phases:
  pre_build:
    commands:
      - pip install --upgrade pip wheel setuptools
      - pip install -r requirements.txt
      
  post_build:
    commands:
      - echo Build completed on `date`



artifacts:
  files:
    - '**/*'
