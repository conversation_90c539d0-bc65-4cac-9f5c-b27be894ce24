from django.contrib import admin
from user_authentication.models import UserProfile ,PhoneNumOtp ,UserCheckin
from django.contrib.auth.models import User


admin.site.unregister(User)
class AdminUserModel(admin.ModelAdmin):
    list_display = ['username', 'email', 'first_name', 'last_name', 'is_superuser', 'is_staff', 'is_active', 'is_authenticated']
    list_filter = ('is_staff', 'is_superuser')
    search_fields = ['username', 'email', 'first_name', 'last_name']

admin.site.register(User, AdminUserModel)

# Register your models here.

@admin.register(UserProfile)
class AdminUserProfileModel(admin.ModelAdmin):
    list_display = ['user','country_code','phone_number','role','age_group']


@admin.register(PhoneNumOtp)
class AdminPhoneNumOtpModel(admin.ModelAdmin):
    list_display = ['country_code','phone_number', 'email','otp','otp_expiry']

@admin.register(UserCheckin)
class AdminUserCheckinModel(admin.ModelAdmin):
    list_display = ['user','created_on']
