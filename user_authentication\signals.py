from django.db.models.signals import post_save
from django.contrib.auth.models import User
from django.dispatch import receiver
from .models import UserProfile

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        # 创建用户资料
        UserProfile.objects.create(user=instance)
    try:
        # 确保用户资料存在
        profile = UserProfile.objects.get(user=instance)
        # 如果用户名看起来像手机号码，自动设置手机号码
        if instance.username and len(instance.username) == 8 and instance.username.isdigit():
            if not profile.phone_number:
                profile.phone_number = instance.username
                profile.country_code = "+852"  # 默认国家代码
                profile.save()
    except UserProfile.DoesNotExist:
        # 如果用户资料不存在，创建一个
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    try:
        instance.userprofile.save()
    except Exception:
        # 如果保存失败，尝试重新创建
        UserProfile.objects.get_or_create(user=instance)
