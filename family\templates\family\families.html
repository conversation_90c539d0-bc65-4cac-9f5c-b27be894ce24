{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Families' %}{% endblock %}
{% block content %}
{% load static %}

{% if msg %}
<div id="idFamilyMsg" class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
    <span class="font-medium">{{ msg }}</span>
    <span onclick="closeMsg('idFamilyMsg')">
        <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </span>
</div>
{% endif %}
 
<div class="p-4">
    <div class="mb-3 justify-between flex">
        <h1 class="text-2xl font-bold mb-4">{% trans 'Families' %}</h1>
       <div class="content-center">
            <a class="text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg" href="/export-familydata/?field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" >{% trans 'Export Families Breakdown'%}</a>
        </div> 
    </div>
 
    <div class="flex flex-col md:flex-row w-full mx-auto mb-3 justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
 
        <!-- Page length dropdown -->
        <div id="dropdown" class="flex items-center space-x-2 z-10 w-full md:w-auto">
 
            <span>Show</span>
                <select id="id_pageLength" name="page_length" onchange="changePageLength()" class="shadow-lg w-fit bg-white hover:bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>
 
            <div class="relative flex flex-wrap items-center w-full md:max-w-md">
                <select id="id_userField" name="user_field" class="w-full shadow-lg bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 md:w-auto mr-px mb-2 md:mb-0 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 rounded-l-lg">
                    <option value="">Select Field</option>
                    <!-- <option value="Family ID">Family ID</option> -->
                    <option value="Leader Name">Leader Name</option>
                    <option value="Family Name">Family Name</option>
                    <!-- <option value="Category">Category</option> -->
                    <!-- <option value="Is Quiz Start">Is Quiz Start</option> -->
                </select>
                
                <input type="search" id="searchKeyword" value="{{ search_keyword }}" name="search_with_field" class="flex-grow bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 mr-px shadow-lg w-full md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500" placeholder="Select the field then type..." required />
                <button type="button" id="id_clearSearch" onclick="clearSearch()" title="Clear" class="{% if not search_field or not search_keyword %} hidden {% endif %} p-2.5 mb-2 md:mb-0 text-sm font-medium text-gray-600 bg-transparent border-none cursor-pointer focus:outline-none focus:ring-0 md:rounded-none">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 6l8 8M14 6l-8 8" />
                    </svg>
                    <span class="sr-only">Clear</span>
                </button>

                <!-- Search Button -->
                <button type="button" id="id_searchButton" onclick="searchFamily()" title="Search" class="shadow-lg p-2.5 text-sm font-medium text-white bg-blue-500 rounded-r-lg hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 border border-gray-300">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                    <span class="sr-only">Search</span>
                </button>
 
            </div>
        </div>
 
        <div class="relative w-full shadow-lg rounded-lg overflow-x-auto sm:rounded-lg">
            <div class="table-container">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                    <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                        <tr>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'created_on' and request.GET.order == 'asc' %}&sort_by=created_on&order=desc{% else %}&sort_by=created_on&order=asc{% endif %}">
                                    {% trans 'Created On' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'created_on' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'created_on' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            
                            <!-- <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'family_id' and request.GET.order == 'asc' %}&sort_by=family_id&order=desc{% else %}&sort_by=family_id&order=asc{% endif %}">
                                    {% trans 'Family ID' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_id' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_id' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th> -->
    
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Family Group Icon' %}
                                </div>
                            </th>

                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Leader Name' %}
                                </div>
                            </th>
                           
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'family_name' and request.GET.order == 'asc' %}&sort_by=family_name&order=desc{% else %}&sort_by=family_name&order=asc{% endif %}">
                                    {% trans 'Family Name' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_name' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_name' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
     
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'category_name' and request.GET.order == 'asc' %}&sort_by=category_name&order=desc{% else %}&sort_by=category_name&order=asc{% endif %}">
                                    {% trans 'Category' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'category_name' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'category_name' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
    
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'is_quiz_starts' and request.GET.order == 'asc' %}&sort_by=is_quiz_starts&order=desc{% else %}&sort_by=is_quiz_starts&order=asc{% endif %}">
                                    {% trans 'Is Quiz Start' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'is_quiz_starts' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'is_quiz_starts' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'View Members' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Weekly Photos' %}
                                </div>
                            </th>
                        </tr>
                    </thead>
     
                    <tbody>
                        {% if family_details %}
                        {% for family_detail in family_details %}
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td class="px-6 py-4">
                                {{ family_detail.created_on }}
                            </td>
                            <td class="px-6 py-4">
                                {% if family_detail.family_icon %}
                                <div>
                                    <img src="{{family_detail.family_icon}}" class="image-style rounded border" alt="">
                                </div>
                                {% endif %}
                            </td>
                            <!-- <td class="px-6 py-4">
                                {{ family_detail.family_id }}
                            </td> -->
                            <td class="px-6 py-4">
                                {{ family_detail.leader_id__username }}
                            </td>
                            <td class="px-6 py-4">
                                {{ family_detail.family_name }}
                            </td>
                            <td class="px-6 py-4">
                                {{ family_detail.category_name }}
                            </td>
                            <td class="px-6 py-4">
                                <div {% if family_detail.is_quiz_starts == 'True' %}  class="w-fit pl-2 pr-2 bg-green-100 text-green-900 rounded-md" {% else %} class="w-fit pl-2 pr-2 bg-red-100 text-red-900 rounded-md" {% endif %}>
                                    {{ family_detail.is_quiz_starts }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex">
                                    <a href="/familymember/{{ family_detail.id }}" title="View">
                                        <svg class="w-6 h-6 text-blue-500 hover:text-blue-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                                            <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                            <path stroke="none" d="M0 0h24v24H0z"/>
                                            <line x1="4" y1="7" x2="20" y2="7" />
                                            <line x1="10" y1="11" x2="10" y2="17" />
                                            <line x1="14" y1="11" x2="14" y2="17" />
                                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12" />
                                            <path d="M9 7v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3" />
                                        </svg>
                                    </button>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex">
                                    <a href="/family-photos/{{ family_detail.id }}" title="View">
                                        <svg class="w-6 h-6 text-blue-500 hover:text-blue-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                                            <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                            <path stroke="none" d="M0 0h24v24H0z"/>
                                            <line x1="4" y1="7" x2="20" y2="7" />
                                            <line x1="10" y1="11" x2="10" y2="17" />
                                            <line x1="14" y1="11" x2="14" y2="17" />
                                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12" />
                                            <path d="M9 7v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3" />
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
    
                        {% endfor %}
                        {% else %}
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="8" class="px-6 py-4 text-base">Data is not available.</td></tr>
                        {% endif %}
                        
                    </tbody>           
                </table>
            </div>
            <nav class="flex flex-col bg-gray-50 md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4" aria-label="Table navigation">
                <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                    Showing
                    <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
                    of
                    <span class="font-semibold text-gray-900 dark:text-white">{{ total_items }}</span>
                </span>
                <ul class="inline-flex items-stretch -space-x-px">
                    {% if page_obj.has_previous %}
                    <li>
                        <a href="?page={{ page_obj.previous_page_number }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <span class="sr-only">Previous</span>
                            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                    {% endif %}
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li>
                            <a
                            class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                {{ num }}
                            </a>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %} <li>
                            <a href="?page={{ num }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}"
                            class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                {{ num }}
                            </a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    {% if page_obj.has_next %}
                    <li>
                        <a href="?page={{ page_obj.next_page_number }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <span class="sr-only">Next</span>
                            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        </div>
    </div>
    </div>

<script src="{% static 'family/js/families.js'%}?v=0.4"></script>
<script>
 
    $(document).ready(function() {
        msg = '{{ msg }}';
        if (msg) {
            setTimeout(() => {
                $('#idFamilyMsg').hide();
            }, 10000);
        }
 
        var dropdown = document.getElementById('id_userField');
        var search_field = '{{ search_field }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === search_field) {
                dropdown.options[i].selected = true;
                break;
            }
        }
 
        var dropdown = document.getElementById('id_pageLength');
        var page_length = '{{ page_length }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === page_length) {
                dropdown.options[i].selected = true;
                break;
            }
        }
    });
 
</script>
 
{% endblock content %}
 