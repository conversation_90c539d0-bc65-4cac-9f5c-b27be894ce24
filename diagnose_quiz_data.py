#!/usr/bin/env python3
"""
诊断quiz数据的完整情况
"""

import psycopg2
from datetime import datetime, <PERSON><PERSON><PERSON>

def diagnose_quiz_data():
    """诊断quiz数据"""
    
    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }
    
    try:
        print("🔍 诊断quiz数据完整情况...")
        
        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 1. 检查总的quiz questions数量
        print("\n📊 总体quiz questions统计:")
        total_query = """
        SELECT 
            COUNT(*) as total_questions,
            COUNT(DISTINCT uq.user_id) as unique_users,
            COUNT(DISTINCT fg.id) as unique_families
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        """
        
        cursor.execute(total_query)
        total_count, unique_users, unique_families = cursor.fetchone()
        print(f"   总quiz questions: {total_count}")
        print(f"   参与用户数: {unique_users}")
        print(f"   参与家庭组数: {unique_families}")
        
        # 2. 按quiz_type分类统计
        print(f"\n📋 按quiz_type分类:")
        type_query = """
        SELECT 
            q.quiz_type,
            COUNT(*) as question_count,
            COUNT(DISTINCT uq.user_id) as unique_users
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        GROUP BY q.quiz_type
        ORDER BY q.quiz_type
        """
        
        cursor.execute(type_query)
        type_results = cursor.fetchall()
        for quiz_type, count, users in type_results:
            print(f"   {quiz_type}: {count} questions, {users} users")
        
        # 3. 检查日期范围（使用正确的date字段）
        print(f"\n📅 日期范围分析 (使用date字段):")
        date_query = """
        SELECT 
            MIN(uq.date) as min_date,
            MAX(uq.date) as max_date,
            COUNT(DISTINCT uq.date) as unique_dates
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        """
        
        cursor.execute(date_query)
        min_date, max_date, unique_dates = cursor.fetchone()
        print(f"   最早日期: {min_date}")
        print(f"   最晚日期: {max_date}")
        print(f"   总天数: {unique_dates}")
        
        # 4. 检查最近30天的数据
        print(f"\n⏰ 最近30天数据:")
        recent_30_days = datetime.now().date() - timedelta(days=30)
        recent_query = """
        SELECT 
            COUNT(*) as recent_questions,
            COUNT(DISTINCT uq.user_id) as recent_users,
            COUNT(DISTINCT fg.id) as recent_families
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND uq.date >= %s
        """
        
        cursor.execute(recent_query, (recent_30_days,))
        recent_count, recent_users, recent_families = cursor.fetchone()
        print(f"   最近30天questions: {recent_count}")
        print(f"   最近30天用户: {recent_users}")
        print(f"   最近30天家庭组: {recent_families}")
        
        # 5. 检查created_at vs date字段的差异
        print(f"\n🔄 created_at vs date字段对比:")
        comparison_query = """
        SELECT 
            'created_at' as field_type,
            MIN(uq.created_at::date) as min_date,
            MAX(uq.created_at::date) as max_date,
            COUNT(DISTINCT uq.created_at::date) as unique_dates
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        
        UNION ALL
        
        SELECT 
            'date' as field_type,
            MIN(uq.date) as min_date,
            MAX(uq.date) as max_date,
            COUNT(DISTINCT uq.date) as unique_dates
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        """
        
        cursor.execute(comparison_query)
        comparison_results = cursor.fetchall()
        for field_type, min_d, max_d, unique_d in comparison_results:
            print(f"   {field_type}字段: {min_d} 到 {max_d} ({unique_d} 天)")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_quiz_data()
