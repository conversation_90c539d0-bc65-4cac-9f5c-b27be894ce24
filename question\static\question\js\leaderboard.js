function searchLeaderboardList(type) {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    var selectedLength = $('#id_pageLength').val();
    var page_length = 10;
    if (selectedLength) {
        page_length = selectedLength;
    }
    if (selected_field && search_keyword) {
        window.location = "/leaderboard/?page=1&length=" + page_length + "&field=" + selected_field + "&search=" + search_keyword + "&type=" + type;
    } else {
        window.location = "/leaderboard/?page=1&length=" + page_length;
    }
}

$('#searchKeyword').on("keyup", function (e) {
    if (e.keyCode == 13) {
        e.preventDefault();
        const urlParams = new URLSearchParams(window.location.search);
        var typeParam = urlParams.get('type');
        if (typeParam && typeParam !== 'undefined') {
            searchLeaderboardList(typeParam);
        } else {
            searchLeaderboardList("food");
        }
    }
})

function changePageLength(type) {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    var selectedLength = $('#id_pageLength').val();
    if (selectedLength) {
        window.location = "/leaderboard/?page=1&length=" + selectedLength + "&field=" + selected_field + "&search=" + search_keyword + "&type=" + type;
    } else {
        window.location = "/leaderboard/?page=1&length=10";
    }
}

function clearSearch (type) {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    if (selected_field || search_keyword) {
        var selectedLength = $('#id_pageLength').val();
        var page_length = 10;
        if (selectedLength) {
            page_length = selectedLength;
        }
        window.location = "/leaderboard/?page=1&length=" + page_length + "&type=" + type;
    }
}

$('#searchKeyword').keyup(function () {
    var search_keyword = $('#searchKeyword').val();
    if (search_keyword) {
        $('#id_clearSearch').show();
    } else {
        $('#id_clearSearch').hide();
    }
})