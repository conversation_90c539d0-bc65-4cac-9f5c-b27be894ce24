#!/usr/bin/env python3
"""
测试新密码的本地数据库连接
"""

import psycopg2

def test_with_new_password():
    """使用新密码测试数据库连接"""
    
    configs = [
        # 配置1: localhost + postgres数据库
        {
            'name': 'localhost + postgres',
            'config': {
                'host': 'localhost',
                'port': 5432,
                'database': 'postgres',
                'user': 'postgres',
                'password': '8915841@@'
            }
        },
        # 配置2: 127.0.0.1 + postgres数据库
        {
            'name': '127.0.0.1 + postgres',
            'config': {
                'host': '127.0.0.1',
                'port': 5432,
                'database': 'postgres',
                'user': 'postgres',
                'password': '8915841@@'
            }
        },
        # 配置3: localhost + test数据库
        {
            'name': 'localhost + test',
            'config': {
                'host': 'localhost',
                'port': 5432,
                'database': 'test',
                'user': 'postgres',
                'password': '8915841@@'
            }
        },
        # 配置4: 127.0.0.1 + test数据库
        {
            'name': '127.0.0.1 + test',
            'config': {
                'host': '127.0.0.1',
                'port': 5432,
                'database': 'test',
                'user': 'postgres',
                'password': '8915841@@'
            }
        }
    ]
    
    print("🔍 使用新密码 8915841@@ 测试连接...")
    
    for i, test_case in enumerate(configs, 1):
        print(f"\n🔄 测试配置 {i}: {test_case['name']}")
        config = test_case['config']
        
        try:
            conn = psycopg2.connect(**config)
            print(f"   ✅ 连接成功!")
            
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"   📊 PostgreSQL版本: {version[0][:60]}...")
            
            # 列出所有数据库
            cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname;")
            databases = cursor.fetchall()
            db_list = [db[0] for db in databases]
            print(f"   🗄️  可用数据库: {', '.join(db_list)}")
            
            # 检查当前数据库的表
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """)
            tables = cursor.fetchall()
            
            if tables:
                print(f"   📋 当前数据库的表 ({len(tables)}个):")
                for table in tables[:10]:  # 只显示前10个
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table[0]};")
                        count = cursor.fetchone()[0]
                        print(f"     ✅ {table[0]}: {count} 条记录")
                    except:
                        print(f"     ❌ {table[0]}: 无法访问")
                
                if len(tables) > 10:
                    print(f"     ... 还有 {len(tables) - 10} 个表")
            else:
                print(f"   📋 当前数据库没有表")
            
            # 检查是否有HKU相关表
            hku_tables = ['question_userquestion', 'question_question', 'auth_user', 'family_familymembers', 'family_familygroups', 'family_category']
            found_hku = []
            
            for hku_table in hku_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {hku_table};")
                    count = cursor.fetchone()[0]
                    found_hku.append((hku_table, count))
                except:
                    pass
            
            if found_hku:
                print(f"   🎉 找到HKU相关表:")
                for table, count in found_hku:
                    print(f"     ✅ {table}: {count} 条记录")
            
            cursor.close()
            conn.close()
            
            return config, found_hku  # 返回成功的配置和HKU表信息
            
        except psycopg2.OperationalError as e:
            print(f"   ❌ 连接失败: {e}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    return None, None

def check_other_databases(working_config):
    """检查其他数据库是否有HKU数据"""
    
    print(f"\n🔍 检查其他数据库是否有HKU数据...")
    
    try:
        # 先连接到postgres数据库获取所有数据库列表
        postgres_config = working_config.copy()
        postgres_config['database'] = 'postgres'
        
        conn = psycopg2.connect(**postgres_config)
        cursor = conn.cursor()
        
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname;")
        databases = [db[0] for db in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        print(f"📋 检查数据库: {', '.join(databases)}")
        
        # 检查每个数据库
        for db_name in databases:
            if db_name in ['postgres', 'template0', 'template1']:
                continue
                
            print(f"\n🔍 检查数据库: {db_name}")
            
            try:
                test_config = working_config.copy()
                test_config['database'] = db_name
                
                db_conn = psycopg2.connect(**test_config)
                db_cursor = db_conn.cursor()
                
                # 检查HKU相关表
                hku_tables = ['question_userquestion', 'question_question', 'auth_user', 'family_familymembers', 'family_familygroups']
                found_tables = []
                
                for table in hku_tables:
                    try:
                        db_cursor.execute(f"SELECT COUNT(*) FROM {table};")
                        count = db_cursor.fetchone()[0]
                        found_tables.append((table, count))
                    except:
                        pass
                
                if found_tables:
                    print(f"   🎉 找到HKU表:")
                    total_records = 0
                    for table, count in found_tables:
                        print(f"     ✅ {table}: {count} 条记录")
                        total_records += count
                    
                    if total_records > 0:
                        print(f"   📊 总记录数: {total_records}")
                        
                        # 检查quiz数据的日期范围
                        if any(table[0] == 'question_userquestion' for table in found_tables):
                            try:
                                db_cursor.execute("""
                                    SELECT MIN(date), MAX(date), COUNT(*)
                                    FROM question_userquestion 
                                    WHERE answered = true
                                """)
                                min_date, max_date, quiz_count = db_cursor.fetchone()
                                print(f"   📅 Quiz数据: {min_date} 到 {max_date} ({quiz_count} 条)")
                                
                                db_cursor.close()
                                db_conn.close()
                                
                                return test_config  # 返回找到HKU数据的配置
                            except:
                                pass
                else:
                    print(f"   ❌ 没有HKU表")
                
                db_cursor.close()
                db_conn.close()
                
            except Exception as e:
                print(f"   ❌ 连接失败: {e}")
    
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
    
    return None

def main():
    """主函数"""
    print("🔧 使用新密码测试本地数据库")
    print("🔑 密码: 8915841@@")
    print("=" * 50)
    
    # 测试连接
    working_config, hku_tables = test_with_new_password()
    
    if working_config:
        print(f"\n🎉 数据库连接成功!")
        
        if hku_tables:
            print(f"✅ 在当前数据库找到HKU数据!")
            
            # 保存配置
            config_content = f'''# HKU本地数据库配置 - 已验证
HKU_LOCAL_CONFIG = {{
    'host': '{working_config['host']}',
    'port': {working_config['port']},
    'database': '{working_config['database']}',
    'user': '{working_config['user']}',
    'password': '{working_config['password']}'
}}
'''
            
            with open('hku_local_config.py', 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print(f"\n📁 配置已保存到: hku_local_config.py")
            print(f"🚀 现在可以使用本地数据库进行HKU数据导出了!")
            
        else:
            print(f"❌ 当前数据库没有HKU数据，检查其他数据库...")
            hku_config = check_other_databases(working_config)
            
            if hku_config:
                print(f"\n🎉 在其他数据库找到HKU数据!")
                
                # 保存配置
                config_content = f'''# HKU本地数据库配置 - 已验证
HKU_LOCAL_CONFIG = {{
    'host': '{hku_config['host']}',
    'port': {hku_config['port']},
    'database': '{hku_config['database']}',
    'user': '{hku_config['user']}',
    'password': '{hku_config['password']}'
}}
'''
                
                with open('hku_local_config.py', 'w', encoding='utf-8') as f:
                    f.write(config_content)
                
                print(f"\n📁 HKU配置已保存到: hku_local_config.py")
                print(f"🚀 现在可以使用本地HKU数据库进行导出了!")
            else:
                print(f"\n❌ 没有找到HKU数据")
                print(f"💡 可能需要导入HKU数据或检查其他数据库实例")
    else:
        print(f"\n❌ 所有连接尝试都失败了")
        print(f"💡 请检查:")
        print(f"   1. PostgreSQL服务是否运行")
        print(f"   2. 密码是否正确")
        print(f"   3. 用户权限设置")

if __name__ == "__main__":
    main()
