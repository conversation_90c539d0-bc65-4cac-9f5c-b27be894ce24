import secrets, string, random, traceback, json ,os, csv
from django.conf import settings
from django.db.models import Q
from django.contrib.auth.models import User
from rest_framework import status
from datetime import datetime ,timedelta
from rest_framework.response import Response 
from rest_framework.views import APIView
from user_authentication.models import UserProfile
from family.models import FamilyGroups, Category, FamilyMembers, FamilyGroupFile, WeeklyUploadFile
from .serializers import FamilyGroupSerializer, CreateFamilyGroupSerializer ,WeeklyUploadFileSerializer
from base.views import create_from_exceptions
from django.shortcuts import render
from rest_framework_simplejwt.tokens import RefreshToken
import pandas as pd
from base import utils, storage_backends
from django.http import HttpResponse, FileResponse
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import authenticate,login ,logout
from django.db import transaction
from base.utils import custom_data_response, custom_error_response, get_leaderboard
from decouple import config
from question.models import Leaderboard, UserQuestion, QuizDates
from django.http import StreamingHttpResponse

class FamilyGroupCreate(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    categories = ['Food', 'Sports']
    #queryset = FamilyGroups.objects.none()
    #serializer_class = CreateFamilyGroupSerializer
    http_method_names = ['post', 'put', 'patch']
    
    def generate_unique_family_id(self):
        while True:
            family_id = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(6))
            if not FamilyGroups.objects.filter(family_id=family_id).exists():
                return family_id
                
    action_serializers = {
        'create_family':CreateFamilyGroupSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)
    
    @action(methods=['post'], detail=False)
    def create_family(self, request):
        try: 
            with transaction.atomic():
                data = request.data
                leader_id = request.data.get('leader_id')
                family_name = request.data.get('family_group_name')
                icon_url = request.data.get('family_group_photo')

                required_fields = {
                    'leader_id' : 'Leader Id' ,
                    'family_group_name' : 'Family Group Name' ,
                    'family_group_photo' : 'Family Group Photo',
                }
                missing_fields = [value for key,value in required_fields.items() if not data.get(key)]
                if missing_fields:
                    message = utils.get_required_msg(missing_fields)
                    return Response({
                        "success": False,
                        'message': message}, status=status.HTTP_400_BAD_REQUEST)

                # if file and not utils.validate_file_extension(file.name):
                #     return utils.custom_error_response(message='Family Group Photo must be in jpg, jpeg, heic, png.')
                
                if not str(leader_id).isdigit():
                    return utils.custom_error_response(message='Please enter only valid numeric value for leader id.')
                
                leader_id = int(leader_id)
                leader_user = User.objects.filter(id=leader_id).values("id").first()
                if not leader_user:
                    return utils.custom_error_response(message='Leader ID does not exist')
                
                if FamilyGroups.objects.filter(family_name=family_name).exists():
                    return utils.custom_error_response(message ='Family group name already exists. Please choose a different name.')
                if FamilyGroups.objects.filter(leader_id=leader_id).exists():
                    return utils.custom_error_response(message='The leader already has a family group.')

                family_id = self.generate_unique_family_id()
                # category_name = random.choice(self.categories)
                user_category = UserProfile.objects.filter(user_id=leader_id, is_deleted=False).values("family_group").first()
                category_name = user_category["family_group"] if user_category else ""
                category, created = Category.objects.get_or_create(name=category_name)

                data = {
                    'leader_id': leader_user["id"],
                    'family_id': family_id,
                    'family_name': family_name,
                    'category': category.id,
                    'icon_url': icon_url,
                    # 'file': file,
                    # 'file_name': file.name if file else None
                    }
                
                serializer = FamilyGroupSerializer(data=data) 
                if serializer.is_valid():
                    family_group = serializer.save()
                    response_data = serializer.data 
                    
                    # family_group_file = FamilyGroupFile.objects.filter(family_group=family_group).first()
                    # base64_with_prefix = ""
                    
                    # file_path = family_group_file.file.url if family_group_file else ""

                    # file_name = family_group_file["file_name"]
                    # bucket_name = settings.MEDIA_BUCKET_NAME
                    # object_name = f"{family_group.id}/{file_name}"
                    # is_bucket = storage_backends.create_bucket(bucket_name)
                    # if is_bucket:   
                    #     is_file_upload = storage_backends.upload_file(file_path, bucket_name, object_name)
                    #     if is_file_upload:
                    #         base64_with_prefix = storage_backends.get_file(bucket_name, object_name)

                    FamilyMembers.objects.create(family_group=family_group, member_id=leader_user["id"], is_member=True, is_leader=True)

                    response_data.update({  
                        'category': category_name,
                        # 'family_group_photo': file_path 
                        })
                    return utils.custom_data_response(data=response_data, message='Family group created successfully')

                return utils.custom_error_response(message=serializer.errors)
        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return utils.custom_error_response(message="Something went wrong.", status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['patch'], detail=False, url_path=r"update_family/(?P<leader_id>\d+)")
    def update_family(self, request, leader_id):
        try:
            data = request.data
            new_family_name = request.data.get('family_group_name')
            icon_url = request.data.get('family_group_photo')

            required_fields = {
                'family_group_name' : 'Family Group Name' ,
                'family_group_photo' : 'Family Group Photo',
            }
            
            missing_fields = [value for key, value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message}, status=status.HTTP_400_BAD_REQUEST)
            
            if leader_id and not User.objects.filter(id=leader_id).exists():
               return utils.custom_error_response(message='Leader ID does not exist.')
            
            family_group = FamilyGroups.objects.filter(leader_id=leader_id).first()
            if not family_group or family_group.is_quiz_starts:
                return Response({"success": False,'message': 'Family group not found.' if not family_group else 
                                 'Family Group cannot be updated after the quiz has started.'}, status=status.HTTP_404_NOT_FOUND 
                                 if not family_group else status.HTTP_400_BAD_REQUEST)
            
            if new_family_name:
                if new_family_name != family_group.family_name:
                    if FamilyGroups.objects.filter(family_name=new_family_name).exclude(id=family_group.id).exists():
                        return utils.custom_error_response(message='Family group name already exists. Please choose a different name.')
                family_group.family_name = new_family_name
                family_group.icon_url = icon_url
            
            # if new_file:
            #     if not utils.validate_file_extension(new_file.name):
            #         return utils.custom_error_response(message='Family Group Photo must be in jpg, jpeg, heic, png.')
            #     family_group_file = FamilyGroupFile.objects.filter(family_group_id=family_group.id).first()
            #     if family_group_file:
            #         family_group_file.file = new_file
            #         family_group_file.file_name = str(new_file.name) 
            #         family_group_file.save()

            family_group.save()
            category_name = family_group.category.name
            response_data = FamilyGroupSerializer(family_group).data

            # family_grp_file = FamilyGroupFile.objects.filter(family_group=family_group).first()
            # file_path = ""
            # if family_grp_file:
            #     file_path = family_grp_file.file.url

            # file_name = family_grp_file["file_name"]
            # bucket_name = settings.MEDIA_BUCKET_NAME
            # object_name = f"{family_group.id}/{file_name}"
            # base64_with_prefix = ""
            # if new_file:
            #     is_deleted = storage_backends.delete_file(bucket_name, object_name)
            #     if is_deleted:
            #         is_bucket = storage_backends.create_bucket(bucket_name)
            #         if is_bucket:   
            #             is_file_upload = storage_backends.upload_file(file_path, bucket_name, object_name)

            # base64_with_prefix = storage_backends.get_file(bucket_name, object_name)

            response_data.update({
                'category': category_name,
                # 'family_group_photo': file_path,
            })
            return utils.custom_data_response(data=response_data, message='Family group updated successfully.')

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({"success": False, "message": "Something went wrong."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MemberDetails(viewsets.GenericViewSet):    
    @action(methods=['get'], detail=False, url_path=r"member-details/(?P<pk>\d+)")
    def member_details(self, request, pk):
        try:
            if pk is None:
                return Response({
                    "success": False,
                    "message": "User ID is required."}, status=status.HTTP_400_BAD_REQUEST)

            user_profile = UserProfile.objects.filter(user_id=pk, is_deleted=False).values("user__first_name", "user__last_name", "country_code", "phone_number", "age_group", "role", "is_verify", "is_pwd_change").first()
            if not user_profile:
                return Response({"success": False, "message": "User does not exist."}, status=status.HTTP_400_BAD_REQUEST)

            family_grp_data = FamilyMembers.objects.filter(member_id=pk).values("family_group_id", "is_member", "family_group__family_name", "family_group__category__name", "family_group__icon_url", "family_group__leader_id_id", "family_group__leader_id__first_name", "family_group__leader_id__last_name").first()
            country_code = ""
            phone_number = ""
            base64_with_prefix = ""
            file_path = ""
            is_join_family = False
            if family_grp_data:
                is_join_family = family_grp_data["is_member"]
                leader_data = UserProfile.objects.filter(user_id=family_grp_data["family_group__leader_id_id"], is_deleted=False).values("country_code", "phone_number").first()
                country_code = leader_data["country_code"] if leader_data and leader_data["country_code"] else ""
                phone_number = leader_data["phone_number"] if leader_data and leader_data["phone_number"] else ""
                file_path = family_grp_data["family_group__icon_url"] if family_grp_data["family_group__icon_url"] else ""

                # family_group_file = FamilyGroupFile.objects.filter(family_group_id=family_grp_data["family_group_id"]).first()
                # if family_group_file:
                #     file_path = family_group_file.file.url
                    # file_name = family_group_file["file_name"]
                    # bucket_name = settings.MEDIA_BUCKET_NAME
                    # object_name = f"{family_group_file['family_group_id']}/{file_name}"
                    # base64_with_prefix = storage_backends.get_file(bucket_name, object_name)
            
            data = {
                    "family": {
                        "id": family_grp_data["family_group_id"] if family_grp_data else "",
                        "name": family_grp_data["family_group__family_name"] if family_grp_data else "",
                        "category": family_grp_data["family_group__category__name"] if family_grp_data else "",
                        "invited_by": utils.get_user_full_name(family_grp_data["family_group__leader_id__first_name"], family_grp_data["family_group__leader_id__last_name"]) if family_grp_data else "",
                        "country_code": country_code,
                        "phone_number": phone_number,
                        "profile_pic": file_path,
                    },
                    "user_id": int(pk),
                    "first_name": user_profile['user__first_name'] if user_profile['user__first_name'] else "",
                    "last_name": user_profile['user__last_name'] if user_profile['user__last_name'] else "",
                    "country_code": user_profile['country_code'] if user_profile['country_code'] else "",
                    "phone_number": user_profile['phone_number'] if user_profile['phone_number'] else "",
                    "age_group": user_profile['age_group'] if user_profile['age_group'] else "",
                    "role": user_profile['role'] if user_profile['role'] else "",
                    "is_join_family": is_join_family,
                    "is_verify": user_profile["is_verify"],
                    "is_pwd_change": user_profile["is_pwd_change"],
                    }
            
            return Response({
                "success": True,
                "message": "User-data fetched successfully.",
                "data": data,
            }, status=status.HTTP_200_OK)
        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({"success": False, "message": "Something went wrong."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class JoinInvitation(APIView):
    def post(self,request):
        try:
            user_id = request.data.get("user_id")

            if not user_id:
                return custom_data_response(message="User ID is invalid.")
            
            family_member = FamilyMembers.objects.filter(member_id=user_id, is_deleted=False).first()
            if not family_member:
                return Response({"success" : False, "message": "User associated with this number is already a part of other family. Hence a user cannot be a member for 2 different family."}, status=status.HTTP_400_BAD_REQUEST)
            
            if family_member.is_member is True:
                return Response({"success" : False , "message" : "Member is already in family group."}, status=status.HTTP_400_BAD_REQUEST)
            
            # if family_member.family_group and UserQuestion.objects.filter(user_id=family_member.family_group.leader_id).exists():
            #     return Response({"success": False, "message": "抱歉，因為遊戲已經正式開始，所以無法加入家庭聯盟。"}, status=status.HTTP_400_BAD_REQUEST)

            family_member.is_member = True
            family_member.save()
            get_leaderboard(user_id)
            user = UserProfile.objects.filter(user_id=user_id).first()
            if user:
                other_users = list(UserProfile.objects.filter(email=user.email, is_deleted=False).exclude(user_id=user_id).values_list("user_id", flat=True))
                if other_users:
                    FamilyMembers.objects.filter(member_id__in=other_users, is_member=False).delete()
                    User.objects.filter(id__in=other_users).exclude(is_superuser=True).delete()

            refresh = RefreshToken.for_user(user)

            return Response({"success": True, "message": "User successfully joined the family group.",
                              "data": {
                                'access_token': str(refresh.access_token),
                                'refresh_token': str(refresh),
                                "user_id": user_id}}, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WeeklyUploadFileView(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]

    action_serializers = {
        'weekly-upload-file':WeeklyUploadFileSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)
    
    @action(methods=['post'], detail=False ,url_path='upload_family_photo') 
    def weekly_upload_file(self,request):
        try:
            data = request.data
            file = request.FILES.get('file')
            leader_id = request.data.get("leader_id")
            
            required_fields = {
                'file' : 'File' ,
                'leader_id' : 'Leader Id',
            }
            
            missing_fields = [value for key, value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message}, status=status.HTTP_400_BAD_REQUEST)
                
            if file and not utils.validate_file_extension(file.name):
                return utils.custom_error_response(message='Family Group Photo must be in jpg, jpeg, heic, png.')

            family_group = FamilyGroups.objects.filter(leader_id=leader_id).values("id", "category__name").first()                

            if not family_group:
                return Response({"success":False , "message":"Family group not Found."},status=status.HTTP_404_NOT_FOUND)

            if str(family_group["category__name"]).lower() != "sports":
                return custom_error_response(message="Only member with Sports category can upload family group selfie.")
          
            if datetime.now().weekday() != int(config("WEEK_DAY")):
                return custom_error_response(message="You can upload your family group selfie next sunday.")

            leader_board = get_leaderboard(leader_id)
            if WeeklyUploadFile.objects.filter(created_on__icontains=datetime.now().date(), family_group_id=family_group["id"]).exists():
                WeeklyUploadFile.objects.filter(created_on__icontains=datetime.now().date(), family_group_id=family_group["id"]).delete()
                leader_board.score = leader_board.update_score()
                leader_board.save()
                # return custom_error_response(message="Your family group selfie have already been uploaded successfully. You can upload the family group selfie on next sunday.")   

            data = {"file": file, "file_name": file.name, "family_group": family_group["id"] ,"score" : int(settings.WEEKLY_FAMILY_PHOTO_SCORE)}

            weekly_upload_file = WeeklyUploadFileSerializer(data=data)                
            if weekly_upload_file.is_valid(raise_exception=True):
                weekly_upload_file.save()
                leader_board.weekly_file.add(weekly_upload_file.data["id"])
                leader_board.score = leader_board.update_score()
                leader_board.save()
                return Response({"success": True, "message": "Family group selfie uploaded successfully."}, status=status.HTTP_200_OK)
            return Response({"success":False , "message":"Error while uploading the family group selfie."},status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        
"""-------------------------------------------------Admin panel functions-------------------------------------------------"""
def get_families(request):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        search_field = request.GET.get("field")
        search_keyword = request.GET.get("search")
        sort_by = request.GET.get("sort_by")
        sort_order = request.GET.get("order")
        page_length = int(request.GET.get("length", 10))

        query = Q()

        """Add filtering based on search field and keyword"""
        if search_field and search_keyword:
            if search_field == "Family ID":
                query &= Q(family_id__icontains=search_keyword)
            elif search_field == "Leader Name":
                search_keyword_split = search_keyword.split()
                first_name = search_keyword_split[0]
                last_name = search_keyword_split[1] if len(search_keyword_split) > 1 else None
               
                if last_name is not None:
                    query &= Q(leader_id__first_name__icontains=first_name) | Q(leader_id__last_name__icontains=last_name) | Q(leader_id__first_name__icontains=search_keyword) | Q(leader_id__last_name__icontains=search_keyword) 
                else:
                     query &= Q(leader_id__first_name__icontains=first_name) | Q(leader_id__first_name__icontains=search_keyword) | Q(leader_id__last_name__icontains=search_keyword) 
                    
            elif search_field == "Family Name":
                query &= Q(family_name__icontains=search_keyword)
            elif search_field == "Category":
                query &= Q(category__name__icontains=search_keyword)
            elif search_field == "Is Quiz Start":
                query &= Q(is_quiz_starts__icontains=search_keyword)

        sort = "-created_on"
        if sort_by == "family_id":
            sort = "family_id" if sort_order == "asc" else "-family_id"
        elif sort_by == "family_name":
            sort = "family_name" if sort_order == "asc" else "-family_name"
        elif sort_by == "created_on":
            sort = "created_on" if sort_order == "asc" else "-created_on"
        elif sort_by == "category_name":
            sort = "category__name" if sort_order == "asc" else "-category__name"
        elif sort_by == "is_quiz_starts":
            sort = "is_quiz_starts" if sort_order == "asc" else "-is_quiz_starts"

        existed_user_ids = list(UserProfile.objects.filter(is_deleted=False).values_list("user_id", flat=True))
        if existed_user_ids:
            query &= Q(leader_id_id__in=existed_user_ids)

        total_records = FamilyGroups.objects.filter(query).values_list("id").order_by(sort)
        page_number = request.GET.get('page')
        page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)

        family_group_data = FamilyGroups.objects.filter(query).values(
            "id",
            "created_on",
            "family_id",
            "leader_id_id",
            "leader_id__first_name",
            "leader_id__last_name",
            "family_name",
            "category__name",
            "is_quiz_starts",
            "icon_url",
        ).order_by(sort)[start:end]

        response = {
            "page_obj": page_obj,
            'total_items': total_items,
            "logged_in_user_data": logged_in_user_data,
            "search_field": search_field if search_field else "",
            "search_keyword": search_keyword if search_keyword else "",
            "sort_by": sort_by if sort_by else "",
            "sort_order": sort_order if sort_order else "",
            "page_length": page_length,
            "page_number": page_number if page_number else 1,
            "family_details": [], 
        }

        family_ids = list(family_group_data.values_list("id", flat=True))
        family_members = FamilyMembers.objects.filter(family_group_id__in=family_ids).values("member_id", "family_group_id")
        family_group_member = {}
        for family_member in family_members:
            if family_member["family_group_id"] not in family_group_member:
                family_group_member[family_member["family_group_id"]] = [family_member["member_id"]]
            else:
                family_group_member[family_member["family_group_id"]].append(family_member["member_id"])
      
        # leader_ids = list(family_group_data.values_list("leader_id_id", flat=True))
        # user_ids = list(UserProfile.objects.filter(user_id__in=leader_ids, is_deleted=False).values_list("user_id", flat=True))

        for family_group in family_group_data:
            # if family_group["leader_id_id"] in user_ids:
            is_quiz_starts = "False"
            if family_group["id"] in family_group_member:
                is_quiz_starts = "True" if UserQuestion.objects.filter(user_id__in=family_group_member[family_group["id"]]).exists() else "False"

            response["family_details"].append({
                "id": family_group["id"],
                "created_on": utils.get_date_from_datetime(family_group["created_on"]),
                "family_id": family_group["family_id"],
                "leader_id__username": utils.get_user_full_name(family_group["leader_id__first_name"], family_group["leader_id__last_name"]),
                "family_name": family_group["family_name"],
                "category_name": family_group["category__name"],
                "is_quiz_starts": is_quiz_starts,
                "family_icon": family_group["icon_url"] if family_group["icon_url"] else "",
            })

        return render(request, "family/families.html", response)
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "family/families.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})


# def deletefamilies(request):
#     try:
#         if request.method == "POST":
#             family_id = request.POST.get("family_id")
#             family = FamilyGroups.objects.filter(family_id=family_id).first() if family_id else None
#             if family:
#                 family.is_deleted = True
#                 family.save()

#                 return HttpResponse(json.dumps({"code": 1, "msg": "Success!"}), content_type="json")
#             return HttpResponse(json.dumps({"code": 0, "msg": "User not found."}), content_type="json")
#         return HttpResponse(json.dumps({"code": 0, "msg": "Invalid method."}), content_type="json")
#     except Exception as e:
#         create_from_exceptions(request.user.id, e, traceback.format_exc())
#         return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")


def familymemberdetails(request,id):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        family_group = FamilyGroups.objects.filter(id=id).values("category__name" , "family_name").first()
        family_members_data = FamilyMembers.objects.filter(
            family_group_id=id, is_member=True
        ).values(
            "family_group__family_name",
            "member_id",
            "member__first_name",
            "member__last_name",
            "is_member",
            "created_on",
            "is_leader",
            "family_group__category__name",
        ).order_by("id")

        response = {
            "logged_in_user_data": logged_in_user_data,
            "family_member_details": [],
            "family_group_category": family_group["category__name"] if family_group else "",
            "family_group_name": family_group["family_name"] if family_group else "",
        }
        
        member_ids = list(family_members_data.values_list("member_id" , flat=True))
        user_ids = list(UserProfile.objects.filter(user_id__in=member_ids, is_deleted=False).values_list("user_id" ,flat=True))

        for family_member in family_members_data:
            if family_member["member_id"] in user_ids:
                response["family_member_details"].append({
                    "member_id": family_member["member_id"],
                    "first_name": family_member["member__first_name"] if family_member["member__first_name"] else "",
                    "last_name" : family_member["member__last_name"] if family_member["member__last_name"] else "",
                    "created_on": utils.get_date_from_datetime(family_member["created_on"]),
                    "is_leader": "True" if family_member["is_leader"] else "False",
                })

        return render(request, "family/familymember.html", response)
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "family/familymember.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})


class Echo:
    """An object that implements just the write method of the file-like interface."""
    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value


def family_list_export(request):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        search_field = request.GET.get("field")
        search_keyword = request.GET.get("search")
        sort_by = request.GET.get("sort_by")
        sort_order = request.GET.get("order")

        query = Q()
        user_que_query = Q()
        # if search_field and search_keyword:
        #     if search_field == "Family ID":
        #         query &= Q(family_id__icontains=search_keyword)
        #     if search_field == "Leader Name":
        #         query &= Q(leader_id__username__icontains=search_keyword)
        #     elif search_field == "Family Name":
        #         query &= Q(family_name__icontains=search_keyword)
        #     elif search_field == "Created On":
        #         query &= Q(created_on__icontains=search_keyword)
        #     elif search_field == "Category":
        #         query &= Q(category__name__icontains=search_keyword)
        #     elif search_field == "Is Quiz Start":
        #         query &= Q(is_quiz_starts__icontains=search_keyword)
        
        quiz_date = QuizDates.objects.values("start_date", "end_date").order_by("-id").first()
        if quiz_date:
            user_que_query &= Q(date__range=(quiz_date["start_date"], quiz_date["end_date"]))
            
        sort = "created_on"

        existed_user_ids = list(UserProfile.objects.filter(is_deleted=False).values_list("user_id", flat=True))
        if existed_user_ids:
            query &= Q(member_id__in=existed_user_ids)

        family_data = []
        family_member_list = FamilyMembers.objects.filter(query).values(
            'id',
            'family_group_id',
            'member_id',
            'member__first_name',
            'member__last_name',
            'family_group__family_name',
            'family_group__created_on',
            'family_group__category__name',
            'is_leader',
            'created_on',
            # 'is_quiz_starts',
        ).order_by(sort)

        member_ids = list(family_member_list.values_list("member_id", flat=True))
        if member_ids:
            user_que_query &= Q(user_id__in=member_ids)
            # user_que_query &= Q(is_deleted=False)

            member_wise_user_questions = UserQuestion.objects.filter(user_que_query).values(
                    "id",
                    "question_id",
                    "question__question",
                    "question__option",
                    "question__type",
                    "question__option_type",
                    "question__answer",
                    "question__explanation",
                    "question__category",
                    "question__score",
                    "question__quiz_type",
                    "is_correct",
                    "date",
                    "selected_answer",
                    "user_id",
                ).order_by('date')

            headers = [
                'Family Created On', 'Leader Name', 'Family Name', 'Category', 'Member Created On',
                'Member Name', 'Question Created On', 'Quiz Type', 'Question Category', 'Question',
                'Correct Answer', 'Given Answer', 'Is Correct', 'Score'
            ]

            # Create a generator function that yields CSV rows
            def row_generator():
                yield headers  # First yield the headers

                leader_names = []
                family_names = []
                for family_member in family_member_list:
                    count = 0
                    for member_que in member_wise_user_questions:
                        if family_member["member_id"] == member_que["user_id"]:
                            count += 1
                            leader_name = utils.get_user_full_name(family_member["member__first_name"], family_member["member__last_name"]) if family_member["is_leader"] else ""
                            if leader_name not in leader_names:
                                leader_names.append(leader_name)
                                leader_name = leader_name
                            else:
                                leader_name = ""

                            family_name = family_member["family_group__family_name"] if family_member["family_group__family_name"] else ""
                            family_created_on = ""
                            family_category = ""
                            if family_name not in family_names:
                                family_names.append(family_name)
                                family_name = family_name
                                family_created_on = utils.get_date_from_datetime(str(family_member["family_group__created_on"])) if family_member["family_group__created_on"] else ""
                                family_category = family_member["family_group__category__name"] if family_member["family_group__category__name"] else ""
                            else:
                                family_name = ""

                            member_created_on = ""
                            member_name = ""
                            if count == 1:
                                member_created_on = utils.get_date_from_datetime(family_member["created_on"]) if family_member["created_on"] else ""
                                member_name = utils.get_user_full_name(family_member["member__first_name"], family_member["member__last_name"])

                            if member_que["question__answer"] and member_que["selected_answer"]:
                                is_correct = True if member_que["is_correct"] else False
                            else:
                                is_correct = ""

                            yield [
                                family_created_on,
                                leader_name,
                                family_name,
                                family_category,
                                member_created_on,
                                member_name,
                                str(member_que["date"]) if member_que["date"] else "",
                                member_que["question__quiz_type"] or "",
                                member_que["question__category"] or "",
                                member_que["question__question"] or "",
                                member_que["question__option"].get(member_que["question__answer"], "") if member_que["question__answer"] else "",
                                member_que["question__option"].get(member_que["selected_answer"], "") if member_que["selected_answer"] else "",
                                is_correct,
                                member_que["question__score"] if member_que["question__score"] and member_que["is_correct"] else "",
                            ]

            # Use StreamingHttpResponse for efficient memory usage
            pseudo_buffer = Echo()
            writer = csv.writer(pseudo_buffer)
            response = StreamingHttpResponse(
                (writer.writerow(row) for row in row_generator()),
                content_type="text/csv"
            )

            now = datetime.now().strftime("%d%m%Y")
            file_name = "families_" + str(now) + ".csv"

            response['Content-Disposition'] = 'attachment; filename=' + file_name
            return response
                
            # df = pd.DataFrame(family_data)

            # now = datetime.now().strftime("%d%m%Y")
            # file_path = 'media/temp/families/' + str(request.user.id) + "/"
            # file_name = "families_" + str(now) + ".csv"
            # full_file_path = file_path + file_name
            # if not os.path.exists(file_path):
            #     os.makedirs(file_path)

            # df.to_csv(full_file_path, index=False)        
            # response = FileResponse(open(full_file_path, 'rb'), content_type='text/csv')
            # response['Content-Disposition'] = f'attachment; filename=' + file_name
            # return response

        return render(request, 'family/families.html', {"msg": "Family members not found.", "logged_in_user_data": logged_in_user_data})
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, 'family/families.html', {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})
    

# def family_list_export(request):
#     logged_in_user_data = utils.get_loggedin_user_details(request)
#     try:
#         search_field = request.GET.get("field")
#         search_keyword = request.GET.get("search")
#         sort_by = request.GET.get("sort_by")
#         sort_order = request.GET.get("order")

#         query = Q()
#         if search_field and search_keyword:
#             if search_field == "Family ID":
#                 query &= Q(family_id__icontains=search_keyword)
#             if search_field == "Leader Name":
#                 query &= Q(leader_id__username__icontains=search_keyword)
#             elif search_field == "Family Name":
#                 query &= Q(family_name__icontains=search_keyword)
#             elif search_field == "Created On":
#                 query &= Q(created_on__icontains=search_keyword)
#             elif search_field == "Category":
#                 query &= Q(category__name__icontains=search_keyword)
#             elif search_field == "Is Quiz Start":
#                 query &= Q(is_quiz_starts__icontains=search_keyword)
        
#         sort = "-created_on"
#         if sort_by == "family_id":
#             sort = "family_id" if sort_order == "asc" else "-family_id"
#         elif sort_by == "family_name":
#             sort = "family_name" if sort_order == "asc" else "-family_name"
#         elif sort_by == "created_on":
#             sort = "created_on" if sort_order == "asc" else "-created_on"
#         elif sort_by == "category_name":
#             sort = "category__name" if sort_order == "asc" else "-category__name"
#         elif sort_by == "is_quiz_starts":
#             sort = "is_quiz_starts" if sort_order == "asc" else "-is_quiz_starts"

#         family_data = []
#         family_list = FamilyGroups.objects.filter(query).values(
#         'family_id',
#         'leader_id__username',
#         'family_name',
#         'created_on',    
#         'category__name',
#         'is_quiz_starts',
#         ).order_by(sort)

#         for family_list in family_list:
#             family_data.append({
#                 'Family ID' : family_list["family_id"],
#                 'Leader Name'  : family_list["leader_id__username"],
#                 'Family Name' : family_list["family_name"],
#                 "Created On": utils.get_date_from_datetime(str(family_list["created_on"])) if family_list["created_on"] else "",
#                 "Category": family_list["category__name"],
#                 "Is Quiz Start": "True" if family_list["is_quiz_starts"] else "False"})
            
#         df = pd.DataFrame(family_data)

#         now = datetime.now().strftime("%d%m%Y")
#         file_path = 'media/temp/families/' + str(request.user.id) + "/"
#         file_name = "families_" + str(now) + ".csv"
#         full_file_path = file_path + file_name
#         if not os.path.exists(file_path):
#             os.makedirs(file_path)

#         df.to_csv(full_file_path, index=False)        
#         response = FileResponse(open(full_file_path, 'rb'), content_type='text/csv')
#         response['Content-Disposition'] = f'attachment; filename=' + file_name
#         return response

#     except Exception as e:
#         create_from_exceptions(request.user.id, e, traceback.format_exc())
#         return render(request, 'family/families.html', {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})
    
    
def family_photos(request, id):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        page_length = request.GET.get("length")
        total_records = WeeklyUploadFile.objects.filter(family_group_id=id).values_list("id").order_by("-created_on")
        page_number = request.GET.get('page')
        page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)
        from_date = request.GET.get("start_date")
        to_date = request.GET.get("end_date")
    
        query = Q()
        if from_date and to_date:
            start_date = datetime.strptime(from_date, "%m/%d/%Y").strftime("%Y-%m-%d 00:00:00")
            end_date = datetime.strptime(to_date, "%m/%d/%Y").strftime("%Y-%m-%d 23:59:59")
            query.add(Q(created_on__range=(start_date, end_date)), query.connector)
        
        query.add(Q(family_group_id=id), query.connector)

        family_group = FamilyGroups.objects.filter(id=id).values("family_name", "category__name").first()
        family_photos = WeeklyUploadFile.objects.filter(query).order_by("-created_on")[start:end]

        response = {
            "data": [],
            "page_obj": page_obj,
            'total_items': total_items,
            "logged_in_user_data": logged_in_user_data,
            "page_length": page_length if page_length else 10,
            "from_date": from_date if from_date else "",
            "to_date": to_date if to_date else "",
            "family_group_category": family_group["category__name"] if family_group else "",
            "family_group_name": family_group["family_name"] if family_group else "",
            "family_group_id": id,
            "page_number": page_number if page_number else 1,
        }

        for family_photo in family_photos:
            response["data"].append({
                "id": family_photo.id,
                "file": family_photo.file.url if family_photo.file else "",
                "score": family_photo.score if family_photo.score else "",
                "created_on": utils.get_date_from_datetime(str(family_photo.created_on)) if family_photo.created_on else "",
            })
            
        return render(request, 'family/family_photos.html', response)
            
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, 'family/family_photos.html', {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})
    

def delete_weekly_family_photo(request):
    try:
        with transaction.atomic():
            if request.method == "POST":
                id = request.POST.get("id")
                if id:
                    weekly_upload_file = WeeklyUploadFile.objects.filter(id=id).first()
                    if weekly_upload_file:
                        leader_id = weekly_upload_file.family_group.leader_id_id
                        weekly_upload_file.delete()
                        leader_board = get_leaderboard(leader_id)
                        leader_board.score =  leader_board.update_score()
                        leader_board.save()
                    
                    return HttpResponse(json.dumps({"code": 1, "msg": "Record deleted successfully."}), content_type="json")
                return HttpResponse(json.dumps({"code": 0, "msg": "Record not found."}), content_type="json")
            return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")