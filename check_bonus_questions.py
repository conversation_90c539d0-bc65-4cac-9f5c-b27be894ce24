#!/usr/bin/env python3
"""
检查HKU数据库中的Bonus questions情况
"""

import psycopg2
from datetime import datetime

def check_bonus_questions():
    """检查Bonus questions的情况"""
    
    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }
    
    try:
        print("🔍 检查Bonus questions情况...")
        
        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查所有quiz_type
        print("\n📊 所有quiz_type分布:")
        quiz_type_query = """
        SELECT 
            q.quiz_type,
            COUNT(*) as question_count,
            COUNT(DISTINCT uq.id) as answered_count
        FROM question_question q
        LEFT JOIN question_userquestion uq ON q.id = uq.question_id AND uq.answered = true
        WHERE q.is_deleted = false
        GROUP BY q.quiz_type
        ORDER BY q.quiz_type
        """
        
        cursor.execute(quiz_type_query)
        quiz_types = cursor.fetchall()
        
        for quiz_type, question_count, answered_count in quiz_types:
            print(f"   {quiz_type}: {question_count} 题目, {answered_count} 回答")
        
        # 检查Bonus questions的详细情况
        print(f"\n🎁 Bonus questions详细情况:")
        bonus_query = """
        SELECT 
            q.category,
            q.question,
            q.score,
            COUNT(uq.id) as answer_count,
            SUM(CASE WHEN uq.is_correct THEN 1 ELSE 0 END) as correct_count
        FROM question_question q
        LEFT JOIN question_userquestion uq ON q.id = uq.question_id AND uq.answered = true
        WHERE q.quiz_type = 'Bonus'
        AND q.is_deleted = false
        GROUP BY q.id, q.category, q.question, q.score
        ORDER BY q.category, q.question
        """
        
        cursor.execute(bonus_query)
        bonus_questions = cursor.fetchall()
        
        if bonus_questions:
            for category, question, score, answer_count, correct_count in bonus_questions:
                print(f"   类别: {category}")
                print(f"   问题: {question[:50]}...")
                print(f"   分数: {score}")
                print(f"   回答次数: {answer_count}")
                print(f"   正确次数: {correct_count}")
                print(f"   ---")
        else:
            print("   没有找到Bonus questions")
        
        # 检查Bonus questions的用户回答情况
        print(f"\n👥 Bonus questions用户回答分布:")
        bonus_users_query = """
        SELECT 
            fg.family_name,
            au.username,
            q.category,
            COUNT(*) as bonus_answers,
            SUM(CASE WHEN uq.is_correct THEN q.score ELSE 0 END) as bonus_score
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND q.quiz_type = 'Bonus'
        GROUP BY fg.family_name, au.username, q.category
        ORDER BY fg.family_name, au.username, q.category
        """
        
        cursor.execute(bonus_users_query)
        bonus_users = cursor.fetchall()
        
        if bonus_users:
            for family, username, category, answers, score in bonus_users:
                print(f"   {family} - {username} - {category}: {answers} 题, {score} 分")
        else:
            print("   没有用户回答Bonus questions")
        
        # 检查Bonus questions的日期分布
        print(f"\n📅 Bonus questions日期分布:")
        bonus_dates_query = """
        SELECT 
            uq.date as answer_date,
            q.category,
            COUNT(*) as answer_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND q.quiz_type = 'Bonus'
        GROUP BY uq.date, q.category
        ORDER BY uq.date, q.category
        """
        
        cursor.execute(bonus_dates_query)
        bonus_dates = cursor.fetchall()
        
        if bonus_dates:
            for answer_date, category, count in bonus_dates:
                print(f"   {answer_date} - {category}: {count} 条")
        else:
            print("   没有Bonus questions的答题记录")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    check_bonus_questions()
