#!/usr/bin/env python3
"""
重新生成所有导出文件
使用正确的日期范围：2025-05-05 到 2025-05-19
"""

import psycopg2
import csv
import json
from datetime import datetime, date

class HKUExportRegeneration:
    """HKU导出重新生成器"""
    
    def __init__(self):
        self.db_config = {
            'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
            'port': 5432,
            'database': 'hku_staging',
            'user': 'postgres',
            'password': 'k9Z#x$Lh3&!V'
        }
        
        # 标准日期范围
        self.start_date = date(2025, 5, 5)
        self.end_date = date(2025, 5, 19)
        
        print(f"📅 使用日期范围: {self.start_date} 到 {self.end_date}")
    
    def connect_db(self):
        """连接数据库"""
        try:
            conn = psycopg2.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return conn
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return None
    
    def export_food_category(self):
        """导出饮食类别数据"""
        print("\n🍎 导出饮食类别数据...")
        
        conn = self.connect_db()
        if not conn:
            return None
        
        try:
            cursor = conn.cursor()
            
            # 查询饮食类别数据
            query = """
            SELECT 
                uq.date as answer_date,
                fg.family_name,
                au.username as member_name,
                q.category as question_category,
                q.quiz_type,
                q.question,
                q.answer as correct_answer,
                uq.selected_answer as given_answer_letter,
                q.option as question_options,
                uq.is_correct,
                CASE WHEN uq.is_correct THEN q.score ELSE 0 END as question_score,
                q.score as max_question_score,
                uq.user_id,
                fg.id as family_group_id,
                COALESCE(sc.steps, 0) as step_count
            FROM question_userquestion uq
            JOIN question_question q ON uq.question_id = q.id
            JOIN auth_user au ON uq.user_id = au.id
            JOIN family_familymembers fm ON au.id = fm.member_id
            JOIN family_familygroups fg ON fm.family_group_id = fg.id
            JOIN family_category c ON fg.category_id = c.id
            LEFT JOIN question_stepscounter sc ON uq.user_id = sc.user_id AND uq.date = sc.created_on::date
            WHERE uq.answered = true
            AND uq.is_deleted = false
            AND q.is_deleted = false
            AND fm.is_deleted = false
            AND fm.is_member = true
            AND c.name = 'Food'
            AND uq.date BETWEEN %s AND %s
            ORDER BY uq.date, fg.family_name, au.username, q.quiz_type, q.id
            """
            
            cursor.execute(query, (self.start_date, self.end_date))
            results = cursor.fetchall()
            
            print(f"📊 找到 {len(results)} 条饮食类别记录")
            
            # 生成CSV文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"hku_exercise_export_Food_{timestamp}.csv"
            
            self._write_csv_with_options(results, filename, 'Food')
            
            cursor.close()
            conn.close()
            
            return filename
            
        except Exception as e:
            print(f"❌ 饮食类别导出失败: {e}")
            if conn:
                conn.close()
            return None
    
    def export_sports_category(self):
        """导出运动类别数据"""
        print("\n🏃 导出运动类别数据...")
        
        conn = self.connect_db()
        if not conn:
            return None
        
        try:
            cursor = conn.cursor()
            
            # 查询运动类别数据
            query = """
            SELECT 
                uq.date as answer_date,
                fg.family_name,
                au.username as member_name,
                q.category as question_category,
                q.quiz_type,
                q.question,
                q.answer as correct_answer,
                uq.selected_answer as given_answer_letter,
                q.option as question_options,
                uq.is_correct,
                CASE WHEN uq.is_correct THEN q.score ELSE 0 END as question_score,
                q.score as max_question_score,
                uq.user_id,
                fg.id as family_group_id,
                COALESCE(sc.steps, 0) as step_count
            FROM question_userquestion uq
            JOIN question_question q ON uq.question_id = q.id
            JOIN auth_user au ON uq.user_id = au.id
            JOIN family_familymembers fm ON au.id = fm.member_id
            JOIN family_familygroups fg ON fm.family_group_id = fg.id
            JOIN family_category c ON fg.category_id = c.id
            LEFT JOIN question_stepscounter sc ON uq.user_id = sc.user_id AND uq.date = sc.created_on::date
            WHERE uq.answered = true
            AND uq.is_deleted = false
            AND q.is_deleted = false
            AND fm.is_deleted = false
            AND fm.is_member = true
            AND c.name = 'Sports'
            AND uq.date BETWEEN %s AND %s
            ORDER BY uq.date, fg.family_name, au.username, q.quiz_type, q.id
            """
            
            cursor.execute(query, (self.start_date, self.end_date))
            results = cursor.fetchall()
            
            print(f"📊 找到 {len(results)} 条运动类别记录")
            
            # 生成CSV文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"hku_exercise_export_Sports_{timestamp}.csv"
            
            self._write_csv_with_options(results, filename, 'Sports')
            
            cursor.close()
            conn.close()
            
            return filename
            
        except Exception as e:
            print(f"❌ 运动类别导出失败: {e}")
            if conn:
                conn.close()
            return None
    
    def _write_csv_with_options(self, results, filename, category_type):
        """写入CSV文件，包含完整的选项信息"""
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入表头
            headers = [
                'Answer Date', 'Family Name', 'Member Name', 'Question Category',
                'Quiz Type', 'Question', 'Correct Answer', 'Given Answer',
                'All Answer Options', 'Is Correct', 'Question Score', 'Max Question Score',
                'User ID', 'Family Group ID', 'Step Count',
                'Daily Checkin Score', 'Weekly Family Photo Score',
                'Food Screenshot Score', 'Steps Screenshot Score', 'Quiz Score'
            ]
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            
            # 按用户和日期分组处理数据
            user_date_data = {}
            for row in results:
                user_id = row[12]  # user_id
                answer_date = row[0]  # answer_date
                key = (user_id, answer_date)
                
                if key not in user_date_data:
                    user_date_data[key] = []
                user_date_data[key].append(row)
            
            # 写入数据行
            for (user_id, answer_date), rows in user_date_data.items():
                # 计算当天的Quiz分数
                quiz_score = sum(row[10] for row in rows)  # question_score
                
                # 为每个问题写入一行，但只在第一行显示分数
                for i, row in enumerate(rows):
                    # 处理答案选项
                    given_answer_letter = row[7]  # 用户选择的字母
                    question_options = row[8]     # JSON格式的选项
                    quiz_type = row[4]            # Quiz类型
                    
                    # 对于Bonus questions，解析选项并获取完整答案文本
                    if quiz_type == 'Bonus' and question_options:
                        try:
                            options_dict = json.loads(question_options) if isinstance(question_options, str) else question_options
                            # 获取用户选择的完整答案
                            given_answer_full = options_dict.get(given_answer_letter, given_answer_letter) if given_answer_letter else ''
                            # 格式化所有选项
                            all_options = '; '.join([f"{k}: {v}" for k, v in options_dict.items()])
                        except:
                            given_answer_full = given_answer_letter if given_answer_letter else ''
                            all_options = str(question_options) if question_options else ''
                    else:
                        given_answer_full = given_answer_letter if given_answer_letter else ''
                        all_options = ''
                    
                    # 构建CSV行数据
                    csv_row = [
                        row[0],   # Answer Date
                        row[1],   # Family Name
                        row[2],   # Member Name
                        row[3],   # Question Category
                        row[4],   # Quiz Type
                        row[5],   # Question
                        row[6] if quiz_type != 'Bonus' else '',  # Correct Answer (空白对于Bonus questions)
                        given_answer_full,  # Given Answer (完整文本)
                        all_options,  # All Answer Options (所有选项)
                        row[9],   # Is Correct
                        row[10],  # Question Score
                        row[11],  # Max Question Score
                        row[12],  # User ID
                        row[13],  # Family Group ID
                        row[14],  # Step Count
                    ]
                    
                    # 添加分数字段（只在第一行显示）
                    if i == 0:
                        if category_type == 'Food':
                            csv_row.extend([
                                100,  # Daily Checkin Score
                                0,    # Weekly Family Photo Score (不计分)
                                500,  # Food Screenshot Score
                                0,    # Steps Screenshot Score (饮食类别不适用)
                                quiz_score  # Quiz Score
                            ])
                        else:  # Sports
                            csv_row.extend([
                                100,  # Daily Checkin Score
                                0,    # Weekly Family Photo Score (不计分)
                                0,    # Food Screenshot Score (运动类别不适用)
                                500,  # Steps Screenshot Score
                                quiz_score  # Quiz Score
                            ])
                    else:
                        csv_row.extend([0, 0, 0, 0, 0])  # 其他行不重复计分
                    
                    writer.writerow(csv_row)
        
        print(f"✅ {category_type}类别导出完成: {filename}")

def main():
    """主函数"""
    print("🔄 重新生成所有HKU导出文件")
    print("📅 使用标准日期范围: 2025-05-05 到 2025-05-19")
    print("=" * 60)
    
    regenerator = HKUExportRegeneration()
    
    # 导出饮食类别
    food_file = regenerator.export_food_category()
    
    # 导出运动类别
    sports_file = regenerator.export_sports_category()
    
    print(f"\n🎉 重新导出完成!")
    if food_file:
        print(f"📁 饮食类别文件: {food_file}")
    if sports_file:
        print(f"📁 运动类别文件: {sports_file}")
    
    print(f"\n📋 下一步:")
    print(f"   1. 检查生成的CSV文件")
    print(f"   2. 运行分离脚本生成详细分数文件")
    print(f"   3. 验证数据完整性")

if __name__ == "__main__":
    main()
