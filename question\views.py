from django.shortcuts import render, redirect, HttpResponse
from django.views import View
from .models import Question, QuestionImage, UserQuestion, AnswerImage, AnswerText, QuizStep, Leaderboard, UserFoodStepImage
from .forms import ImportQuestionForm
from django.contrib import messages
from django.db import transaction
from django.db.models import Q
from django.core.files.temp import NamedTemporaryFile
from django.core.files.base import ContentFile
from base.utils import get_pagination
import csv, re, requests, traceback, json
from base import utils
from base.views import create_from_exceptions
from django.db.models import Q
from user_authentication.models import UserProfile
from family.models import FamilyMembers, FamilyGroups, FamilyGroupFile
from question.models import StepsLeaderboard
from datetime import datetime
from .models import Info
from .forms import PrizeInfoForm, ScoreInfoForm

# Create your views here.

class QuestionListView(View):
    def get(self, request):
        # question_list = Question.objects.all().prefetch_related("question_image")
        # context = {
        #     "question_list":question_list
        # }
        # return render(request, 'question/question_list.html', context)
    
        logged_in_user_data = utils.get_loggedin_user_details(request)
        try:
            page_length = request.GET.get("length")
            total_records = Question.active_objects.filter(is_deleted=False).values_list("id").order_by("-id")
            page_number = request.GET.get('page')
            page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)
            from_date = request.GET.get("start_date")
            to_date = request.GET.get("end_date")
        
            query = Q()
            if from_date and to_date:
                start_date = datetime.strptime(from_date, "%m/%d/%Y").strftime("%Y-%m-%d 00:00:00")
                end_date = datetime.strptime(to_date, "%m/%d/%Y").strftime("%Y-%m-%d 23:59:59")
                query.add(Q(updated_at__range=(start_date, end_date)), query.connector)
            
            query.add(Q(is_deleted=False), query.connector)

            questions = Question.objects.filter(query).values(
                "id",
                "question",
                "type",
                "option_type",
                "option",
                "answer",
                "explanation",
                "category",
                "shuffle",
                "score",
                "quiz_type",
                "date",
                "updated_at",
                ).order_by("-id")[start:end]
            
            questions_ids = list(questions.values_list("id", flat=True))
            que_id_image = {}
            que_images = QuestionImage.objects.filter(question_id__in=questions_ids)
            for que_img in que_images:
                que_id_image[que_img.question.id] = que_img.image.url if que_img.image else ""
            
            response = {"data": [],
                        "page_obj": page_obj,
                        'total_items': total_items,
                        "logged_in_user_data": logged_in_user_data,
                        "page_length": page_length if page_length else 10,
                        "from_date": from_date if from_date else "",
                        "to_date": to_date if to_date else "",
                        "page_number": page_number if page_number else 1,
                        }   
                
            for question in questions:
                response["data"].append({
                    "id": question["id"],
                    "question": question["question"] if question["question"] else "",
                    "type": question["type"] if question["type"] else "",
                    "option_type": question["option_type"] if question["option_type"] else "",
                    "option_a": question["option"]["A"] if question["option"] else "",
                    "option_b": question["option"]["B"] if question["option"] else "",
                    "option_c": question["option"]["C"] if question["option"] else "",
                    # "option_d": question["option"]["D"] if question["option"] else "",
                    "answer": question["option"][question["answer"]] if question["answer"] and question["option"] and question["answer"] in question["option"] else "",
                    "explanation": question["explanation"] if question["explanation"] else "",
                    "category": question["category"] if question["category"] else "",
                    "shuffle": question["shuffle"] if question["shuffle"] else False,
                    "score": question["score"] if question["score"] else "",
                    "quiz_type": question["quiz_type"] if question["quiz_type"] else "",
                    "date": question["date"] if question["date"] else "",
                    "que_image": que_id_image[question["id"]] if question["id"] in que_id_image else "",
                    "is_user_ans_img": True if question["option_type"] and str(question["option_type"]).lower() in ["image_upload", "image_option", "image upload", "image option"] else False,
                    "updated_at": utils.get_date_from_datetime(question["updated_at"]) if question["updated_at"] else "",
                })
            
            return render(request, "question/question_list.html", response)
        except Exception as e:
            print(e)
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return render(request, "question/question_list.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})
    
class QuestionDetailView(View):
    def get(self, request, id):
        question_list = Question.objects.get(id = id)
        question_image = QuestionImage.objects.filter(question_id = id)
        page_length = 10 #request.GET.get("length")
        page_number = 1 #request.GET.get('page')
        question = get_pagination(request, question_list, page_number, page_length)
        print(question, '*** question ***')
        context = {
            'question':question,
            'question_image':question_image
        }
        return render(request, 'question/question_detail.html', context)
    
from concurrent.futures import ThreadPoolExecutor, as_completed
from django.shortcuts import redirect
from django.contrib import messages
from django.db import transaction
from django.views import View
import csv
import requests
from tempfile import NamedTemporaryFile
from django.core.files.base import ContentFile
import re
from .models import Question, QuestionImage
from .forms import ImportQuestionForm
from .helper import assign_questions_to_users
from .reassign_questions import assign_questions_to_new_users

class ImportQuestionView(View):
    @transaction.atomic
    def post(self, request):
        form = ImportQuestionForm(request.POST, request.FILES)
        old_questions = Question.objects.all()
        for q in old_questions:
            q.is_deleted = True
        Question.objects.bulk_update(old_questions, ['is_deleted'])
            
        if form.is_valid():
            csv_file = request.FILES['csv_file']
            if csv_file and csv_file.name and ((csv_file.name).split(".")[-1]).lower() == "csv":
                try:
                    csv_data = csv_file.read().decode('utf-8').splitlines()
                except UnicodeDecodeError:
                    csv_data = csv_file.read().decode('big-5').splitlines()  
                except Exception as e:
                    messages.error(request, f"Error in uploading file {e}")
                    return redirect('question_list')
                if csv_data[0].startswith('\ufeff'):
                    csv_data[0] = csv_data[0][1:]
                reader = csv.DictReader(filter(lambda x: x.strip(), csv_data))

                # Using ThreadPoolExecutor for multi-threading
                with ThreadPoolExecutor(max_workers=10) as executor:
                    futures = {executor.submit(self.import_question, request, row): row for row in reader}
                    for future in as_completed(futures):
                        row = futures[future]
                        try:
                            future.result()
                        except Exception as e:
                            messages.error(request, f"Error importing question {row}: {e}")
                status, import_message = assign_questions_to_users()
                if status:
                    messages.success(request, import_message)
                else:
                    messages.error(request, import_message)
                return redirect('question_list')
            else:
                messages.info(request, "Please choose correct file to import!")
        else:
            messages.error(request, "Error in Importing question.")
        return redirect('question_list')

    @staticmethod
    def import_question(request, row):
        question_text = row['question']
        question_image = None
        if ImportQuestionView.is_image_url(question_text):
            question_image = ImportQuestionView.download_image(image_url=question_text)
            question_text = None
        options = {}
        if row["option_type"] == "text_option" or row['option_type'] == "image_option":
            options["A"] = row["A"]
            options["B"] = row["B"]
            options["C"] = row["C"]
            # options["D"] = row["D"]
        try:
            question = Question.objects.create(
                question_no=row['number'],
                question=question_text,
                type=row['question_type'],
                option_type=row['option_type'],
                option=options if options else None,
                answer=str(row['correct_option']).upper(),
                # explanation=row['explanation'],
                explanation="",
                category=row['category'],
                shuffle=True if row['shuffle'] == "TRUE" else False,
                quiz_type=row['quiz_type'],
                score=row['weight']
            )

            QuestionImage.objects.create(question=question, image=question_image)
        except Exception as e:
            messages.error(request, f"Error importing question: {e}")
            return redirect('question_list')

    @staticmethod
    def is_image_url(text):
        url_pattern = re.compile(r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+')
        image_extensions = ['.jpg', '.jpeg', '.png']
        if url_pattern.match(text):
            if any(text.lower().endswith(ext) for ext in image_extensions):
                return True
        return False

    @staticmethod
    def download_image(image_url):
        response = requests.get(image_url)
        if response.status_code == 200:
            img_temp = NamedTemporaryFile()
            img_temp.write(response.content)
            img_temp.flush()
            img_temp.seek(0)
            return ContentFile(img_temp.read(), name=image_url.split('/')[-1])
        return None


def assign_to_new_users(request):
    try:
        status, import_message = assign_questions_to_new_users()
        if status:
            messages.success(request, import_message)
        else:
            messages.error(request, import_message)
        
        return redirect('question_list')
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        messages.error(request, f"Error in assign questions to the new users: {e}")
        return redirect('question_list')


def member_wise_que_ans(request, id):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        from_date = request.GET.get("start_date")
        to_date = request.GET.get("end_date")
        sort_by = request.GET.get("sort_by")
        sort_order = request.GET.get("order")
        page_length = request.GET.get("length")
        type = request.GET.get("type")

        if not UserProfile.objects.filter(user_id=id, is_deleted=False).exists():
            return render(request, "question/member_wise_que_ans.html", {"msg": "User does not exist or deleted.", "logged_in_user_data": logged_in_user_data})
        
        user_details = FamilyMembers.objects.filter(member_id=id, is_member=True).values("member__first_name", "member__last_name", "family_group__family_name", "family_group__category__name", "family_group_id").first()

        query = Q()
        if from_date and to_date:
            start_date = datetime.strptime(from_date, "%m/%d/%Y").strftime("%Y-%m-%d 00:00:00")
            end_date = datetime.strptime(to_date, "%m/%d/%Y").strftime("%Y-%m-%d 23:59:59")
            query.add(Q(created_at__range=(start_date, end_date)), query.connector)

        if type == "steps":
            query.add(Q(user_id=id), query.connector)
        
            sort = "-created_at"
            if sort_by == "created_at":
                sort = "created_at" if sort_order == "asc" else "-created_at"

            response = {"data": [], 
                        "logged_in_user_data": logged_in_user_data,
                        "from_date": from_date if from_date else "",
                        "to_date": to_date if to_date else "",
                        "sort_by": sort_by if sort_by else "",
                        "sort_order": sort_order if sort_order else "",
                        "page_length": page_length if page_length else 10,
                        "member_name": utils.get_user_full_name(user_details["member__first_name"], user_details["member__last_name"]) if user_details else "",
                        "family_group_id": user_details["family_group_id"] if user_details else "",
                        "family_group_name": user_details["family_group__family_name"] if user_details and user_details["family_group__family_name"] else "",
                        "family_group_category": user_details["family_group__category__name"] if user_details and user_details["family_group__category__name"] else "",
                        "user_id": id,
                        "type": "steps",
                        }
            
            if str(user_details["family_group__category__name"]).lower() == "sports":
                total_records = QuizStep.objects.filter(query).values_list("id").order_by(sort)
                page_number = request.GET.get('page')
                page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)
                
                quiz_step_data = QuizStep.objects.filter(query).order_by(sort)[start:end]
                for quiz_step in quiz_step_data:
                    total_steps = 0
                    for step in quiz_step.steps.all():
                        total_steps += int(step.steps) if step.steps else 0

                    response["data"].append({
                        "id": quiz_step.id,
                        "user_id": quiz_step.user.id,
                        "total_steps": total_steps,
                        "created_on": utils.get_date_from_datetime(quiz_step.created_at) if quiz_step.created_at else "",
                        "file": quiz_step.image.image.url if quiz_step.image and quiz_step.image.image else "",
                        "steps_score": quiz_step.steps_score if quiz_step.steps_score else "",
                    })
            else:
                total_records = UserFoodStepImage.objects.filter(query).values_list("id").order_by(sort)
                page_number = request.GET.get('page')
                page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)

                user_food_steps_ss = UserFoodStepImage.objects.filter(query).order_by(sort)[start:end]
                for user_food_step_ss in user_food_steps_ss:
                    response["data"].append({
                        "id": user_food_step_ss.id,
                        "user_id": user_food_step_ss.user.id,
                        "created_on": utils.get_date_from_datetime(user_food_step_ss.created_at) if user_food_step_ss.created_at else "",
                        "file": user_food_step_ss.image.url if user_food_step_ss and user_food_step_ss.image else "",
                        'upload_type': user_food_step_ss.upload_type if user_food_step_ss and user_food_step_ss.upload_type else "",
                    })
                    
            response["page_obj"] = page_obj
            response["page_number"] = page_number if page_number else 1
            response['total_items'] = total_items
        else:
            query.add(Q(user_id=id) & Q(answered=True), query.connector)
        
            sort = "-date"
            if sort_by == "category":
                sort = "question__category" if sort_order == "asc" else "-question__category"
            elif sort_by == "quiz_type":
                sort = "question__quiz_type" if sort_order == "asc" else "-question__quiz_type"
            elif sort_by == "date":
                sort = "date" if sort_order == "asc" else "-date"
            elif sort_by == "is_correct":
                sort = "is_correct" if sort_order == "asc" else "-is_correct"

            total_records = UserQuestion.objects.filter(query).values_list("id").order_by(sort)
            page_number = request.GET.get('page')
            page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)
            
            response = {"data": [], 
                        "page_obj": page_obj,
                        "page_number": page_number if page_number else 1,
                        'total_items': total_items,
                        "logged_in_user_data": logged_in_user_data,
                        "from_date": from_date if from_date else "",
                        "to_date": to_date if to_date else "",
                        "sort_by": sort_by if sort_by else "",
                        "sort_order": sort_order if sort_order else "",
                        "page_length": page_length if page_length else 10,
                        "member_name": utils.get_user_full_name(user_details["member__first_name"], user_details["member__last_name"]) if user_details else "",
                        "family_group_id": user_details["family_group_id"] if user_details else "",
                        "family_group_name": user_details["family_group__family_name"] if user_details and user_details["family_group__family_name"] else "",
                        "family_group_category": user_details["family_group__category__name"] if user_details and user_details["family_group__category__name"] else "",
                        "user_id": id,
                        "type": "quiz",
                        }
        
            user_wise_que_data = UserQuestion.objects.filter(query).values(
                "id",
                "question_id",
                "question__question",
                "question__option",
                "question__type",
                "question__option_type",
                "question__answer",
                "question__explanation",
                "question__category",
                "question__score",
                "question__quiz_type",
                "is_correct",
                "date",
                "selected_answer",
            ).order_by(sort)[start:end]
            
            user_que_ids = list(user_wise_que_data.values_list("id", flat=True))
            user_text_ans = AnswerText.objects.filter(question_id__in=user_que_ids).values("question_id", "answer")
            que_text_ans = utils.get_dict_from_queryset(user_text_ans, "question_id", "answer")
            que_img_ans = {}
            user_img_ans = AnswerImage.objects.filter(question_id__in=user_que_ids)
            for img_ans in user_img_ans:
                que_img_ans[img_ans.question_id] = img_ans.image.url if img_ans.image else ""
                
            questions_ids = list(user_wise_que_data.values_list("question_id", flat=True))
            que_id_image = {}
            que_images = QuestionImage.objects.filter(question_id__in=questions_ids)
            for que_img in que_images:
                que_id_image[que_img.question.id] = que_img.image.url if que_img.image else ""
            
            for que_data in user_wise_que_data:
                user_answer = ""
                # user_ans_img = ""
                if que_data["id"] in que_text_ans:
                    user_answer = que_text_ans[que_data["id"]]
                elif que_data["id"] in que_img_ans:
                    user_answer = que_img_ans[que_data["id"]]
                elif que_data["selected_answer"] and que_data["question__option"] and que_data["selected_answer"] in que_data["question__option"]:
                    user_answer = que_data["question__option"][que_data["selected_answer"]]

                response["data"].append({
                    "question_id": que_data["question_id"],
                    "question__question": que_data["question__question"] if que_data["question__question"] else "",
                    "question__type": que_data["question__type"] if que_data["question__type"] else "",
                    "question__option_type": que_data["question__option_type"] if que_data["question__option_type"] else "",
                    "question__answer": que_data["question__option"][que_data["question__answer"]] if que_data["question__answer"] and que_data["question__answer"] in que_data["question__option"] else "",
                    "question__explanation": que_data["question__explanation"] if que_data["question__explanation"] else "",
                    "question__category": que_data["question__category"] if que_data["question__category"] else "",
                    "question__score": que_data["question__score"] if que_data["question__score"] and que_data["is_correct"] else "",
                    "question__quiz_type": que_data["question__quiz_type"] if que_data["question__quiz_type"] else "",
                    "is_correct": que_data["is_correct"] if que_data["is_correct"] else False,
                    "user_answer": user_answer,
                    "is_user_ans_img": True if que_data["question__option_type"] and str(que_data["question__option_type"]).lower() in ["image_upload", "image_option", "image upload", "image option"] else False,
                    "que_image": que_id_image[que_data["question_id"]] if que_data["question_id"] in que_id_image else "",
                    "date": str(que_data["date"]) if que_data["date"] else "",
                })

        return render(request, "question/member_wise_que_ans.html", response)
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "question/member_wise_que_ans.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})
        # return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data}), content_type="json")


def get_usersteps(request, id):
    try:
        if request.method == "GET":
            data = []
            quiz_step_data = QuizStep.objects.filter(id=id).first()
            total_steps = 0
            if quiz_step_data and quiz_step_data.steps:
                for step in quiz_step_data.steps.all():
                    total_steps += int(step.steps) if step.steps else 0
                    data.append({
                        "date": str(step.date) if step.date else "",
                        "steps": step.steps if step.steps else 0,
                    })

                user_full_name = utils.get_user_full_name(quiz_step_data.user.first_name, quiz_step_data.user.last_name)
                
                return HttpResponse(json.dumps({"code": 1, "data": data, "total_steps": total_steps, "id": id, "user_id": quiz_step_data.user.id, "user_full_name": user_full_name}), content_type="json")
            return HttpResponse(json.dumps({"code": 0, "msg": "Steps data is not available."}), content_type="json")
        return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")


def update_usersteps(request):
    try:
        if request.method == "POST":
            user_steps_data = eval(request.POST.get("user_steps_data")) if request.POST.get("user_steps_data") else []
            step_id = request.POST.get("step_id")
            user_id = request.POST.get("user_id")
            family = FamilyMembers.objects.filter(member = user_id).first()
            if step_id and user_steps_data:
                quiz_step = QuizStep.objects.filter(id=step_id).first()
                for user_steps in user_steps_data:
                    steps = user_steps['steps']
                    quiz_step.steps.filter(date__icontains=user_steps["date"]).update(steps=steps)
                    quiz_step.update_steps_score() #update_steps_score the score in the steps leaderboard.
                    quiz_step.save()

                    steps_leaderboard = StepsLeaderboard.objects.filter(family_group = family.family_group).first()
                    if steps_leaderboard:
                        steps_leaderboard.update_score()
                        steps_leaderboard.save()
                return HttpResponse(json.dumps({"code": 1, "msg": "User steps updated.", "user_id": user_id}), content_type="json")
            return HttpResponse(json.dumps({"code": 0, "msg": "Error in updating user steps."}), content_type="json")
        return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")


def leaderboard_list(request):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        search_field = request.GET.get("field")
        search_keyword = request.GET.get("search")
        sort_by = request.GET.get("sort_by")
        sort_order = request.GET.get("order")
        page_length = request.GET.get("length")
        category_type = request.GET.get("type", "food")

        user_ids = list(UserProfile.objects.filter(is_deleted=False).values_list("user_id", flat=True))
        query = Q()

        if category_type == "steps":
            if search_field and search_keyword:
                if search_field == "Family Name":
                    query.add(Q(family_group__family_name__icontains=search_keyword), query.connector)
                elif search_field == "Created on":
                    query.add(Q(created_at__icontains=search_keyword), query.connector)
                elif search_field == "Total Score":
                    query.add(Q(score__icontains=search_keyword), query.connector)

            sort = "-score"
            if sort_by == "family_name":
                sort = "family_group__family_name" if sort_order == "asc" else "-family_group__family_name"
            elif sort_by == "category":
                sort = "family_group__category__name" if sort_order == "asc" else "-family_group__category__name"
            elif sort_by == "score":
                sort = "score" if sort_order == "asc" else "-score"

            total_records = StepsLeaderboard.objects.filter(query).order_by(sort).values_list("id")
            page_number = request.GET.get('page')
            page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)

            family_groups = StepsLeaderboard.objects.filter(query).order_by(sort).values("id", "family_group__family_name", "family_group__icon_url", "created_at", "score", "family_group__category__name", "family_group_id" ,"family_group__leader_id",
            )[start:end] 
            
            # family_group_ids = list(family_groups.values_list("family_group_id", flat=True))
            # family_group_id_pic = utils.get_family_group_dp(family_group_ids)

            response = {
                "data": [],
                "page_obj": page_obj,
                "page_number": page_number if page_number else 1,
                "total_items": total_items,
                "logged_in_user_data": logged_in_user_data,
                "search_field": search_field or "",
                "search_keyword": search_keyword or "",
                "sort_by": sort_by or "",
                "sort_order": sort_order or "",
                "page_length": page_length if page_length else 10,
                "type": "steps",
            }
            
            score_wise_rank = {}
            score_wise_id = StepsLeaderboard.objects.filter(family_group__leader_id__in=user_ids).order_by("-score").values("id")
            count = 0
            for score_id in score_wise_id:
                count += 1
                score_wise_rank[score_id["id"]] = count
            
            for family_group in family_groups:
                if family_group["family_group__leader_id"] in user_ids:
                    response["data"].append({
                        "family_group__family_name": family_group["family_group__family_name"] if family_group["family_group__family_name"] else "",
                        "family_group__category__name": family_group["family_group__category__name"] if family_group["family_group__category__name"] else "",
                        "created_at": utils.get_date_from_datetime(family_group["created_at"]) if family_group["created_at"] else "",
                        "score": family_group["score"] if family_group["score"] else "",
                        "family_group_pic": family_group["family_group__icon_url"] if family_group["family_group__icon_url"] else "",
                        "count": score_wise_rank[family_group["id"]] if family_group["id"] in score_wise_rank else "",
                    })
        else:
            if category_type == "food":
                query.add(Q(category__name__icontains="Food"), query.connector)
            elif category_type == "sports":
                query.add(Q(category__name__icontains="Sports"), query.connector)

            if search_field and search_keyword:
                if search_field == "Family Group Name":
                    query.add(Q(family_group__family_name__icontains=search_keyword), query.connector)
                elif search_field == "Score":
                    query.add(Q(score__icontains=search_keyword), query.connector)

            sort = "-score"
            if sort_by == "family_name":
                sort = "family_group__family_name" if sort_order == "asc" else "-family_group__family_name"
            elif sort_by == "category":
                sort = "category__name" if sort_order == "asc" else "-category__name"
            elif sort_by == "score":
                sort = "score" if sort_order == "asc" else "-score"

            total_records = Leaderboard.objects.filter(query).order_by(sort).values_list("id")
            page_number = request.GET.get('page')
            page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)

            leaderboard_data = Leaderboard.objects.filter(query).order_by(sort).values(
                "id", "family_group__family_name", "category__name", "score", "family_group_id", "family_group__icon_url", "family_group__leader_id",
            )[start:end]
            
            # family_group_ids = list(leaderboard_data.values_list("family_group_id", flat=True))
            # family_group_id_pic = utils.get_family_group_dp(family_group_ids)

            response = {
                "data": [],
                "page_obj": page_obj,
                "page_number": page_number if page_number else 1,
                "total_items": total_items,
                "logged_in_user_data": logged_in_user_data,
                "search_field": search_field or "",
                "search_keyword": search_keyword or "",
                "sort_by": sort_by or "",
                "sort_order": sort_order or "",
                "page_length": page_length if page_length else 10,
                "type": category_type,
            }
            
            score_wise_rank_food = {}
            score_wise_rank_sports = {}
            score_wise_id_food = Leaderboard.objects.filter(family_group__leader_id__in=user_ids).order_by("-score").values("id", "category__name")
            count_food = 0
            count_sports = 0
            for score_id_food in score_wise_id_food:
                if str(score_id_food["category__name"]).lower() == "food":
                    count_food += 1
                    score_wise_rank_food[score_id_food["id"]] = count_food
                elif str(score_id_food["category__name"]).lower() == "sports":
                    count_sports += 1
                    score_wise_rank_sports[score_id_food["id"]] = count_sports

            for leaderboard_dt in leaderboard_data:
                if leaderboard_dt["family_group__leader_id"] in user_ids:
                    count = ""
                    if str(leaderboard_dt["category__name"]).lower() == "food" and leaderboard_dt["id"] in score_wise_rank_food:
                        count = score_wise_rank_food[leaderboard_dt["id"]]
                    if str(leaderboard_dt["category__name"]).lower() == "sports" and leaderboard_dt["id"] in score_wise_rank_sports:
                        count = score_wise_rank_sports[leaderboard_dt["id"]]

                    response["data"].append({
                        "family_group__family_name": leaderboard_dt["family_group__family_name"] if leaderboard_dt["family_group__family_name"] else "",
                        "category__name": leaderboard_dt["category__name"] if leaderboard_dt["category__name"] else "",
                        "score": leaderboard_dt["score"] if leaderboard_dt["score"] is not None else 0,
                        "family_group_pic": leaderboard_dt["family_group__icon_url"] if leaderboard_dt["family_group__icon_url"] else "",
                        "count": count,
                    })

        return render(request, "question/leaderboard.html", response)

    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "question/leaderboard.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})
    

def download_import_questions_samplefile(request):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        full_file_path = 'sample_files/import_questions_sample_file.csv'
        response = utils.download_samplefile(full_file_path, "import_questions_sample_file.csv")
        if response:
            return response
        return render(request, "question/question_list.html", {"msg": "Sample file is not found."})
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "question/question_list.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})


class PrizeInfo(View):
    def get(self, request):
        logged_in_user_data = utils.get_loggedin_user_details(request)
        prize_info = Info.load()
        context = {
            "prize_info": prize_info.prize_info if prize_info else "",
            "logged_in_user_data": logged_in_user_data,
        }
        return render(request, 'prize_info_form.html', context)
     
class UpdatePrizeInfo(View):
    def get(self, request):
        logged_in_user_data = utils.get_loggedin_user_details(request)
        prize_info  = Info.load()
        form = PrizeInfoForm(instance = prize_info)
        context = {
            "form":form,
            "prize_info":prize_info.prize_info if prize_info else "",
            "logged_in_user_data": logged_in_user_data,
        }
        return render(request, 'prize_info_form.html', context)

    def post(self, request):
        prize_info = Info.load()
        form  = PrizeInfoForm(request.POST, instance = prize_info)
        if form.is_valid():
            form.save()
            messages.success(request, "Prize information updated successfully.")
            return redirect('prize_info')
        messages.error(request, "Error in updating prize information.")
        return redirect('prize_info')
    

class ScoreInfo(View):
    def get(self, request):
        logged_in_user_data = utils.get_loggedin_user_details(request)
        leaderboard_info = Info.load()
        context = {
            "leaderboard_info": leaderboard_info.leaderboard_info if leaderboard_info else "",
            "logged_in_user_data": logged_in_user_data,
        }
        return render(request, 'score_info_form.html', context)
     
class UpdateScoreInfo(View):
    def get(self, request):
        logged_in_user_data = utils.get_loggedin_user_details(request)
        leaderboard_info  = Info.load()
        form = ScoreInfoForm(instance = leaderboard_info)
        context = {
            "form": form,
            "leaderboard_info": leaderboard_info.leaderboard_info if leaderboard_info else "",
            "logged_in_user_data": logged_in_user_data,
        }
        return render(request, 'score_info_form.html', context)

    def post(self, request):
        leaderboard_info = Info.load()
        form  = ScoreInfoForm(request.POST, instance = leaderboard_info)
        if form.is_valid():
            form.save()
            messages.success(request, "Score information updated successfully.")
            return redirect('score_info')
        messages.error(request, "Error in updating score information.")
        return redirect('score_info')