from rest_framework import serializers
from ..models import (
    Question, QuestionImage, UserQuestion, Leaderboard, QuizStep, Info, StepsLeaderboard, UserSteps, UserStepImage, UserProfile
)
import random
from django.contrib.auth.models import User
from family.models import FamilyGroups, FamilyGroupFile, Category

class QuestionImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionImage
        fields = ['image']

class QuestionListSerializer(serializers.ModelSerializer):
    question_image = serializers.SerializerMethodField()
    class Meta:
        model = Question
        fields = ['id', 'question', 'type', 'option_type', 'option', 'category', 'score', 'quiz_type', 'date', 'question_image']


    def get_question_image(self, obj):
        image_instance = obj.question_image.first()
        if image_instance and image_instance.image:
            return image_instance.image.url
        return None
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.shuffle and representation.get('option'):
            options = representation['option']
            keys = list(options.keys())
            random.shuffle(keys)
            shuffled_options = {key: options[key] for key in keys}
            representation['option'] = shuffled_options
        return representation
    
class UserQuestionSerializer(serializers.ModelSerializer):
    question  = QuestionListSerializer()
    class Meta:
        model = UserQuestion
        fields = ['id', 'question', 'date', 'user']

class UserAnswerSerializer(serializers.Serializer):
    answer = serializers.CharField()
    family_member_id = serializers.IntegerField()

class FamilyGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = FamilyGroups
        fields = ['family_name']

class FamilyGroupFileSerializer(serializers.ModelSerializer):
    family_group = FamilyGroupSerializer()
    class Meta:
        model = FamilyGroupFile
        fields = ['family_group', 'file', 'file_name']
class LeaderBoardViewSerializer(serializers.ModelSerializer):
    family_group = serializers.SerializerMethodField()
    is_self = serializers.BooleanField()
    #category = serializers.SerializerMethodField()
    class Meta:
        model = Leaderboard
        fields = ['family_group', 'score', 'created_at', 'updated_at', 'is_self']

    def get_family_group(self, obj):
        family_group = obj.family_group
        family_group = FamilyGroups.objects.get(id = family_group.id)
        data = {}
        data["family_group"] = FamilyGroupSerializer(family_group).data
        school_name = UserProfile.objects.filter(user_id=family_group.leader_id_id).values("school").first()
        data["school_name"] = school_name["school"] if school_name["school"] else ""
        if family_group.icon_url:
            data['file'] = family_group.icon_url
        return data

    
class StepsLeaderBoardViewSerializer(serializers.ModelSerializer):
    family_group = serializers.SerializerMethodField()
    is_self = serializers.BooleanField()
    class Meta:
        model = StepsLeaderboard
        fields = ['family_group', 'score', 'created_at', 'updated_at', 'is_self']

    def get_family_group(self, obj):
        family_group = obj.family_group
        family_group = FamilyGroups.objects.get(id = family_group.id)
        data = {}
        data["family_group"] = FamilyGroupSerializer(family_group).data
        school_name = UserProfile.objects.filter(user_id=family_group.leader_id_id).values("school").first()
        data["school_name"] = school_name["school"] if school_name["school"] else ""
        if family_group.icon_url:
            data['file'] = family_group.icon_url
        return data

class InfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Info
        fields = ['prize_info','leaderboard_info']

class GetUserSteps(serializers.Serializer):
    steps = serializers.IntegerField()


class UserStepsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSteps
        fields = ['date', 'steps']

class QuizStepsSerializer(serializers.ModelSerializer):
    steps = UserStepsSerializer(many=True)
    class Meta:
        model = QuizStep
        fields = ['id', 'week_start_date', 'steps_score', 'image', 'steps']