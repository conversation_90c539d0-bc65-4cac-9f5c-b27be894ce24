{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Leaderboard' %}{% endblock %}
{% block content %}
{% load static %}

</style>

{% if msg %}
<div id="idLeaderboardMsg" class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
    <span class="font-medium">{{ msg }}</span>
    <span onclick="closeMsg('idLeaderboardMsg')">
        <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </span>
</div>
{% endif %}
 
<div class="p-4">
    <div class="mb-3 justify-between flex">
        <h1 class="text-2xl font-bold mb-4">{% trans 'Leaderboard List' %}</h1>
    </div>

    <div class="mb-3 flex flex-wrap">
        <a href="?type=food" style="width: 160px;" class="min-w-[160px] p-1.5 mb-1 mr-1 rounded-lg text-center bg-gray-200 shadow-lg hover:bg-blue-200 hover:border-b-2 hover:border-blue-600 hover:font-medium {% if type == 'food' %}tab-active{% endif %}">Food</a>
        <a href="?type=sports" style="width: 160px;" class="min-w-[160px] p-1.5 mb-1 mr-1 rounded-lg text-center bg-gray-200 shadow-lg hover:bg-blue-200 hover:border-b-2 hover:border-blue-600 hover:font-medium {% if type == 'sports' %}tab-active{% endif %}">Sports</a>
        <a href="?type=steps" style="width: 160px;" class="min-w-[160px] p-1.5 mb-1 mr-1 rounded-lg text-center bg-gray-200 shadow-lg hover:bg-blue-200 hover:border-b-2 hover:border-blue-600 hover:font-medium {% if type == 'steps' %}tab-active{% endif %}">Steps</a>
    </div>  

    <div class="flex flex-col md:flex-row w-full mx-auto mb-3 justify-between items-center space-y-4 md:space-y-0 md:space-x-4">

        <!-- Page length dropdown -->
        <div id="dropdown" class="flex items-center space-x-2 z-10 w-full md:w-auto 1">
            <span>Show</span>
                <select id="id_pageLength" name="page_length" onchange="changePageLength('{{type}}')" class="shadow-lg w-fit bg-white hover:bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>
            <div class="relative flex flex-wrap items-center w-full md:max-w-md">
                <select id="id_userField" name="user_field" class="w-full shadow-lg bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 md:w-auto mr-px mb-2 md:mb-0 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 rounded-l-lg">
                    {% if type == "steps" %}
                    <option value="">Select Field</option>
                    <option value="Family Name">Family Name</option>
                    <option value="Total Score">Total Score</option>
                    {% else %}
                    <option value="">Select Field</option>
                    <option value="Family Group Name">Family Group Name</option>
                    <option value="Score">Score</option>
                    {% endif%}
                </select>
                <input type="search" id="searchKeyword" value="{{ search_keyword }}" name="search_with_field" class="flex-grow bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 mr-px shadow-lg w-full md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500" placeholder="Select the field then type..." required />
                <button type="button" id="id_clearSearch" onclick="clearSearch('{{type}}')" title="Clear" class="{% if not search_field or not search_keyword %} hidden {% endif %} p-2.5 mb-2 md:mb-0 text-sm font-medium text-gray-600 bg-transparent border-none cursor-pointer focus:outline-none focus:ring-0 md:rounded-none">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 6l8 8M14 6l-8 8" />
                    </svg>
                    <span class="sr-only">Clear</span>
                </button>
                <button type="button" id="id_searchButton" onclick="searchLeaderboardList('{{type}}')" title="Search" class="shadow-lg p-2.5 text-sm font-medium text-white bg-blue-500 rounded-r-lg hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 border border-gray-300">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                    <span class="sr-only">Search</span>
                </button>
            </div>
        </div>
        <div class="relative w-full shadow-lg rounded-lg overflow-x-auto sm:rounded-lg 3">
            {% if type == "steps" %}
            <div class="table-container">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                    <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                        <tr>
                            <th scope="col" class="px-6 py-3">{% trans 'Family Rank' %}</th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items- justify-center">
                                    {% trans 'Family Group Photo' %}
                                </div>
                            </th>   
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'family_name' and request.GET.order == 'asc' %}&sort_by=family_name&order=desc{% else %}&sort_by=family_name&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                    {% trans 'Family Name' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/20000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_name' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_name' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'category' and request.GET.order == 'asc' %}&sort_by=category&order=desc{% else %}&sort_by=category&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                    {% trans 'Category' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'category' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'category' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th> 
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'score' and request.GET.order == 'asc' %}&sort_by=score&order=desc{% else %}&sort_by=score&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                    {% trans 'Total Score' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'score' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'score' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                        </tr>
                    </thead>

                    <tbody>
                        {% if data %}
                            {% for dt in data %}
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4">{{ dt.count }}.</td>
                                    <td class="px-6 py-4 image-container">
                                        {% if dt.family_group_pic %}
                                        <div style="text-align: -webkit-center;">
                                            <img src="{{dt.family_group_pic}}" class="image-style rounded border" alt="">
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ dt.family_group__family_name }}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ dt.family_group__category__name }}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ dt.score }}
                                    </td>
                                </tr>
                            {% endfor %}
                        {% else %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="5" class="px-6 py-4 text-base">Data is not available.</td></tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="table-container">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                    <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                        <tr>
                            <th scope="col" class="px-6 py-3">{% trans 'Family Rank' %}</th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center justify-center">
                                    {% trans 'Family Group Photo' %}
                                </div>
                            </th>                
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'family_name' and request.GET.order == 'asc' %}&sort_by=family_name&order=desc{% else %}&sort_by=family_name&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                    {% trans 'Family Group Name' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_name' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'family_name' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'score' and request.GET.order == 'asc' %}&sort_by=score&order=desc{% else %}&sort_by=score&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                    {% trans 'Score' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'score' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'score' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>                     
                        </tr>
                    </thead>

                    <tbody>
                        {% if data %}
                            {% for dt in data %}
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4">{{ dt.count }}.</td>
                                    <td class="px-6 py-4">
                                        {% if dt.family_group_pic %}
                                        <div style="text-align: -webkit-center;">
                                            <img src="{{dt.family_group_pic}}" class="image-style rounded border" alt="">
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ dt.family_group__family_name }}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ dt.score }}
                                    </td>
                                </tr>
                            {% endfor %}
                        {% else %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="4" class="px-6 py-4 text-base">Data is not available.</td></tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            {% endif %}
               
<nav class="flex flex-col bg-gray-50 md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4" aria-label="Table navigation">
    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
        Showing
        <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
        of
        <span class="font-semibold text-gray-900 dark:text-white">{{ total_items }}</span>
    </span>
    <ul class="inline-flex items-stretch -space-x-px">
        {% if page_obj.has_previous %}
        <li>
            <a href="?page={{ page_obj.previous_page_number }}&length={{ page_length }}{% if sort_by and sort_order %}&sort_by={{ sort_by }}&order={{ sort_order }}{% endif %}{% if type %}&type={{ type }}{% endif %}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                <span class="sr-only">Previous</span>
                <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
            </a>
        </li>
        {% endif %}
        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
            <li>
                <a
                class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    {{ num }}
                </a>
            </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %} <li>
                <a href="?page={{ num }}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if sort_by and sort_order %}&sort_by={{ sort_by }}&order={{ sort_order }}{% endif %}{% if type %}&type={{ type }}{% endif %}"
                class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    {{ num }}
                </a>
            </li>
            {% endif %}
        {% endfor %}
        {% if page_obj.has_next %}
        <li>
            <a href="?page={{ page_obj.next_page_number }}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if sort_by and sort_order %}&sort_by={{ sort_by }}&order={{ sort_order }}{% endif %}{% if type %}&type={{ type }}{% endif %}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                <span class="sr-only">Next</span>
                <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
</div>
</div>
</div>
</div>
</div>

<script src="{% static 'question/js/leaderboard.js'%}?v=0.4"></script>
<script>
 
    $(document).ready(function() {
        msg = '{{ msg }}';
        if (msg) {
            setTimeout(() => {
                $('#idLeaderboardMsg').hide();
            }, 10000);
        }
 
        var dropdown = document.getElementById('id_userField');
        var search_field = '{{ search_field }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === search_field) {
                dropdown.options[i].selected = true;
                break;
            }
        }
 
        var dropdown = document.getElementById('id_pageLength');
        var page_length = '{{ page_length }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === page_length) {
                dropdown.options[i].selected = true;
                break;
            }
        }
    });
 
</script>
 
{% endblock content %}
 