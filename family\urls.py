from django.urls import path, include
from family.views import FamilyGroupCreate
from family.views import *
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register('', FamilyGroupCreate, basename="create_family")
router.register('', MemberDetails, basename="member_details")
router.register('', WeeklyUploadFileView, basename="upload_family_photo")


urlpatterns = [
    path('api/', include(router.urls)),
    #path('api/update-family/<int:leader_id>/', FamilyGroupCreate.as_view(), name='update_family_group'),
    path('api/join-invitation/', JoinInvitation.as_view(), name='join_invitation'),


    path('families/', get_families, name="families"),
    # path('delete-families/', deletefamilies, name="delete-families"),
    path('familymember/<str:id>/', familymemberdetails, name="familymember"),
    path('family-photos/<str:id>/', family_photos, name="family_photos"),
    path('export-familydata/', family_list_export, name="export-familydata"),
    path('delete-weekly-family-photo/', delete_weekly_family_photo, name="delete-weekly-family-photo"),

    # path('add-family-member/', AddFamilyMember.as_view(), name='add-family'),
    # path('join-family/', JoinFamilyGroup.as_view(), name='join-family'),
    
]