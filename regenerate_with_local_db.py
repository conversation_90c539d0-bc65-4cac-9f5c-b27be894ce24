#!/usr/bin/env python3
"""
使用本地数据库重新生成所有导出文件
"""

import psycopg2
import csv
import json
from datetime import datetime, date

# 导入本地数据库配置
from hku_local_config import HKU_LOCAL_CONFIG

class LocalHKUExporter:
    """本地HKU数据导出器"""
    
    def __init__(self):
        self.db_config = HKU_LOCAL_CONFIG
        print(f"📡 使用本地数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
    
    def connect_db(self):
        """连接数据库"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return None
    
    def analyze_data_range(self):
        """分析数据的日期范围"""
        print("\n🔍 分析数据日期范围...")
        
        conn = self.connect_db()
        if not conn:
            return None, None
        
        try:
            cursor = conn.cursor()
            
            # 检查quiz数据的日期范围
            cursor.execute("""
                SELECT 
                    MIN(date) as min_date,
                    MAX(date) as max_date,
                    COUNT(*) as total_records,
                    COUNT(DISTINCT date) as unique_dates
                FROM question_userquestion 
                WHERE answered = true
                AND is_deleted = false
            """)
            
            min_date, max_date, total_records, unique_dates = cursor.fetchone()
            
            print(f"📅 数据日期范围: {min_date} 到 {max_date}")
            print(f"📊 总记录数: {total_records}")
            print(f"📆 总天数: {unique_dates}")
            
            # 检查最近的数据
            cursor.execute("""
                SELECT date, COUNT(*) as daily_count
                FROM question_userquestion
                WHERE answered = true
                AND is_deleted = false
                GROUP BY date
                ORDER BY date DESC
                LIMIT 10
            """)
            
            recent_data = cursor.fetchall()
            print(f"\n📋 最近10天的数据:")
            for date_val, count in recent_data:
                print(f"   {date_val}: {count} 条记录")
            
            cursor.close()
            conn.close()
            
            return min_date, max_date
            
        except Exception as e:
            print(f"❌ 分析数据范围失败: {e}")
            if conn:
                conn.close()
            return None, None
    
    def export_category_data(self, category_name, start_date=None, end_date=None):
        """导出指定类别的数据"""
        print(f"\n🔄 导出{category_name}类别数据...")
        
        conn = self.connect_db()
        if not conn:
            return None
        
        try:
            cursor = conn.cursor()
            
            # 构建日期过滤条件
            date_filter = ""
            params = [category_name]
            
            if start_date and end_date:
                date_filter = "AND uq.date BETWEEN %s AND %s"
                params.extend([start_date, end_date])
                print(f"📅 日期范围: {start_date} 到 {end_date}")
            
            # 查询指定类别数据
            query = f"""
            SELECT 
                uq.date as answer_date,
                fg.family_name,
                au.username as member_name,
                q.category as question_category,
                q.quiz_type,
                q.question,
                q.answer as correct_answer,
                uq.selected_answer as given_answer_letter,
                q.option as question_options,
                uq.is_correct,
                CASE WHEN uq.is_correct THEN q.score ELSE 0 END as question_score,
                q.score as max_question_score,
                uq.user_id,
                fg.id as family_group_id,
                COALESCE(sc.steps, 0) as step_count
            FROM question_userquestion uq
            JOIN question_question q ON uq.question_id = q.id
            JOIN auth_user au ON uq.user_id = au.id
            JOIN family_familymembers fm ON au.id = fm.member_id
            JOIN family_familygroups fg ON fm.family_group_id = fg.id
            JOIN family_category c ON fg.category_id = c.id
            LEFT JOIN question_usersteps sc ON uq.user_id = sc.user_id AND uq.date = sc.date
            WHERE uq.answered = true
            AND uq.is_deleted = false
            AND q.is_deleted = false
            AND fm.is_deleted = false
            AND fm.is_member = true
            AND c.name = %s
            {date_filter}
            ORDER BY uq.date, fg.family_name, au.username, q.quiz_type, q.id
            """
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            print(f"📊 找到 {len(results)} 条{category_name}类别记录")
            
            # 统计信息
            families = set(row[1] for row in results)
            users = set(row[2] for row in results)
            dates = set(row[0] for row in results)
            
            print(f"📋 统计信息:")
            print(f"   家庭组数: {len(families)}")
            print(f"   用户数: {len(users)}")
            print(f"   日期数: {len(dates)}")
            
            # 生成CSV文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"hku_exercise_export_{category_name}_{timestamp}.csv"
            
            self._write_csv_with_options(results, filename, category_name)
            
            cursor.close()
            conn.close()
            
            return filename
            
        except Exception as e:
            print(f"❌ {category_name}类别导出失败: {e}")
            if conn:
                conn.close()
            return None
    
    def _write_csv_with_options(self, results, filename, category_type):
        """写入CSV文件，包含完整的选项信息"""
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入表头
            headers = [
                'Answer Date', 'Family Name', 'Member Name', 'Question Category',
                'Quiz Type', 'Question', 'Correct Answer', 'Given Answer',
                'All Answer Options', 'Is Correct', 'Question Score', 'Max Question Score',
                'User ID', 'Family Group ID', 'Step Count',
                'Daily Checkin Score', 'Weekly Family Photo Score',
                'Food Screenshot Score', 'Steps Screenshot Score', 'Quiz Score'
            ]
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            
            # 按用户和日期分组处理数据
            user_date_data = {}
            for row in results:
                user_id = row[12]  # user_id
                answer_date = row[0]  # answer_date
                key = (user_id, answer_date)
                
                if key not in user_date_data:
                    user_date_data[key] = []
                user_date_data[key].append(row)
            
            print(f"📝 写入CSV文件: {filename}")
            
            # 写入数据行
            for (user_id, answer_date), rows in user_date_data.items():
                # 计算当天的Quiz分数
                quiz_score = sum(row[10] for row in rows)  # question_score
                
                # 为每个问题写入一行，但只在第一行显示分数
                for i, row in enumerate(rows):
                    # 处理答案选项
                    given_answer_letter = row[7]  # 用户选择的字母
                    question_options = row[8]     # JSON格式的选项
                    quiz_type = row[4]            # Quiz类型
                    
                    # 对于Bonus questions，解析选项并获取完整答案文本
                    if quiz_type == 'Bonus' and question_options:
                        try:
                            options_dict = json.loads(question_options) if isinstance(question_options, str) else question_options
                            # 获取用户选择的完整答案
                            given_answer_full = options_dict.get(given_answer_letter, given_answer_letter) if given_answer_letter else ''
                            # 格式化所有选项
                            all_options = '; '.join([f"{k}: {v}" for k, v in options_dict.items()])
                        except:
                            given_answer_full = given_answer_letter if given_answer_letter else ''
                            all_options = str(question_options) if question_options else ''
                    else:
                        given_answer_full = given_answer_letter if given_answer_letter else ''
                        all_options = ''
                    
                    # 构建CSV行数据
                    csv_row = [
                        row[0],   # Answer Date
                        row[1],   # Family Name
                        row[2],   # Member Name
                        row[3],   # Question Category
                        row[4],   # Quiz Type
                        row[5],   # Question
                        row[6] if quiz_type != 'Bonus' else '',  # Correct Answer (空白对于Bonus questions)
                        given_answer_full,  # Given Answer (完整文本)
                        all_options,  # All Answer Options (所有选项)
                        row[9],   # Is Correct
                        row[10],  # Question Score
                        row[11],  # Max Question Score
                        row[12],  # User ID
                        row[13],  # Family Group ID
                        row[14],  # Step Count
                    ]
                    
                    # 添加分数字段（只在第一行显示）
                    if i == 0:
                        if category_type == 'Food':
                            csv_row.extend([
                                100,  # Daily Checkin Score
                                0,    # Weekly Family Photo Score (不计分)
                                500,  # Food Screenshot Score
                                0,    # Steps Screenshot Score (饮食类别不适用)
                                quiz_score  # Quiz Score
                            ])
                        else:  # Sports
                            csv_row.extend([
                                100,  # Daily Checkin Score
                                0,    # Weekly Family Photo Score (不计分)
                                0,    # Food Screenshot Score (运动类别不适用)
                                500,  # Steps Screenshot Score
                                quiz_score  # Quiz Score
                            ])
                    else:
                        csv_row.extend([0, 0, 0, 0, 0])  # 其他行不重复计分
                    
                    writer.writerow(csv_row)
        
        print(f"✅ {category_type}类别导出完成: {filename}")

def main():
    """主函数"""
    print("🚀 使用本地数据库重新生成HKU导出文件")
    print("=" * 60)
    
    exporter = LocalHKUExporter()
    
    # 分析数据范围
    min_date, max_date = exporter.analyze_data_range()
    
    if not min_date or not max_date:
        print("❌ 无法获取数据范围")
        return
    
    # 使用全部数据范围
    print(f"\n📅 使用全部数据范围: {min_date} 到 {max_date}")
    start_date = min_date
    end_date = max_date
    
    print(f"\n📅 使用日期范围: {start_date} 到 {end_date}")
    
    # 导出饮食类别
    food_file = exporter.export_category_data('Food', start_date, end_date)
    
    # 导出运动类别
    sports_file = exporter.export_category_data('Sports', start_date, end_date)
    
    print(f"\n🎉 重新导出完成!")
    if food_file:
        print(f"📁 饮食类别文件: {food_file}")
    if sports_file:
        print(f"📁 运动类别文件: {sports_file}")
    
    print(f"\n📋 下一步:")
    print(f"   1. 检查生成的CSV文件")
    print(f"   2. 运行分离脚本生成详细分数文件")
    print(f"   3. 验证数据完整性")

if __name__ == "__main__":
    main()
