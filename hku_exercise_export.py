#!/usr/bin/env python
"""
HKU Student Quiz - Exercise Export
导出详细的exercise数据，包含每个问题的详细信息
"""

import psycopg2
from datetime import datetime, timedelta
import datetime as dt
import csv
from collections import defaultdict

class HKUExerciseExporter:
    """HKU Exercise详细导出器"""
    
    def __init__(self):
        self.db_config = {
            'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
            'port': 5432,
            'database': 'hku_staging',
            'user': 'postgres',
            'password': 'k9Z#x$Lh3&!V'
        }
        
        self.score_config = {
            'USER_DAILY_CHECKIN_SCORE': 100,
            'WEEKLY_FAMILY_PHOTO_SCORE': 100,
            'FOOD_SCREENSHOT_SCORE': 500,
            'STEPS_SCREENSHOT_SCORE': 500
        }
        
        self.conn = None
        self.steps_start_date = None

    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            print("✅ 成功连接到HKU数据库!")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            print("🔌 数据库连接已断开")
    
    def execute_query(self, query, params=None):
        """执行SQL查询"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(query, params)
            columns = [desc[0] for desc in cursor.description]
            results = cursor.fetchall()
            cursor.close()
            
            return [dict(zip(columns, row)) for row in results]
        except Exception as e:
            print(f"❌ 查询执行失败: {e}")
            print(f"Query: {query}")
            return []

    def determine_steps_start_date(self):
        """确定步数开始计算的日期"""
        if not self.connect():
            # 如果连接失败，使用默认日期
            self.steps_start_date = datetime(2025, 5, 15).date()
            return

        try:
            # 查找最早的步数记录日期
            query = """
            SELECT MIN(date) as earliest_step_date
            FROM question_usersteps
            WHERE is_deleted = false
            AND steps > 0
            """
            result = self.execute_query(query)

            if result and result[0]['earliest_step_date']:
                self.steps_start_date = result[0]['earliest_step_date']
                print(f"📊 步数开始计算日期: {self.steps_start_date}")
            else:
                # 如果没有步数记录，使用默认日期
                self.steps_start_date = datetime(2025, 5, 15).date()
                print(f"⚠️  未找到步数记录，使用默认日期: {self.steps_start_date}")

        except Exception as e:
            print(f"❌ 确定步数开始日期失败: {e}")
            self.steps_start_date = datetime(2025, 5, 15).date()
        finally:
            self.disconnect()
    
    def get_detailed_question_data(self, start_date=None, end_date=None, days_back=30):
        """获取详细的问题答题数据"""
        # 设置日期范围
        if not start_date or not end_date:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days_back)
        
        query = """
        SELECT
            uq.date as answer_date,
            fg.family_name,
            au.username as member_name,
            q.category as question_category,
            q.quiz_type,
            q.question,
            q.answer as correct_answer,
            uq.selected_answer as given_answer,
            uq.is_correct,
            CASE WHEN uq.is_correct THEN q.score ELSE 0 END as question_score,
            q.score as max_question_score,
            uq.user_id,
            fg.id as family_group_id,
            fm.member_id
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND uq.date BETWEEN %s AND %s
        ORDER BY uq.date, fg.family_name, au.username, uq.created_at
        """
        
        return self.execute_query(query, (start_date, end_date))

    def debug_sports_participation(self, start_date, end_date):
        """调试运动类别参与情况"""
        query = """
        SELECT DISTINCT
            fg.family_name,
            au.username as member_name,
            q.category as question_category,
            COUNT(*) as answer_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND uq.date BETWEEN %s AND %s
        AND q.category IN ('Sports', 'Walk')
        GROUP BY fg.family_name, au.username, q.category
        ORDER BY fg.family_name, au.username, q.category
        """
        return self.execute_query(query, (start_date, end_date))

    def check_all_sports_dates(self):
        """检查所有运动类别答题的日期"""
        query = """
        SELECT
            uq.date as answer_date,
            fg.family_name,
            COUNT(*) as answer_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND q.category IN ('Sports', 'Walk')
        GROUP BY uq.date, fg.family_name
        ORDER BY uq.date, fg.family_name
        """
        return self.execute_query(query)

    def get_member_daily_scores(self, member_id, date):
        """获取成员某日的各项分数"""
        # 获取Quiz总分
        quiz_score_query = """
        SELECT COALESCE(SUM(CASE WHEN uq.is_correct THEN q.score ELSE 0 END), 0) as quiz_score
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        WHERE uq.user_id = %s
        AND uq.date = %s
        AND uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        """
        quiz_result = self.execute_query(quiz_score_query, (member_id, date))
        quiz_score = quiz_result[0]['quiz_score'] if quiz_result else 0
        
        # 获取Checkin分数
        checkin_query = """
        SELECT COUNT(*) * %s as checkin_score
        FROM user_authentication_usercheckin
        WHERE user_id = %s
        AND created_on::date = %s
        """
        checkin_result = self.execute_query(checkin_query, (self.score_config['USER_DAILY_CHECKIN_SCORE'], member_id, date))
        checkin_score = checkin_result[0]['checkin_score'] if checkin_result else 0
        
        return {
            'quiz_score': quiz_score,
            'checkin_score': checkin_score,
            'total_member_score': quiz_score + checkin_score
        }
    
    def get_family_daily_scores(self, family_group_id, date):
        """获取家庭组某日的分数"""
        # 获取家庭照片分数
        photo_query = """
        SELECT COALESCE(SUM(score), 0) as photo_score
        FROM family_weeklyuploadfile
        WHERE family_group_id = %s
        AND created_on::date = %s
        """
        photo_result = self.execute_query(photo_query, (family_group_id, date))
        photo_score = photo_result[0]['photo_score'] if photo_result else 0
        
        # 获取所有成员当日总分
        members_query = """
        SELECT fm.member_id
        FROM family_familymembers fm
        WHERE fm.family_group_id = %s
        AND fm.is_deleted = false
        AND fm.is_member = true
        """
        members = self.execute_query(members_query, (family_group_id,))
        
        total_family_score = photo_score
        for member in members:
            member_scores = self.get_member_daily_scores(member['member_id'], date)
            total_family_score += member_scores['total_member_score']
        
        return {
            'photo_score': photo_score,
            'total_family_score': total_family_score
        }
    
    def get_member_step_data(self, member_id, date):
        """获取成员步数数据"""
        step_query = """
        SELECT COALESCE(SUM(steps), 0) as daily_steps
        FROM question_usersteps
        WHERE user_id = %s
        AND date = %s
        AND is_deleted = false
        """
        result = self.execute_query(step_query, (member_id, date))
        return result[0]['daily_steps'] if result else 0
    
    def get_family_step_data(self, family_group_id, date):
        """获取家庭组步数数据"""
        # 获取所有成员
        members_query = """
        SELECT fm.member_id
        FROM family_familymembers fm
        WHERE fm.family_group_id = %s
        AND fm.is_deleted = false
        AND fm.is_member = true
        """
        members = self.execute_query(members_query, (family_group_id,))
        
        total_family_steps = 0
        for member in members:
            member_steps = self.get_member_step_data(member['member_id'], date)
            total_family_steps += member_steps
        
        return total_family_steps
    
    def get_family_total_scores(self, family_group_id):
        """获取家庭组总分（不包含步数分数）"""
        query = """
        SELECT
            COALESCE(l.score, 0) as leaderboard_score
        FROM family_familygroups fg
        LEFT JOIN question_leaderboard l ON fg.id = l.family_group_id AND l.is_deleted = false
        WHERE fg.id = %s
        """
        result = self.execute_query(query, (family_group_id,))
        if result:
            return result[0]['leaderboard_score'] or 0
        return 0

    def get_family_steps_scores(self, family_group_id):
        """获取家庭组步数分数"""
        query = """
        SELECT
            COALESCE(sl.score, 0) as steps_leaderboard_score
        FROM family_familygroups fg
        LEFT JOIN question_stepsleaderboard sl ON fg.id = sl.family_group_id AND sl.is_deleted = false
        WHERE fg.id = %s
        """
        result = self.execute_query(query, (family_group_id,))
        if result:
            return result[0]['steps_leaderboard_score'] or 0
        return 0
    
    def generate_exercise_export(self, start_date=None, end_date=None, days_back=30):
        """生成exercise导出数据 - 分离饮食和运动类别"""
        # 确定步数开始日期（如果还没有确定的话）
        if self.steps_start_date is None:
            self.determine_steps_start_date()

        if not self.connect():
            return None

        try:
            # 设置固定日期范围
            if not start_date or not end_date:
                start_date = datetime(2025, 1, 1).date()  # 扩大到整年
                end_date = datetime(2025, 12, 31).date()

            print(f"📅 导出日期范围: {start_date} 到 {end_date}")

            # 获取详细问题数据
            question_data = self.get_detailed_question_data(start_date, end_date)
            print(f"📝 找到 {len(question_data)} 条问题记录")

            # 调试：检查类别分布
            category_counts = {}
            for q in question_data:
                cat = q['question_category']
                category_counts[cat] = category_counts.get(cat, 0) + 1

            print(f"📊 类别分布:")
            for cat, count in category_counts.items():
                print(f"   {cat}: {count} 条记录")

            # 调试：检查运动类别的家庭组分布
            sports_families = {}
            for q in question_data:
                if q['question_category'] in ['Sports', 'Walk']:
                    family = q['family_name']
                    sports_families[family] = sports_families.get(family, 0) + 1

            print(f"🏃 运动类别家庭组分布:")
            for family, count in sports_families.items():
                print(f"   {family}: {count} 条记录")

            # 调试：检查数据库中的运动类别参与情况
            print(f"🔍 验证运动类别数据完整性...")

            print(f"✅ 数据验证完成，开始生成CSV文件...")

            # 分离饮食和运动数据
            food_data = []
            sports_data = []
            
            # 缓存每日分数数据以提高性能
            daily_scores_cache = {}
            family_totals_cache = {}
            family_steps_cache = {}
            
            for question in question_data:
                date = question['answer_date']
                member_id = question['user_id']
                family_group_id = question['family_group_id']
                category = question['question_category']

                # 缓存键
                member_date_key = f"{member_id}_{date}"
                family_date_key = f"{family_group_id}_{date}"

                # 获取成员当日分数（使用缓存）
                if member_date_key not in daily_scores_cache:
                    daily_scores_cache[member_date_key] = self.get_member_daily_scores(member_id, date)
                member_scores = daily_scores_cache[member_date_key]

                # 获取家庭当日分数（使用缓存）
                if family_date_key not in daily_scores_cache:
                    daily_scores_cache[family_date_key] = self.get_family_daily_scores(family_group_id, date)
                family_scores = daily_scores_cache[family_date_key]

                # 获取家庭组总分（不包含步数）
                if family_group_id not in family_totals_cache:
                    family_totals_cache[family_group_id] = self.get_family_total_scores(family_group_id)
                family_total = family_totals_cache[family_group_id]

                # 获取家庭组步数分数
                if family_group_id not in family_steps_cache:
                    family_steps_cache[family_group_id] = self.get_family_steps_scores(family_group_id)
                family_steps_score = family_steps_cache[family_group_id]

                # 获取步数数据
                member_steps = self.get_member_step_data(member_id, date)
                family_steps = self.get_family_step_data(family_group_id, date)

                # 构建导出记录
                export_record = {
                    'Date': date.strftime('%d-%b-%Y'),
                    'Family Group Name': question['family_name'],
                    'Family Member': question['member_name'],
                    'Question Category': question['question_category'],
                    'Quiz Type': question['quiz_type'].title(),
                    'Question': question['question'][:50] + '...' if len(question['question']) > 50 else question['question'],
                    'Correct Answer': question['correct_answer'],
                    'Given Answer': question['given_answer'] or '',
                    'Is Correct': 'TRUE' if question['is_correct'] else 'FALSE',
                    'Score': question['question_score'],
                    'Quiz Score': member_scores['quiz_score'],
                    'Checkin Score': member_scores['checkin_score'],
                    'Total Date Member Score': member_scores['total_member_score'],
                    'Total Date Family Group Score': family_scores['total_family_score'],
                    'Family Selfie Upload': family_scores['photo_score'],
                    'Total Final Family Group Score': family_total,
                    'Date Member Step Count': member_steps,
                    'Date Team Step Count': family_steps,
                    'Total Final Step Count': family_steps_score  # 步数分数
                }

                # 根据类别分离数据
                if category == 'Food':
                    food_data.append(export_record)
                elif category in ['Sports', 'Walk']:  # Sports和Walk都归类为运动
                    sports_data.append(export_record)

            return {
                'food_data': food_data,
                'sports_data': sports_data
            }
            
        finally:
            self.disconnect()
    
    def export_to_csv(self, data, filename=None, category_type=""):
        """导出数据到CSV"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if category_type:
                filename = f"hku_exercise_export_{category_type}_{timestamp}.csv"
            else:
                filename = f"hku_exercise_export_{timestamp}.csv"

        if not data:
            print(f"❌ 没有{category_type}数据可导出")
            return None

        fieldnames = [
            'Date', 'Family Group Name', 'Family Member', 'Question Category', 'Quiz Type',
            'Question', 'Correct Answer', 'Given Answer', 'Is Correct', 'Score',
            'Quiz Score', 'Checkin Score', 'Total Date Member Score', 'Total Date Family Group Score',
            'Family Selfie Upload', 'Total Final Family Group Score', 'Date Member Step Count',
            'Date Team Step Count', 'Total Final Step Count'
        ]

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for row in data:
                writer.writerow(row)

        print(f"📊 {category_type} CSV导出完成: {filename}")
        print(f"📝 导出记录数: {len(data)}")
        return filename

    def export_separated_data(self, data_dict):
        """导出分离的饮食和运动数据"""
        exported_files = []

        # 导出饮食数据
        if data_dict['food_data']:
            food_filename = self.export_to_csv(data_dict['food_data'], category_type="Food")
            if food_filename:
                exported_files.append(food_filename)

        # 导出运动数据
        if data_dict['sports_data']:
            sports_filename = self.export_to_csv(data_dict['sports_data'], category_type="Sports")
            if sports_filename:
                exported_files.append(sports_filename)

        return exported_files


def main():
    """主函数"""
    print("🎓 HKU Student Quiz - Exercise Export")
    print("📝 详细的问题答题记录导出")
    print("=" * 60)
    
    try:
        exporter = HKUExerciseExporter()

        # 使用固定日期范围
        start_date = datetime(2025, 1, 1).date()
        end_date = datetime(2025, 12, 31).date()

        print(f"\n📅 固定导出日期范围: {start_date} 到 {end_date}")
        print("\n🔄 正在生成Exercise导出数据...")
        print("⏳ 这可能需要一些时间，请耐心等待...")

        data_dict = exporter.generate_exercise_export(start_date, end_date)

        if data_dict and (data_dict['food_data'] or data_dict['sports_data']):
            exported_files = exporter.export_separated_data(data_dict)

            print(f"\n✅ 导出完成!")
            print(f"📁 导出文件数: {len(exported_files)}")
            for filename in exported_files:
                print(f"   📄 {filename}")

            # 显示统计信息
            food_count = len(data_dict['food_data'])
            sports_count = len(data_dict['sports_data'])
            total_count = food_count + sports_count

            print(f"\n📊 数据统计:")
            print(f"   🍎 饮食类别记录: {food_count}")
            print(f"   🏃 运动类别记录: {sports_count}")
            print(f"   📝 总记录数: {total_count}")

            # 显示样本数据
            if food_count > 0:
                print(f"\n📋 饮食类别样本数据 (前3条):")
                for i, row in enumerate(data_dict['food_data'][:3]):
                    print(f"   {i+1}. {row['Date']} | {row['Family Group Name']} | {row['Family Member']} | {row['Question Category']} | 分数: {row['Score']}")

            if sports_count > 0:
                print(f"\n📋 运动类别样本数据 (前3条):")
                for i, row in enumerate(data_dict['sports_data'][:3]):
                    print(f"   {i+1}. {row['Date']} | {row['Family Group Name']} | {row['Family Member']} | {row['Question Category']} | 分数: {row['Score']}")
        else:
            print("❌ 没有找到数据")
            
    except KeyboardInterrupt:
        print("\n⏹️  用户取消操作")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
