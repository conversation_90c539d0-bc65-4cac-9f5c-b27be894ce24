#!/usr/bin/env python3
"""
调试数据库连接问题
"""

import psycopg2
import socket
import ssl

def test_direct_connection():
    """直接测试数据库连接"""
    
    configs = [
        # 原始配置
        {
            'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
            'port': 5432,
            'database': 'hku_staging',
            'user': 'postgres',
            'password': 'k9Z#x$Lh3&!V'
        },
        # 添加SSL配置
        {
            'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
            'port': 5432,
            'database': 'hku_staging',
            'user': 'postgres',
            'password': 'k9Z#x$Lh3&!V',
            'sslmode': 'require'
        },
        # 尝试不同的SSL模式
        {
            'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
            'port': 5432,
            'database': 'hku_staging',
            'user': 'postgres',
            'password': 'k9Z#x$Lh3&!V',
            'sslmode': 'prefer'
        }
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n🔄 尝试配置 {i}...")
        print(f"   SSL模式: {config.get('sslmode', '默认')}")
        
        try:
            conn = psycopg2.connect(**config)
            print(f"✅ 配置 {i} 连接成功!")
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1;")
            result = cursor.fetchone()
            print(f"✅ 查询测试成功: {result}")
            
            cursor.close()
            conn.close()
            return config
            
        except Exception as e:
            print(f"❌ 配置 {i} 失败: {e}")
            print(f"   错误类型: {type(e).__name__}")
    
    return None

def test_socket_connection():
    """测试socket连接"""
    print(f"\n🔌 测试socket连接...")
    
    host = 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com'
    port = 5432
    
    try:
        # 创建socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30)  # 增加超时时间
        
        print(f"🔍 解析主机名: {host}")
        ip = socket.gethostbyname(host)
        print(f"✅ IP地址: {ip}")
        
        print(f"🔗 连接到 {ip}:{port}")
        result = sock.connect_ex((ip, port))
        
        if result == 0:
            print(f"✅ Socket连接成功")
            sock.close()
            return True
        else:
            print(f"❌ Socket连接失败，错误代码: {result}")
            sock.close()
            return False
            
    except socket.gaierror as e:
        print(f"❌ 主机名解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Socket连接错误: {e}")
        return False

def test_with_timeout():
    """使用更长的超时时间测试"""
    print(f"\n⏰ 使用长超时时间测试...")
    
    config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V',
        'connect_timeout': 60  # 60秒超时
    }
    
    try:
        print(f"🔄 尝试连接（60秒超时）...")
        conn = psycopg2.connect(**config)
        print(f"✅ 长超时连接成功!")
        
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"📊 数据库版本: {version[0][:50]}...")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 长超时连接失败: {e}")
        return False

def check_environment():
    """检查环境"""
    print(f"\n🔍 检查环境...")
    
    import sys
    import platform
    
    print(f"🐍 Python版本: {sys.version}")
    print(f"💻 操作系统: {platform.system()} {platform.release()}")
    print(f"📦 psycopg2版本: {psycopg2.__version__}")
    
    # 检查环境变量
    import os
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"🌐 代理设置 {var}: {value}")

def main():
    """主函数"""
    print("🔧 数据库连接调试工具")
    print("=" * 50)
    
    # 检查环境
    check_environment()
    
    # 测试socket连接
    if not test_socket_connection():
        print(f"\n❌ Socket连接失败，无法继续")
        return
    
    # 测试数据库连接
    working_config = test_direct_connection()
    
    if working_config:
        print(f"\n🎉 找到可用的配置!")
        print(f"📋 推荐配置:")
        for key, value in working_config.items():
            if key != 'password':
                print(f"   {key}: {value}")
            else:
                print(f"   {key}: ***")
    else:
        print(f"\n❌ 所有配置都失败了")
        # 尝试长超时
        test_with_timeout()

if __name__ == "__main__":
    main()
