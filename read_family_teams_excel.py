#!/usr/bin/env python3
"""
读取Family teams_randomization.xlsx文件
"""

import pandas as pd

def read_family_teams_excel():
    """读取家庭组随机分配Excel文件"""
    
    try:
        print("📖 读取Family teams_randomization.xlsx文件...")
        
        # 读取Excel文件
        df = pd.read_excel('Family teams_randomization.xlsx')
        
        print(f"📊 Excel文件信息:")
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        print(f"   列名: {list(df.columns)}")
        
        # 显示前几行数据
        print(f"\n📋 前10行数据:")
        print(df.head(10).to_string())
        
        # 检查是否有分组信息
        if 'Group' in df.columns or 'Category' in df.columns or 'Type' in df.columns:
            group_col = None
            for col in ['Group', 'Category', 'Type', 'Condition', 'Assignment']:
                if col in df.columns:
                    group_col = col
                    break
            
            if group_col:
                print(f"\n📊 按{group_col}分组统计:")
                group_stats = df[group_col].value_counts()
                for group, count in group_stats.items():
                    print(f"   {group}: {count} 个家庭组")
                
                print(f"\n📋 各组家庭组列表:")
                for group in group_stats.index:
                    group_families = df[df[group_col] == group]
                    family_names = group_families.iloc[:, 0].tolist()  # 假设第一列是家庭组名
                    print(f"\n   {group} ({len(family_names)}个):")
                    for i, family in enumerate(family_names, 1):
                        print(f"     {i:2d}. {family}")
        
        # 检查总数
        total_families = len(df)
        print(f"\n📊 总计: {total_families} 个家庭组")
        
        # 检查是否有138个家庭组
        if total_families == 138:
            print(f"✅ 确认：Excel文件包含138个家庭组")
        else:
            print(f"⚠️  注意：Excel文件包含{total_families}个家庭组，不是138个")
        
        # 保存为CSV以便进一步分析
        csv_filename = 'family_teams_randomization.csv'
        df.to_csv(csv_filename, index=False, encoding='utf-8')
        print(f"\n📁 已保存为CSV文件: {csv_filename}")
        
        return df
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        
        # 尝试读取所有工作表
        try:
            print(f"\n🔍 尝试读取所有工作表...")
            excel_file = pd.ExcelFile('Family teams_randomization.xlsx')
            print(f"   工作表列表: {excel_file.sheet_names}")
            
            for sheet_name in excel_file.sheet_names:
                print(f"\n📋 工作表: {sheet_name}")
                try:
                    sheet_df = pd.read_excel('Family teams_randomization.xlsx', sheet_name=sheet_name)
                    print(f"   行数: {len(sheet_df)}, 列数: {len(sheet_df.columns)}")
                    print(f"   列名: {list(sheet_df.columns)}")
                    
                    if len(sheet_df) > 0:
                        print(f"   前3行:")
                        print(sheet_df.head(3).to_string())
                        
                        # 如果这个工作表有138行，可能就是我们要找的
                        if len(sheet_df) == 138:
                            print(f"   ✅ 这个工作表有138行！")
                            return sheet_df
                            
                except Exception as sheet_error:
                    print(f"   ❌ 读取工作表失败: {sheet_error}")
                    
        except Exception as excel_error:
            print(f"❌ 读取Excel文件结构失败: {excel_error}")
        
        return None

if __name__ == "__main__":
    read_family_teams_excel()
