from rest_framework import serializers
from user_authentication.models import UserProfile


class EditUserProfileSerializer(serializers.ModelSerializer):

    class Meta:
        model = UserProfile
        fields = ['age_group' ,'role' ]


class LoginSerializer(serializers.Serializer):
    phone_number  = serializers.IntegerField()
    country_code = serializers.CharField(max_length=4)
    password = serializers.CharField(max_length = 200, min_length = 8)


class SendOtpSerializer(serializers.Serializer):
    phone_number  = serializers.IntegerField()
    country_code = serializers.CharField(max_length=4)
    # type = serializers.CharField(max_length = 100)

class MemberSendOtpSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    phone_number  = serializers.IntegerField()
    country_code = serializers.CharField(max_length=4)

class VerifyOtpSerializer(serializers.Serializer):
    phone_number  = serializers.IntegerField()
    country_code = serializers.Char<PERSON>ield(max_length=4)
    otp = serializers.IntegerField(max_value= 9999)
    # type = serializers.CharField(max_length = 100)

class ChangePasswordSerializer(serializers.Serializer):
    is_skip = serializers.BooleanField()
    phone_number  = serializers.IntegerField()
    country_code = serializers.CharField(max_length=4)
    new_password = serializers.CharField(max_length=200, required = False)
    confirm_password = serializers.CharField(max_length=200, required = False)
    # type = serializers.CharField(max_length = 100)

class UpdateUserProfileDetailSerializer(serializers.Serializer):
    first_name = serializers.CharField(max_length = 200)
    last_name = serializers.CharField(max_length = 200)

class InviteFamilyMemberSerializer(serializers.Serializer):
    leader_id = serializers.IntegerField()
    country_code = serializers.CharField(max_length = 4)
    phone_number = serializers.IntegerField()
    first_name = serializers.CharField(max_length = 200)
    last_name = serializers.CharField(max_length = 200)
    age_group = serializers.CharField(max_length = 200)
    role = serializers.CharField(max_length = 200)
    
class CancelFamilyInvitationSerializer(serializers.Serializer):
    leader_id = serializers.IntegerField()
    country_code = serializers.CharField(max_length = 4)
    phone_number = serializers.IntegerField()

class CheckinSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()

class LogoutSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()

class DeactivateMemberSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()