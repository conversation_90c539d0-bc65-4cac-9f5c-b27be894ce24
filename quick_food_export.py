#!/usr/bin/env python3
"""
快速生成HKU饮食类别导出数据（包含Bonus questions）
"""

import psycopg2
import csv
import json
from datetime import datetime

def quick_food_export():
    """快速生成饮食类别导出（包含Food + Bonus questions）"""

    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }

    try:
        print("🍎 快速生成饮食类别导出数据...")

        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        # 首先检查饮食类别的家庭组分布
        print("🔍 检查饮食类别家庭组分布:")
        food_families_query = """
        SELECT DISTINCT
            fg.family_name,
            COUNT(*) as answer_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND q.category = 'Food'
        GROUP BY fg.family_name
        ORDER BY fg.family_name
        """

        cursor.execute(food_families_query)
        food_families = cursor.fetchall()

        print("   饮食类别家庭组:")
        for family, count in food_families:
            print(f"     {family}: {count} 条")

        # 查询饮食类别数据（包含步数和Bonus questions，包含完整选项）
        query = """
        SELECT
            uq.date as answer_date,
            fg.family_name,
            au.username as member_name,
            q.category as question_category,
            q.quiz_type,
            q.question,
            q.answer as correct_answer,
            uq.selected_answer as given_answer_letter,
            q.option as question_options,
            uq.is_correct,
            CASE WHEN uq.is_correct THEN q.score ELSE 0 END as question_score,
            q.score as max_question_score,
            uq.user_id,
            fg.id as family_group_id,
            COALESCE(sc.steps, 0) as step_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        LEFT JOIN question_usersteps sc ON au.id = sc.user_id AND uq.date = sc.date
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND (q.category = 'Food' OR q.quiz_type = 'Bonus')
        ORDER BY uq.date, fg.family_name, au.username
        """
        
        cursor.execute(query)
        results = cursor.fetchall()

        print(f"📊 找到 {len(results)} 条饮食类别记录（包含Bonus questions）")

        # 生成CSV文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'hku_exercise_export_Food_{timestamp}.csv'

        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            headers = [
                'Answer Date', 'Family Name', 'Member Name', 'Question Category',
                'Quiz Type', 'Question', 'Correct Answer', 'Given Answer',
                'All Answer Options', 'Is Correct', 'Question Score', 'Max Question Score',
                'User ID', 'Family Group ID', 'Step Count',
                'Daily Checkin Score', 'Weekly Family Photo Score',
                'Food Screenshot Score', 'Steps Screenshot Score', 'Quiz Score'
            ]
            writer.writerow(headers)

            # 按用户和日期分组处理数据
            user_date_data = {}
            for row in results:
                user_id = row[12]  # 调整索引
                answer_date = row[0]
                key = (user_id, answer_date)

                if key not in user_date_data:
                    user_date_data[key] = []
                user_date_data[key].append(row)

            print(f"📝 开始写入CSV文件...")

            # 写入数据行
            for (user_id, answer_date), rows in user_date_data.items():
                # 计算当天的Quiz分数
                quiz_score = sum(row[10] for row in rows)  # question_score 调整索引

                # 为每个问题写入一行，但只在第一行显示分数
                for i, row in enumerate(rows):
                    # 处理答案选项
                    given_answer_letter = row[7]  # 用户选择的字母
                    question_options = row[8]     # JSON格式的选项
                    quiz_type = row[4]            # Quiz类型

                    # 对于Bonus questions，解析选项并获取完整答案文本
                    if quiz_type == 'Bonus' and question_options:
                        try:
                            options_dict = json.loads(question_options) if isinstance(question_options, str) else question_options
                            # 获取用户选择的完整答案
                            given_answer_full = options_dict.get(given_answer_letter, given_answer_letter) if given_answer_letter else ''
                            # 格式化所有选项
                            all_options = '; '.join([f"{k}: {v}" for k, v in options_dict.items()])
                        except:
                            given_answer_full = given_answer_letter if given_answer_letter else ''
                            all_options = str(question_options) if question_options else ''
                    else:
                        given_answer_full = given_answer_letter if given_answer_letter else ''
                        all_options = ''

                    # 构建CSV行数据
                    csv_row = [
                        row[0],   # Answer Date
                        row[1],   # Family Name
                        row[2],   # Member Name
                        row[3],   # Question Category
                        row[4],   # Quiz Type
                        row[5],   # Question
                        row[6] if quiz_type != 'Bonus' else '',  # Correct Answer (空白对于Bonus questions)
                        given_answer_full,  # Given Answer (完整文本)
                        all_options,  # All Answer Options (所有选项)
                        row[9],   # Is Correct
                        row[10],  # Question Score
                        row[11],  # Max Question Score
                        row[12],  # User ID
                        row[13],  # Family Group ID
                        row[14],  # Step Count
                    ]

                    # 添加分数字段（只在第一行显示）
                    if i == 0:
                        csv_row.extend([
                            100,  # Daily Checkin Score
                            0,    # Weekly Family Photo Score (不计分)
                            500,  # Food Screenshot Score
                            0,    # Steps Screenshot Score (饮食类别不适用)
                            quiz_score  # Quiz Score
                        ])
                    else:
                        csv_row.extend([0, 0, 0, 0, 0])  # 其他行不重复计分

                    writer.writerow(csv_row)

        print(f"✅ 饮食类别导出完成: {filename}")
        print(f"📊 总记录数: {len(results)}")

        # 显示家庭组分布
        family_counts = {}
        category_counts = {}
        for row in results:
            family = row[1]
            category = row[3] if row[3] else 'Bonus'
            family_counts[family] = family_counts.get(family, 0) + 1
            category_counts[category] = category_counts.get(category, 0) + 1

        print(f"🏠 家庭组分布:")
        for family, count in sorted(family_counts.items()):
            print(f"   {family}: {count} 条")

        print(f"📋 类别分布:")
        for category, count in sorted(category_counts.items()):
            print(f"   {category}: {count} 条")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    quick_food_export()
