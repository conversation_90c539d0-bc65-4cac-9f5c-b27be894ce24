#!/usr/bin/env python3
"""
检查Bonus questions的完整数据结构
"""

import psycopg2

def check_bonus_structure():
    """检查Bonus questions的数据结构"""
    
    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }
    
    try:
        print("🔍 检查Bonus questions的完整数据结构...")
        
        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 首先检查question_question表的字段结构
        print("\n🔍 检查question_question表字段:")
        columns_query = """
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'question_question'
        ORDER BY ordinal_position
        """
        cursor.execute(columns_query)
        columns = cursor.fetchall()
        for col_name, col_type in columns:
            print(f"     {col_name}: {col_type}")

        # 检查question_question表中Bonus questions的完整信息
        print("\n📋 Bonus questions完整信息:")
        bonus_detail_query = """
        SELECT
            q.id,
            q.question,
            q.answer,
            q.option,
            q.score
        FROM question_question q
        WHERE q.quiz_type = 'Bonus'
        AND q.is_deleted = false
        LIMIT 5
        """
        
        cursor.execute(bonus_detail_query)
        bonus_details = cursor.fetchall()

        for question_id, question, answer, option, score in bonus_details:
            print(f"\n   问题ID: {question_id}")
            print(f"   问题: {question}")
            print(f"   正确答案: {answer}")
            print(f"   选项JSON: {option}")

            # 解析选项
            if option:
                try:
                    import json
                    options_dict = json.loads(option) if isinstance(option, str) else option
                    print(f"   解析后的选项:")
                    for key, value in options_dict.items():
                        print(f"     {key}: {value}")
                except Exception as e:
                    print(f"   选项解析错误: {e}")

            print(f"   分数: {score}")
            print(f"   ---")
        
        # 检查用户回答的详细情况
        print(f"\n👥 用户Bonus questions回答详情:")
        user_answers_query = """
        SELECT
            fg.family_name,
            au.username,
            q.question,
            q.option,
            uq.selected_answer,
            q.answer as correct_answer,
            uq.is_correct
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        JOIN auth_user au ON uq.user_id = au.id
        JOIN family_familymembers fm ON au.id = fm.member_id
        JOIN family_familygroups fg ON fm.family_group_id = fg.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND fm.is_deleted = false
        AND fm.is_member = true
        AND q.quiz_type = 'Bonus'
        LIMIT 10
        """

        cursor.execute(user_answers_query)
        user_answers = cursor.fetchall()

        for family, username, question, option, selected, correct, is_correct in user_answers:
            print(f"\n   家庭: {family}")
            print(f"   用户: {username}")
            print(f"   问题: {question[:50]}...")
            print(f"   选项: {option}")
            print(f"   用户选择: {selected}")
            print(f"   正确答案: {correct}")
            print(f"   是否正确: {is_correct}")
            print(f"   ---")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    check_bonus_structure()
