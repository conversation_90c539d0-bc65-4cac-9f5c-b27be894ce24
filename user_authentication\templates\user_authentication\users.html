{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Users' %}{% endblock %}
{% block content %}
{% load static %}

{% if msg %}
<div id="idUsersMsg" class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
    <span class="font-medium">{{ msg }}</span>
    <span onclick="closeMsg('idUsersMsg')">
        <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </span>
</div>
{% endif %}
<div class="p-4">
        <div class="mb-3 justify-between flex">
            <h1 class="text-2xl font-bold mb-4">{% trans 'User List' %}</h1>
            <div class="content-center">
                <button type="button" id="staticModal" data-modal-target="import-users-static-modal" data-modal-toggle="import-users-static-modal" class="text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg" >{% trans 'Import'%}</button>
                <a class="text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg" href="/export-userdata/?field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" >{% trans 'Export'%}</a>
            </div>
        </div>

        <div class="flex flex-col md:flex-row w-full mx-auto mb-3 justify-between items-center space-y-4 md:space-y-0 md:space-x-4">

            <!-- Page length dropdown -->
            <div id="dropdown" class="flex items-center space-x-2 z-10 w-full md:w-auto">
                <span>Show</span>
                <select id="id_pageLength" name="page_length" onchange="changePageLength()" class="shadow-lg w-fit bg-white hover:bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>

            <div class="relative flex flex-wrap items-center w-full md:max-w-md">
                <select id="id_userField" name="user_field" class="w-full shadow-lg bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 md:w-auto mr-px mb-2 md:mb-0 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 rounded-l-lg">
                    <option value="">Select Field</option>
                    <option value="First Name">First Name</option>
                    <option value="Last Name">Last Name</option>
                    <option value="Phone Number">Phone Number</option>
                    <option value="Age Group">Age Group</option>
                </select>
                <input type="search" id="searchKeyword" value="{{ search_keyword }}" name="search_with_field" class="flex-grow bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 mr-px shadow-lg w-full md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500 " placeholder="Select the field then type..." required />
                <button type="button" id="id_clearSearch" onclick="clearSearch()" title="Clear" class="{% if not search_field or not search_keyword %} hidden {% endif %} p-2.5 mb-2 md:mb-0 text-sm font-medium text-gray-600 bg-transparent border-none cursor-pointer focus:outline-none focus:ring-0 md:rounded-none">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 6l8 8M14 6l-8 8" />
                    </svg>
                    <span class="sr-only">Clear</span>
                </button>
                <button type="button" onclick="searchUsers()" class="shadow-lg p-2.5 text-sm font-medium text-white bg-blue-500 rounded-r-lg hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 border border-gray-300">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                    <span class="sr-only">Search</span>
                </button>
            </div>
        </div>

        <div class="relative w-full shadow-lg rounded-lg overflow-x-auto sm:rounded-lg">
            <div class="table-container">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                    <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead" >
                        <tr>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" style="width: max-content;" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'created_on' and request.GET.order == 'asc' %}&sort_by=created_on&order=desc{% else %}&sort_by=created_on&order=asc{% endif %}">
                                    {% trans 'Created On' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'created_on' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'created_on' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'first_name' and request.GET.order == 'asc' %}&sort_by=first_name&order=desc{% else %}&sort_by=first_name&order=asc{% endif %}">
                                    {% trans 'First Name' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'first_name' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'first_name' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'last_name' and request.GET.order == 'asc' %}&sort_by=last_name&order=desc{% else %}&sort_by=last_name&order=asc{% endif %}">
                                    {% trans 'Last Name' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'last_name' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'last_name' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Role' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Country Code' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Phone Number' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Age Group' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Email' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'School' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Grade' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Class Number' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'checkin_score' and request.GET.order == 'asc' %}&sort_by=checkin_score&order=desc{% else %}&sort_by=checkin_score&order=asc{% endif %}">
                                    {% trans 'Checkin Score' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'checkin_score' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'checkin_score' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page=1&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}{% if request.GET.sort_by == 'status' and request.GET.order == 'asc' %}&sort_by=status&order=desc{% else %}&sort_by=status&order=asc{% endif %}">
                                    {% trans 'Status' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'status' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'status' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Action' %}
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if data %}
                            {% for dt in data %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td class="px-6 py-4">
                                    {{ dt.user__date_joined }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.user__first_name }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.user__last_name }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.role }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.country_code }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.phone_number }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.age_group }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.email }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.school }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.grade }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.class_number }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.checkin_steps_score }}
                                </td>
                                <td class="px-6 py-4">
                                    <div {% if dt.user__is_active == 'Active' %} class="w-fit pl-2 pr-2 bg-green-100 text-green-900 rounded-md" {% else %} class="w-fit pl-2 pr-2 bg-red-100 text-red-900 rounded-md" {% endif %}>
                                        {{ dt.user__is_active }}
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    {% if not dt.user__is_superuser %}
                                    <div class="flex">
                                        <a href="/user/{{ dt.user_id }}/" title="Edit">
                                            <svg class="text-blue-500 hover:text-blue-600" width="24"  height="24"  viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round">  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" /></svg>
                                        </a>
                                        <button type="button" class="ml-2" onclick="deleteUser('{{ dt.user_id }}')" title="Delete">
                                            <svg class="text-red-500 hover:text-red-600"  width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">  <path stroke="none" d="M0 0h24v24H0z"/>  <line x1="4" y1="7" x2="20" y2="7" />  <line x1="10" y1="11" x2="10" y2="17" />  <line x1="14" y1="11" x2="14" y2="17" />  <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12" />  <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3" /></svg>
                                        </button>
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="14" class="px-6 py-4 text-base">Data is not available.</td></tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

        <nav class="flex flex-col bg-gray-50 md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4" aria-label="Table navigation">
            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                Showing
                <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
                of
                <span class="font-semibold text-gray-900 dark:text-white">{{ total_items }}</span>
            </span>
            <ul class="inline-flex items-stretch -space-x-px">
                {% if page_obj.has_previous %}
                <li>
                    <a href="?page={{ page_obj.previous_page_number }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <span class="sr-only">Previous</span>
                        <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </li>
                {% endif %}
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li>
                        <a
                        class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            {{ num }}
                        </a>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %} <li>
                        <a href="?page={{ num }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}"
                        class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            {{ num }}
                        </a>
                    </li>
                    {% endif %}
                {% endfor %}
                {% if page_obj.has_next %}
                <li>
                    <a href="?page={{ page_obj.next_page_number }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <span class="sr-only">Next</span>
                        <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>

    <div id="import-users-static-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-baseline w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 id="modalTitle" class="text-xl font-semibold text-gray-900 dark:text-white">Import Users</h3>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="import-users-static-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"></path>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <form id="import-users-form" class="space-y-4" enctype="multipart/form-data" action="/import-usersdata/" method="post">
                    {% csrf_token %}
                    <!-- Modal body -->
                    <div id="modalBody" class="!mt-auto p-4 md:p-5 space-y-4">
                        <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white" for="file_input">Upload file</label>
                        <input accept=".csv" name="import_users_file" class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400" id="id_userFile" type="file">
                    </div>
                    <div id="infoMsg" class="!-mt-2.5 mb-2 mr-6 ml-6">Only <b>.csv</b> file is allowed. <a class="text-blue-500 hover:underline" href="/download-import-users-samplefile/">Click here</a> to download sample file.</div>
                    <div id="idModalMsg" class="mb-4 mt-4 ml-5 mr-5" style="display: none;">
                    </div>
                    <!-- Modal footer -->
                    <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600 justify-end">
                        <button id="modalConfirmBtn" type="submit" class="text-white bg-blue-500 hover:bg-blue-600 border-gray-300 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 shadow-lg" style="">Import</button>
                        <button id="modalProcessingBtn" type="button" class="text-white hidden bg-blue-400 border-gray-300 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 shadow-lg" disabled="" style="display: none;">Importing...</button>
                        <button id="modalCloseBtn" data-modal-hide="import-users-static-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-primary-300 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600 shadow-lg">
                            Close
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<script src="{% static 'user_authentication/js/users.js'%}?v=0.4"></script>
<script>

    $(document).ready(function() { 
        msg = '{{ msg }}';
        if (msg) {
            setTimeout(() => {
                $('#idUsersMsg').hide();
            }, 10000);
        }

        var dropdown = document.getElementById('id_userField');
        var search_field = '{{ search_field }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === search_field) {
                dropdown.options[i].selected = true;
                break;
            }
        }

        var dropdown = document.getElementById('id_pageLength');
        var page_length = '{{ page_length }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === page_length) {
                dropdown.options[i].selected = true;
                break;
            }
        }
    });

    document.getElementById('import-users-form').addEventListener('submit', function(e) {
        e.preventDefault();
        document.getElementById('import-users-static-modal').classList.add('hidden');
        document.getElementById('spinner').classList.remove('hidden');
        setTimeout(() => {
            this.submit();
        }, 2000); // Adding a delay of 2 seconds before submitting the form
    });

</script>
{% endblock content %}