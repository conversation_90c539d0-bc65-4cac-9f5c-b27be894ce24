#!/usr/bin/env python3
"""
从Django设置获取数据库配置
"""

import os
import sys
import django
from django.conf import settings

def get_django_db_config():
    """获取Django数据库配置"""
    
    try:
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hku_student_quiz.settings')
        django.setup()
        
        print("🔍 从Django设置获取数据库配置...")
        
        # 获取数据库配置
        db_config = settings.DATABASES['default']
        
        print(f"📊 数据库配置:")
        print(f"   引擎: {db_config.get('ENGINE')}")
        print(f"   数据库名: {db_config.get('NAME')}")
        print(f"   用户: {db_config.get('USER')}")
        print(f"   主机: {db_config.get('HOST')}")
        print(f"   端口: {db_config.get('PORT')}")
        print(f"   密码: {'***' if db_config.get('PASSWORD') else '未设置'}")
        
        # 测试连接
        if db_config.get('HOST') and db_config.get('NAME'):
            print(f"\n🔄 测试Django数据库连接...")
            
            from django.db import connection
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    print(f"✅ Django数据库连接成功: {result}")
                    
                    # 测试一些基本查询
                    cursor.execute("SELECT COUNT(*) FROM question_userquestion WHERE answered = true")
                    count = cursor.fetchone()[0]
                    print(f"📊 总quiz记录数: {count}")
                    
                    return {
                        'host': db_config.get('HOST'),
                        'port': db_config.get('PORT'),
                        'database': db_config.get('NAME'),
                        'user': db_config.get('USER'),
                        'password': db_config.get('PASSWORD')
                    }
                    
            except Exception as e:
                print(f"❌ Django数据库连接失败: {e}")
                return None
        else:
            print(f"❌ 数据库配置不完整")
            return None
            
    except Exception as e:
        print(f"❌ Django设置加载失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 尝试直接读取环境变量
        print(f"\n🔍 尝试读取环境变量...")
        env_vars = ['DB_HOST', 'DB_NAME', 'DB_USERNAME', 'DB_PASS', 'DB_PORT']
        for var in env_vars:
            value = os.environ.get(var)
            if value:
                if 'PASS' in var:
                    print(f"   {var}: ***")
                else:
                    print(f"   {var}: {value}")
            else:
                print(f"   {var}: 未设置")
        
        return None

def check_environment_files():
    """检查可能的环境配置文件"""
    print(f"\n🔍 检查环境配置文件...")
    
    possible_files = ['.env', '.env.local', '.env.production', 'config.py', 'local_settings.py']
    
    for filename in possible_files:
        if os.path.exists(filename):
            print(f"✅ 找到配置文件: {filename}")
            try:
                with open(filename, 'r') as f:
                    content = f.read()
                    if 'DB_HOST' in content or 'DATABASE' in content:
                        print(f"   包含数据库配置")
                        # 显示相关行（隐藏密码）
                        lines = content.split('\n')
                        for line in lines:
                            if any(keyword in line.upper() for keyword in ['DB_', 'DATABASE', 'HOST', 'USER']):
                                if 'PASS' in line.upper():
                                    print(f"   {line.split('=')[0]}=***")
                                else:
                                    print(f"   {line}")
            except Exception as e:
                print(f"   读取失败: {e}")
        else:
            print(f"❌ 未找到: {filename}")

def main():
    """主函数"""
    print("🔧 Django数据库配置获取工具")
    print("=" * 50)
    
    # 检查环境文件
    check_environment_files()
    
    # 获取Django配置
    config = get_django_db_config()
    
    if config:
        print(f"\n🎉 成功获取数据库配置!")
        print(f"💡 可以使用这个配置进行数据导出")
        
        # 保存配置到文件
        config_content = f"""# 正确的数据库配置
DB_CONFIG = {{
    'host': '{config['host']}',
    'port': {config['port']},
    'database': '{config['database']}',
    'user': '{config['user']}',
    'password': '{config['password']}'
}}
"""
        
        with open('working_db_config.py', 'w') as f:
            f.write(config_content)
        
        print(f"📁 配置已保存到: working_db_config.py")
        
    else:
        print(f"\n❌ 无法获取有效的数据库配置")
        print(f"💡 请检查环境变量或Django设置")

if __name__ == "__main__":
    main()
