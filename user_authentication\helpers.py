from decouple import config
from twilio.rest import Client
import logging, traceback
from rest_framework_simplejwt.tokens import RefreshToken
from django.conf import settings
from base.views import create_from_exceptions
from django.core.mail import send_mail
from decouple import config
import sendgrid
from sendgrid.helpers.mail import Mail, From, To, Bcc, Personalization, Email
from base import utils


logger = logging.getLogger(__name__)

def send_otp_on_phone(request, phone_number, otp):
    try:
        account_sid = settings.ACCOUNT_SID
        auth_token = settings.AUTH_TOKEN
        client = Client(account_sid, auth_token)
        # otp_msg = f"Welcome to Family Link, Please enter given OTP for your verification - {otp}. Do not share this OTP without consent."
        # otp_msg = f"歡迎參與「智」家樂‧家庭健康聯盟，請輸入以下驗證碼 - {otp} 。請勿與他人分享此驗證碼。 Welcome to SMART Healthy Family Alliance Game. Please enter the following OTP for verification - {otp}. Do not share this OTP with others."
        otp_msg = f"「智家樂」家庭健康聯盟驗證碼: {otp}"
        client.messages.create(body=otp_msg, from_=settings.PHONE_NUMBER, to=phone_number)
        logger.info(f"Otp sent succussfully to the {phone_number}")
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        logger.error(f"Error in sending otp {e}")

def generate_tokens(user):
    refresh = RefreshToken.for_user(user)
    return {
        'access_token': str(refresh.access_token),
        'refresh_token': str(refresh)
    }

from django.core.mail import EmailMultiAlternatives
from decouple import config

def send_otp_on_email(email, otp):
    try:
        subject = "「智家樂」家庭聯盟遊戲平台郵箱驗證碼"
        text_message = f"""
        你好，

        你的「智家樂」家庭聯盟遊戲平台驗證碼是

        {otp}

        有效時間是10分鐘。

        謝謝，
        「智家樂」平台上
        """

        html_message = f"""
        <p>你好，</p>
        <p>你的「智家樂」家庭聯盟遊戲平台驗證碼是</p>
        <p style="font-size: 24px; font-weight: bold;">{otp}</p>
        <p>有效時間是10分鐘。</p>
        <p>謝謝，<br>「智家樂」平台上</p>
        """

        from_email = config("DEFAULT_FROM_EMAIL")  
        recipient_list = email

        # email_msg = EmailMultiAlternatives(subject, text_message, from_email, recipient_list)
        # email_msg.attach_alternative(html_message, "text/html")
        # email_msg.send()
        message = Mail(
            from_email=Email(from_email),  # Use Email() to wrap sender
            to_emails=To(recipient_list),  # Use To() for recipients
            subject=subject,
            html_content=html_message
        )

        if message:
            response = utils.send_bulk_emails([message])

        print(f'Email sent to => {email}')
    
    except Exception as e:
        raise {
            "success": False,
            "message": f"Failed to send OTP to {email}. Error: {str(e)}"
        }

        