from django.shortcuts import render, get_object_or_404, redirect
from rest_framework.views import APIView
import traceback, random, json, os
from django.utils import timezone
from datetime import datetime, timedelta
from user_authentication.models import UserProfile, PhoneNumOtp, User, User<PERSON><PERSON>ckin
from user_authentication.serializers import EditUserProfileSerializer
from rest_framework import status
from rest_framework.response import Response
from user_authentication.helpers import *
from base.views import create_from_exceptions
from django.contrib.auth.hashers import make_password
from base import utils
from django.contrib.auth import authenticate, logout, login
from django.http import HttpResponse, FileResponse
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.db.models import Q, Max
import pandas as pd
from family.models import FamilyGroups ,FamilyMembers
from django.db import transaction
from django.template.loader import render_to_string
from django.conf import settings
from base.service import send_mail
from .serializers import (
    LoginSerializer, SendOtpSerializer, VerifyOtpSerializer, ChangePasswordSerializer, UpdateUserProfileDetailSerializer,
    InviteFamilyMemberSerializer, CancelFamilyInvitationSerializer, CheckinSerializer, LogoutSerializer,
    DeactivateMemberSerializer, MemberSendOtpSerializer
)
from rest_framework import viewsets
from rest_framework.decorators import action
from django.core.mail import EmailMultiAlternatives, get_connection
import time
from question.models import UserLeaderboard
from email.header import Header
from family.models import Category
import sendgrid
from sendgrid.helpers.mail import Mail, From, To, Bcc, Personalization, Email


"""----------------------LOGIN--------------------------------"""
class LoginView(viewsets.GenericViewSet):
    action_serializers = {
        'login':LoginSerializer
    }
    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)
    @action(methods=['post'], detail=False)

    def login(self,request):
        '''
        required_fields => 'country_code','phone_number' & 'password'
        '''
        try:
            data = request.data
            country_code = request.data.get('country_code')
            phone_number = request.data.get('phone_number')
            password = request.data.get('password')

            required_fields = {
                'country_code': 'Country Code',
                'phone_number': 'Phone Number',
                'password': 'Password',
            }

            if '@' in phone_number:
                email = phone_number
                phone_number = None

            missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            if phone_number:
                if not utils.validate_phone_number(phone_number):
                    return Response({
                        "success": False,
                        "message": "Invalid phone number. It should be exactly 8 digits."
                    }, status=status.HTTP_400_BAD_REQUEST)

            # 首先尝试通过手机号码查找用户
            user_profile = UserProfile.objects.filter(country_code=country_code, phone_number=phone_number, is_deleted=False).values('user__username', 'is_verify', "is_pwd_change", "role", "user_id").first()

            # 如果找不到用户，尝试通过用户名直接查找
            if not user_profile and phone_number:
                # 尝试查找用户名与手机号匹配的用户
                user = User.objects.filter(username=phone_number).first()
                if user:
                    user_profile = UserProfile.objects.filter(user_id=user.id, is_deleted=False).values('user__username', 'is_verify', "is_pwd_change", "role", "user_id").first()
                    # 如果找到用户但手机号为空，自动更新手机号
                    if user_profile and not UserProfile.objects.get(user_id=user.id).phone_number:
                        UserProfile.objects.filter(user_id=user.id).update(country_code=country_code, phone_number=phone_number)

            if not user_profile:
                 return Response({
                "success":False,
                "message" :"User Not Found."}, status=status.HTTP_404_NOT_FOUND)

            user = authenticate(username=user_profile['user__username'], password=password)

            if user:
                # login(request, user)
                # session = request.session
                # session["user_id"] = user.id
                is_family_create = False
                is_member_added = False
                refresh = RefreshToken.for_user(user)
                if str(user_profile["role"]).lower() == "領導者":
                    family_grp = FamilyGroups.objects.filter(leader_id=user.id).values("id").first()
                    if family_grp:
                        is_family_create = True
                        family_member = FamilyMembers.objects.filter(family_group_id=family_grp["id"]).values("id").first()
                        if family_member:
                            is_member_added = True

                return Response({
                    "success": True,
                    "message": "Login successful.",
                    'access_token': str(refresh.access_token),
                    'refresh_token': str(refresh),
                    "data": {"user_id":user.id,
                             "role": user_profile["role"]},
                    "is_verify": user_profile["is_verify"],
                    "is_pwd_change": user_profile["is_pwd_change"],
                    "is_family_create": is_family_create,
                    "is_member_added": is_member_added,
                    },
                    status=status.HTTP_200_OK)
            else:
                if phone_number:
                    error_msg = "country_code: " + str(country_code) + ", phone_number: " + phone_number + ", username: " + str(user_profile['user__username']) + ", password: " + str(password)
                else:
                    error_msg = "email: " + email + ", username: " + str(user_profile['user__username']) + ", password: " + str(password)
                create_from_exceptions(request.user.id, error_msg, traceback.format_exc())
                return Response({
                    "success" : False,
                    "message" : "Invalid credentials."}, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------SEND OTP--------------------------------"""
class SendOtpView(viewsets.GenericViewSet):
    action_serializers = {
        'send_otp':SendOtpSerializer
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path="send-otp")
    def send_otp(self, request):
        try:
            data = request.data
            country_code = request.data.get('country_code')
            phone_number = request.data.get('phone_number')

            required_fields = {
                'country_code': 'Country Code' ,
                'phone_number': 'Phone Number' ,
            }

            email = None
            if '@' in phone_number:
                email = phone_number
                phone_number = None

            missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            if phone_number:
                if not utils.validate_phone_number(phone_number):    # Validate phone number
                    return Response({"success": False,
                                    'message': 'Invalid phone number. It should be exactly 8 digits.'}, status=status.HTTP_400_BAD_REQUEST)

            if phone_number:
                user = UserProfile.objects.filter(country_code=country_code, phone_number=phone_number, is_deleted=False).values("user_id", "role").first()
            else:
                user = UserProfile.objects.filter(country_code=country_code, email=email, is_deleted=False).values("user_id", "role").first()
            if not user:
                return Response({"success": False,
                                'message': 'User does not exist.'}, status=status.HTTP_400_BAD_REQUEST)

            if str(user["role"]).lower() != "領導者":
                is_member = FamilyMembers.objects.filter(member_id=user["user_id"], is_member=True).first()
                if not is_member:
                    return Response({
                        "success": False,
                        "message": "Can't send OTP because User is not a part of any family group."
                    }, status=status.HTTP_400_BAD_REQUEST)

            otp_expire_time = timezone.now() + timedelta(minutes=3)
            otp = str(random.randint(1000, 9999))
            PhoneNumOtp.objects.update_or_create(
                phone_number=phone_number,
                country_code=country_code,
                email = email,
                defaults={
                    'otp_expiry': otp_expire_time,
                    'otp': otp
                    })

            if phone_number:
                send_otp_on_phone(request, phone_number=str(country_code) + str(phone_number), otp=otp)
                return Response({"success":True,
                                    "message": f'OTP sent to the registered phone number {phone_number}.'}, status=status.HTTP_200_OK)
            else:
                send_otp_on_email(email=email, otp=otp)
                return Response({"success":True,
                                    "message": f'OTP sent to the registered email {email}.'}, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                "success": False,
                'message': 'Something went wrong.',
                "error": str(e),
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MemberSendOtpView(viewsets.GenericViewSet):
    action_serializers = {
        'send_otp': MemberSendOtpSerializer
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path="member-send-otp")
    def member_send_otp(self, request):
        try:
            with transaction.atomic():
                data = request.data
                user_id = request.data.get("user_id")
                country_code = request.data.get('country_code')
                phone_number = request.data.get('phone_number')

                required_fields = {
                    'user_id': 'User ID',
                    'country_code': 'Country Code' ,
                    'phone_number': 'Phone Number' ,
                }

                email = None
                if '@' in phone_number:
                    email = phone_number
                    phone_number = None

                #print(f'phone_number => {phone_number}, email => {email}')

                missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

                if missing_fields:
                    message = utils.get_required_msg(missing_fields)
                    return Response({
                        "success": False,
                        'message': message
                    }, status=status.HTTP_400_BAD_REQUEST)

                if phone_number:
                    if not utils.validate_phone_number(phone_number):    # Validate phone number
                        return Response({"success": False,
                                        'message': 'Invalid phone number. It should be exactly 8 digits.'}, status=status.HTTP_400_BAD_REQUEST)
                if phone_number:
                    user = UserProfile.objects.filter(country_code=country_code, phone_number=phone_number, is_verify=True, is_deleted=False).values("user_id").first()
                else:
                    user = UserProfile.objects.filter(country_code=country_code, email=email, is_verify=True, is_deleted=False).values("user_id").first()
                if user:
                    return Response({"success": False,
                                    'message': 'User is already exist with this phone number, Please enter another phone number.'}, status=status.HTTP_400_BAD_REQUEST)

                # 更新用户资料并确认更新成功
                update_result = UserProfile.objects.filter(user_id=user_id, is_deleted=False).update(country_code=country_code, phone_number=phone_number, email=email)

                # 验证更新是否成功
                if update_result == 0:
                    return Response({
                        "success": False,
                        'message': 'Failed to update user profile. User may not exist.'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # 再次检查更新后的数据是否正确
                updated_profile = UserProfile.objects.filter(user_id=user_id, is_deleted=False).first()
                if updated_profile and phone_number and updated_profile.phone_number != phone_number:
                    # 如果更新后手机号码仍然不匹配，尝试直接设置
                    updated_profile.phone_number = phone_number
                    updated_profile.country_code = country_code
                    updated_profile.email = email
                    updated_profile.save()

                otp_expire_time = timezone.now() + timedelta(minutes=3)
                otp = str(random.randint(1000, 9999))
                PhoneNumOtp.objects.update_or_create(
                    phone_number=phone_number,
                    country_code=country_code,
                    email = email,
                    defaults={
                        'otp_expiry': otp_expire_time,
                        'otp': otp
                        })

                if phone_number:
                    send_otp_on_phone(request, phone_number=str(country_code) + str(phone_number), otp=otp)
                    return Response({"success":True,
                                        "message": f'OTP sent to the registered phone number {phone_number}.'}, status=status.HTTP_200_OK)
                else:
                    send_otp_on_email(email=email, otp=otp)
                    return Response({"success":True,
                                        "message": f'OTP sent to the registered email {email}.'}, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                "success": False,
                'message': 'Something went wrong.',
                "error": str(e),
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------VERIFY OTP-------------------------------"""
class VerifyOTP(viewsets.GenericViewSet):

    action_serializers = {
        'verify_otp':VerifyOtpSerializer
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path='verify-otp')
    def verify_otp(self,request):
        try:
            data = request.data
            country_code = request.data.get('country_code')
            phone_number = request.data.get('phone_number')
            otp = request.data.get('otp')

            required_fields = {
                'country_code': 'Country Code',
                'phone_number': 'Phone Number',
                'otp': 'OTP',
            }
            email=""
            if '@' in phone_number:
                email = phone_number
                phone_number = None
            print(f'phone_number =>** {phone_number}, email =>** {email}')

            missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

            if phone_number:
                if missing_fields:
                    message = utils.get_required_msg(missing_fields)
                    return Response({"success": False, 'message': message}, status=status.HTTP_400_BAD_REQUEST)


            if phone_number:
                """Validate phone number"""
                if not utils.validate_phone_number(phone_number):
                    return Response({"success": False, "message": 'Invalid phone number. It should be exactly 8 digits.'}, status=status.HTTP_400_BAD_REQUEST)

            if phone_number:
                user_profile = UserProfile.objects.filter(country_code=country_code, phone_number=phone_number, is_deleted=False).first()
            else:
                user_profile = UserProfile.objects.filter(email=email, is_deleted=False).first()
            if not user_profile:
                return Response({"success": False, "message": 'User not found.'}, status=status.HTTP_404_NOT_FOUND)

            if phone_number:
                """Check if the OTP is valid"""
                phone_otp = PhoneNumOtp.objects.filter(country_code=country_code, phone_number=phone_number, otp=otp).first()
            else:
                phone_otp = PhoneNumOtp.objects.filter(country_code=country_code, email=email, otp=otp).first()

            print(phone_otp)
            current_time = timezone.now()
            if not phone_otp or phone_otp.otp_expiry < current_time:
                return Response({"success": False,"message": 'Invalid OTP or phone number.'}, status=status.HTTP_400_BAD_REQUEST)

            phone_otp.is_verify = True
            phone_otp.save()

            user_profile.is_verify = True
            user_profile.save()

            """OTP is valid"""
            return Response({"success": True,"message": 'OTP verified successfully.'}, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------CHANGE-PW--------------------------------"""
class ChangePwView(viewsets.GenericViewSet):
    permission_classes = [AllowAny]
    action_serializers = {
        'change_password':ChangePasswordSerializer
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path='change-password')
    def change_password(self,request):
        '''
        Is skip => True, then no need for the "new_password" and "confirm_password"
        '''
        try:
            data = request.data
            country_code = request.data.get('country_code')
            phone_number = request.data.get('phone_number')
            new_password = request.data.get('new_password')
            confirm_password = request.data.get('confirm_password')
            is_skip = request.data.get("is_skip")

            if '@' in phone_number:
                email = phone_number
                phone_number = None

            if is_skip is None:
                return Response({
                    "success": False,
                    'message': "Is Skip field is required."
                }, status=status.HTTP_400_BAD_REQUEST)

            is_skip = str(is_skip).lower()
            if is_skip == "false":
                required_fields = {
                    'country_code': 'Country Code',
                    'phone_number': 'Phone Number',
                    'new_password': 'New Password',
                    'confirm_password': 'Confirm Password',
                    'is_skip': 'Is Skip',
                }
            else:
                required_fields = {
                    'country_code': 'Country Code',
                    'phone_number': 'Phone Number',
                    'is_skip': 'Is Skip',
                }

            missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            if phone_number:
                if not utils.validate_phone_number(phone_number):
                    return Response({"success": False,
                                    'message': 'Invalid phone number. It should be exactly 8 digits.'}, status=status.HTTP_400_BAD_REQUEST)

            if is_skip == "false":
                if not utils.validate_password(new_password):
                    return Response({"success": False,
                                    'message': 'The password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one digit, and one special character (@$!%*?&).'}, status=status.HTTP_400_BAD_REQUEST)

                if new_password != confirm_password:
                    return Response({
                        "success" :False,
                        "message" : 'Password and Confirm Password does not match.'}, status=status.HTTP_400_BAD_REQUEST)

            if phone_number:
                user_profile = UserProfile.objects.filter(phone_number=phone_number, country_code=country_code, is_deleted=False).first()
            else:
                user_profile = UserProfile.objects.filter(email=email, is_deleted=False).first()

            if not user_profile:
                return Response({
                    'success':False,
                    'message' :'User not Found.'},status=status.HTTP_404_NOT_FOUND)

            if is_skip == "false":
                user = user_profile.user
                user.password = make_password(new_password)
                user.save()

            user_profile.is_pwd_change = True
            user_profile.save()

            """Delete the OTP after successful password reset"""
            # phone_otp.delete()

            if is_skip == "false":
                return Response({'success': True, 'message': 'Password reset successful.'}, status=status.HTTP_200_OK)
            return Response({'success': True, 'message': 'Password reset is skipped.'}, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------LOGOUT--------------------------------"""
class LogoutView(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]

    action_serializers = {
        'logout':LogoutSerializer
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path='logout')
    def logout(self, request):
        try:
            user_id = request.data.get('user_id')

            if not user_id:
                return Response({
                    'success': False,
                    'message': 'User ID is required.'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not str(user_id).isdigit() and utils.check_int_value(user_id) is False:
                return Response({
                    'success': False,
                    'message': 'User ID must be a number.'}, status=status.HTTP_400_BAD_REQUEST)

            user = User.objects.filter(id=int(user_id)).first()

            if not user:
                return Response({
                    'success': False,
                    'message': 'User not found.'
                }, status=status.HTTP_404_NOT_FOUND)

            logout(request)
            return Response({
                'success': True,
                'message': 'User logged out successfully.'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------USER PROFILE--------------------------------"""
class UserProfileDetail(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = UserProfile.objects.none()
    http_method_names = ['get', 'put', 'patch']
    serializer_class = UpdateUserProfileDetailSerializer

    action_serializers = {
        'partial_update': UpdateUserProfileDetailSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    def retrieve(self, request, pk):
        try:
            if pk is None:
                return Response({
                    "success": False,
                    "message": "User ID is required."}, status=status.HTTP_400_BAD_REQUEST)

            user_profile = UserProfile.objects.filter(user_id=pk, is_deleted=False).values("user__first_name", "user__last_name", "country_code", "phone_number", "age_group", "role").first()
            if not user_profile:
                return Response({"success": False, "message": "User does not exist."}, status=status.HTTP_400_BAD_REQUEST)

            family_grp_data = FamilyMembers.objects.filter(member_id=pk).values("family_group_id", "family_group__family_name", "family_group__category__name", "family_group__leader_id_id", "family_group__leader_id__first_name", "family_group__leader_id__last_name").first()
            country_code = ""
            phone_number = ""
            if family_grp_data:
                leader_data = UserProfile.objects.filter(user_id=family_grp_data["family_group__leader_id_id"], is_deleted=False).values("country_code", "phone_number").first()
                country_code = leader_data["country_code"] if leader_data and leader_data["country_code"] else ""
                phone_number = leader_data["phone_number"] if leader_data and leader_data["phone_number"] else ""

            data = {
                    "family": {
                        "id": family_grp_data["family_group_id"] if family_grp_data else "",
                        "name": family_grp_data["family_group__family_name"] if family_grp_data else "",
                        "category": family_grp_data["family_group__category__name"] if family_grp_data else "",
                        "invited_by": utils.get_user_full_name(family_grp_data["family_group__leader_id__first_name"], family_grp_data["family_group__leader_id__last_name"]) if family_grp_data else "",
                        "country_code": country_code,
                        "phone_number": phone_number,
                    },
                    "user_id": int(pk),
                    "first_name": user_profile['user__first_name'] if user_profile['user__first_name'] else "",
                    "last_name": user_profile['user__last_name'] if user_profile['user__last_name'] else "",
                    "country_code": user_profile['country_code'] if user_profile['country_code'] else "",
                    "phone_number": user_profile['phone_number'] if user_profile['phone_number'] else "",
                    "age_group": user_profile['age_group'] if user_profile['age_group'] else "",
                    "role": user_profile['role'] if user_profile['role'] else "",
                    }

            return Response({
                "success": True,
                "message": "User-data fetched successfully.",
                "data": data,
            }, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id if request.user else None, e, traceback.format_exc())
            return Response({
                "success": False,
                "message": "Something went wrong."
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def partial_update(self, request, pk):
        try:
            if pk is None:
                return Response({
                    "success": False,
                    "message": "User ID is required."}, status=status.HTTP_400_BAD_REQUEST)

            required_fields = {
                'first_name': 'First Name',
                'last_name': 'Last Name',
                'age_group': 'Age Group',
                'role': 'Role',
            }

            missing_fields = [value for key,value in required_fields.items() if not request.data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            user_profile = get_object_or_404(UserProfile, user_id=pk)
            user_profile = UserProfile.objects.filter(user_id=pk, is_deleted=False).first()
            user = user_profile.user

            """update the User fields"""
            user.first_name = request.data.get('first_name', user.first_name)
            user.last_name = request.data.get('last_name', user.last_name)
            user.save()

            serializer = EditUserProfileSerializer(user_profile, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response({
                    "success": True,
                    "message": "Your profile updated successfully."
                }, status=status.HTTP_200_OK)

            return Response({
                "success": False,
                "message": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            create_from_exceptions(request.user.id if request.user else None, e, traceback.format_exc())
            return Response({
                "success": False,
                "message": "Something went wrong."
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------INVITE FAMILY MEMBER------------------"""
class InviteFamilyMember(viewsets.GenericViewSet):

    permission_classes = [IsAuthenticated]

    action_serializers = {
        "invite_family_member":InviteFamilyMemberSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path='invite-family-member')
    def invite_family_member(self, request):
        try:
            data = request.data
            leader_id = request.data.get('leader_id')
            country_code = request.data.get('country_code')
            phone_number = request.data.get('phone_number')
            first_name = request.data.get('first_name')
            last_name = request.data.get('last_name')
            age_group = request.data.get('age_group')
            role = request.data.get('role')
            if '@' in phone_number:
                email = phone_number
                phone_number = None

            #print(f'emain = {email}, phone_number => {phone_number}')
            required_fields = {
                'leader_id' : 'Leader ID',
                'country_code': 'Country Code',
                'phone_number': 'Phone Number',
                'first_name': 'First Name',
                'last_name': 'Last Name',
                'age_group': 'Age Group',
                'role': 'Role',
            }

            missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            leader = User.objects.filter(id=leader_id).values("first_name", "last_name").first()

            """check whether leader exist or not"""
            if not leader:
                return Response({"success": False, "message": "Leader ID does not exist."}, status=status.HTTP_400_BAD_REQUEST)

            """Generate a random password"""
            if email:
                username = email
            username = phone_number
            password = utils.generate_temp_password(phone_number)

            """Create User instance"""
            family_data = FamilyGroups.objects.filter(leader_id_id=leader_id).values("id", "family_name").first()
            if not family_data:
                return Response({"success": False, "message": "Leader have not created any family group."}, status=status.HTTP_400_BAD_REQUEST)

            join_members_count = FamilyMembers.objects.filter(family_group_id=family_data["id"], is_member=True, is_leader=False).count()
            if join_members_count > 3:
                return Response({"success": False, "message": "Family Group already has 3 members."}, status=status.HTTP_400_BAD_REQUEST)
            if phone_number:
                user_id = UserProfile.objects.filter(country_code=country_code, phone_number=phone_number, is_deleted=False).values("user_id").first()
            else:
                user_id = UserProfile.objects.filter(country_code=country_code, email=email, is_deleted=False).values("user_id").first()
            if user_id:
                family_member = FamilyMembers.objects.filter(member_id=user_id["user_id"], is_member=True).values("family_group_id").first()
                if family_member:
                    msg = "Invited member is already in another group."
                    if family_member["family_group_id"] == family_data["id"]:
                        msg = "Invited member is already in this group."
                    return Response({"success": False, "message": msg}, status=status.HTTP_400_BAD_REQUEST)

            last_user_id = User.objects.values("id").order_by("-id").first()
            seq = int(last_user_id["id"]) + 1 if last_user_id else 1
            fullname = utils.get_user_full_name(first_name, last_name).replace(" ", "") + "_" + str(seq)
            user_name = utils.get_unique_username(fullname)
            with transaction.atomic():
                user_obj = User.objects.create(
                    username=user_name,
                    first_name=first_name,
                    last_name=last_name,
                    email = email,
                    password=make_password(password))

                if user_obj:
                    UserProfile.objects.filter(user_id=user_obj.id, is_deleted=False).update(country_code=country_code, phone_number=phone_number, age_group=age_group, role=role, email = email)

                """Create FamilyMember instance"""
                FamilyMembers.objects.create(
                    family_group_id=family_data["id"],
                    member_id=user_obj.id,
                )

                return Response({
                    "success": True,
                    "message": "Member created successfully.",
                    "data": {
                        "invited_by": utils.get_user_full_name(leader["first_name"], leader["last_name"]),
                        "family_name" : family_data['family_name'],
                        # "username": phone_number,
                        # "password": password,
                        "link": f"{settings.LIVE_FRONTEND_URL}/join?member={str(user_obj.id)}"
                    }
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------------CANCEL INVITATION---------------------------"""
class CancelInvitation(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]

    action_serializers = {
        "cancel_family_member":CancelFamilyInvitationSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path='cancel-family-member')
    def cancel_family_member(self,request):
        try:
            data = request.data
            leader_id = data.get('leader_id')
            member_id = data.get("user_id")
            # country_code = request.data.get('country_code')
            # phone_number = request.data.get('phone_number')

            required_fields = {
                'leader_id' : 'Leader ID',
                'user_id' : 'User ID',
                # 'country_code': 'Country Code',
                # 'phone_number': 'Phone Number',
            }

            missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            # if not str(leader_id).isdigit() and utils.check_int_value(leader_id) is False or not str(member_id).isdigit() and utils.check_int_value(member_id) is False:
            #     return Response({
            #         'success': False,
            #         'message': 'Leader ID, User ID must be a number.'}, status=status.HTTP_400_BAD_REQUEST)

            # if not utils.validate_phone_number(phone_number):          # Validate phone number
            #     return Response({"success": False,
            #                 'message': 'Invalid phone number. It should be exactly 8 digits.'}, status=status.HTTP_400_BAD_REQUEST)

            # if not utils.validate_country_code(country_code):
            #     return Response({"success" : False , "message" : "Country Code is Invalid."},status=status.HTTP_400_BAD_REQUEST)

            exists_users = list(UserProfile.objects.filter(user_id__in=[leader_id, member_id], is_deleted=False).values_list("user_id", flat=True))

            """Check if the leader exists"""
            if int(leader_id) not in exists_users:
                return Response({"success": False, "message": "Leader ID does not exist."}, status=status.HTTP_400_BAD_REQUEST)

            """check invited user exist or not"""
            if int(member_id) not in exists_users:
                return Response({"success": False, "message": "Invited user does not exist."}, status=status.HTTP_400_BAD_REQUEST)

            """Check if the family group exists and quiz hasn't started"""
            family_group = FamilyGroups.objects.filter(leader_id=leader_id).values("id", "is_quiz_starts").first()

            if not family_group:
                return Response({"success": False, "message": "Family group does not exist."}, status=status.HTTP_400_BAD_REQUEST)
            if family_group["is_quiz_starts"]:
                return Response({"success": False, "message": "Quiz has already started, can't cancel family group member."}, status=status.HTTP_400_BAD_REQUEST)

            family_member = FamilyMembers.objects.filter(family_group__leader_id=leader_id, member_id=member_id).first()
            if not family_member:
                return Response({"success": False, "message": "Member is not exist in the family group."}, status=status.HTTP_404_NOT_FOUND)

            """Set is_member to False"""
            family_member.is_member = False
            family_member.is_deleted = True
            family_member.save()

            return Response({
                "success": True,
                "message": "Invitation cancelled successfully."
            }, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""-----------------------RE-INVITE FAMILY MEMBER-----------------------"""
class ReInviteMember(APIView):
    permission_classes = [IsAuthenticated]
    def post(self,request):
        try:
            data = request.data
            leader_id = request.data.get('leader_id')
            member_id = data.get("user_id")
            # country_code = request.data.get('country_code')
            # phone_number = request.data.get('phone_number')

            required_fields = {
                    'leader_id' : 'Leader ID',
                    'user_id' : 'User ID',
                    # 'country_code': 'Country Code',
                    # 'phone_number': 'Phone Number',
                }

            missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

            if missing_fields:
                message = utils.get_required_msg(missing_fields)
                return Response({
                    "success": False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            # if not utils.validate_phone_number(phone_number):  # Validate phone number
            #     return Response({"success": False, 'message': 'Invalid phone number. It should be exactly 8 digits.'}, status=status.HTTP_400_BAD_REQUEST)

            leader = User.objects.filter(id=leader_id).values("id", "first_name", "last_name").first()

            """Check if the leader exists"""
            if not leader:
                return Response({"success": False, "message": "Leader ID does not exist."}, status=status.HTTP_400_BAD_REQUEST)

            """Check if the invited member exists with the provided country code"""
            if not UserProfile.objects.filter(user_id=member_id, is_deleted=False).exists():
                return Response({"success": False, "message": "User does not exist."}, status=status.HTTP_400_BAD_REQUEST)

            """Check if the family group exists and quiz hasn't started"""
            family_group = FamilyGroups.objects.filter(leader_id=leader_id).values("id", "is_quiz_starts").first()

            if not family_group:
                return Response({"success": False, "message": "Family group does not exist."}, status=status.HTTP_400_BAD_REQUEST)
            if family_group["is_quiz_starts"]:
                return Response({"success": False, "message": "Quiz has already started, can't send re-invitation."}, status=status.HTTP_400_BAD_REQUEST)

            """Check if the invited member is part of the leader's family group"""
            family_member = FamilyMembers.objects.filter(family_group_id=family_group["id"], member_id=member_id).values("id").first()
            if not family_member:
                return Response({"success": False, "message": "Invited User is not in Family-Group."}, status=status.HTTP_400_BAD_REQUEST)

            # """Generate a new temporary password"""
            # new_password = utils.generate_temp_password()

            # """Update the user's password"""
            # invited_member.user.set_password(new_password)
            # invited_member.user.save()

            """Set is_member to True"""
            # family_member.is_member = True
            # family_member.save()

            return Response({
                "success": True,
                "message": "Member re-invited successfully.",
                "data": {
                    "invited_by": utils.get_user_full_name(leader["first_name"], leader["last_name"]),
                    # "username": phone_number,
                    # "new_password": new_password,
                    "link": f"{settings.LIVE_FRONTEND_URL}/join?member={str(member_id)}"
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({
                'success': False,
                'message': 'Something went wrong.',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# """----------------------REGISTRATION--------------------------------"""
# class RegistrationView(APIView):
#     def post(self,request):
#         try:
#             data = request.data
#             country_code = request.data.get('country_code')
#             phone_number = data.get('phone_number')
#             otp = data.get('otp')
#             first_name = data.get('first_name')
#             last_name = data.get('last_name')
#             age_group = data.get('age_group')
#             password = data.get('password')
#             confirm_password = data.get('confirm_password')

#             required_fields = {
#                             'country_code': 'Country Code',
#                             'phone_number': 'Phone Number',
#                             'otp': 'OTP',
#                             'first_name': 'First Name',
#                             'last_name': 'Last Name',
#                             'age_group': 'Age Group',
#                             'password': 'Password',
#                             'confirm_password' : 'Confirm Password'}
#             missing_fields = [value for key,value in required_fields.items() if not data.get(key)]

#             if missing_fields:
#                 return Response({
#                     "success": False,
#                     'message': f'{", ".join(missing_fields)} is required.'
#                 }, status=status.HTTP_400_BAD_REQUEST)

#             if not utils.validate_phone_number(phone_number):    # Validate phone number
#                 return Response({"success": False,
#                                 'message': 'Invalid phone number. It should be exactly 8 digits.'}, status=status.HTTP_400_BAD_REQUEST)

#             if not utils.validate_password(password):         # Validate password
#                 return Response({"success": False,
#                                 'message': 'Password should be at least 8 characters long, contain at least one uppercase letter, and one special character (@$!%*?&).'},
#                                 status=status.HTTP_400_BAD_REQUEST)

#             if password != confirm_password:
#                 return Response({"success": False, 'message': 'Password and Confirm Password does not match.'}, status=status.HTTP_400_BAD_REQUEST)

#             if not utils.validate_age_group_format(age_group):  # Custom validation function for age group
#                 return Response({
#                     "success": False,
#                     'message': 'Invalid format for age group. Please provide a valid format.'}, status=status.HTTP_400_BAD_REQUEST)

#             if not PhoneNumOtp.objects.filter(phone_number=phone_number, otp=otp).exists():
#                 return Response({"success": False, 'message': 'Invalid OTP.'}, status=status.HTTP_400_BAD_REQUEST)

#             phone_otp = PhoneNumOtp.objects.get(phone_number=phone_number, otp=otp)

#             if UserProfile.objects.filter(phone_number=phone_number, is_deleted=False).exists():
#                 return Response({"success": False, 'message': 'Phone number already registered.'}, status=status.HTTP_400_BAD_REQUEST)

#             full_name = utils.get_user_full_name(first_name, last_name)

#             user = User.objects.create(username=full_name, first_name=first_name, last_name=last_name, password=make_password(password))

#             if user:
#                 UserProfile.objects.filter(user_id=user.id, is_deleted=False).update(phone_number=phone_number, age_group=age_group)

#             """Delete the OTP after successful registration"""
#             phone_otp.delete()

#             tokens = generate_tokens(user)

#             return Response({
#                 "success": True,
#                 'message': 'User registered successfully.',
#                 'access_token': tokens['access_token'],
#                 'refresh_token': tokens['refresh_token'],
#                 "data" : {"user_id":user.id}},status=status.HTTP_201_CREATED)

#         except Exception as e:
#             create_from_exceptions(request.user.id, e, traceback.format_exc())
#             return Response({
#                 "success": False,
#                 'message': 'Something went wrong.',
#                 }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""----------------------USER CHECK-IN--------------------------------"""
class UserCheck_in(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]

    action_serializers = {
        "checkin":CheckinSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path='checkin')
    def checkin(self, request):
        try:
            with transaction.atomic():
                user_id = request.data.get("user_id")
                if not user_id:
                    return Response({'success': False, 'message': 'User ID is required.'}, status=status.HTTP_400_BAD_REQUEST)

                if not str(user_id).isdigit():
                    return Response({'success': False, 'message': 'User ID must be a number.'}, status=status.HTTP_400_BAD_REQUEST)

                user_id = int(user_id)
                user = User.objects.filter(id=user_id).first()
                if not user:
                    return Response({'success': False, 'message': 'User not registered.'}, status=status.HTTP_401_UNAUTHORIZED)

                user_checkin = UserCheckin.objects.filter(user_id=user_id, created_on__icontains=datetime.now().date()).values("id").first()
                if user_checkin:
                    return Response({'success': False, 'message': 'User already checked in today.'}, status=status.HTTP_400_BAD_REQUEST)

                UserCheckin.objects.create(user_id=user_id)

                # start_date = ""
                # end_date = ""
                # today = datetime.today()
                # # Check if today is Sunday (6 = Sunday)
                # if today.weekday() == int(config("WEEK_DAY")):
                #     # Calculate the previous Monday
                #     start_date = (today - timedelta(days=6)).strftime("%Y-%m-%d")
                #     end_date = (today.date() + timedelta(days=1)).strftime("%Y-%m-%d")

                #     if UserCheckin.objects.filter(created_on__range=(start_date, end_date)).count() == 7:
                user = UserProfile.objects.filter(user_id=user_id, is_deleted=False).first()
                if user:
                    user.checkin_steps_score += int(settings.USER_DAILY_CHECKIN_SCORE)
                    user.save()
                    user_leaderboard = UserLeaderboard.objects.create(user_id=user.user.id, score=int(settings.USER_DAILY_CHECKIN_SCORE))
                    leaderboard = utils.get_leaderboard(user.user.id)
                    leaderboard.user_leaderboard.add(user_leaderboard)
                    leaderboard.score = leaderboard.update_score()
                    leaderboard.save()
                return Response({'success': True, 'message': 'User Check-in Successful.'}, status=status.HTTP_201_CREATED)

        except Exception as e:
            create_from_exceptions(request.user.id, e, traceback.format_exc())
            return Response({"success": False, 'message': 'Something went wrong.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


"""-------------------------------------------------Admin panel functions-------------------------------------------------"""
def signin(request):
    try:
        if request.method == "POST":
            username = request.POST.get("username")
            password = request.POST.get("password")
            if username and password:
                user = authenticate(username=username, password=password)
                if user:
                    login(request, user)
                    session = request.session
                    session["backend_user_id"] = user.id

                    return redirect("index")

                return render(request, "user_authentication/signin.html", {"msg": "Invalid credentials."})
            return render(request, "user_authentication/signin.html", {"msg": "Please enter valid Username and Password."})
        return render(request, "user_authentication/signin.html")
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "user_authentication/signin.html", {"msg": "Something went wrong."})


def signout(request):
    try:
        if request.method == "POST":
            id = request.POST.get("user_id")
            user = User.objects.get(id=id) if id else None
            if user:
                logout(request)

                return HttpResponse(json.dumps({"code": 1, "msg": "Success!"}), content_type="json")
            return HttpResponse(json.dumps({"code": 0, "msg": "User not found."}), content_type="json")
        return HttpResponse(json.dumps({"code": 0, "msg": "Invalid method."}), content_type="json")
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")


def get_users(request):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        search_field = request.GET.get("field")
        search_keyword = request.GET.get("search")
        sort_by = request.GET.get("sort_by")
        sort_order = request.GET.get("order")
        page_length = request.GET.get("length")

        query = Q()
        if search_field and search_keyword:
            if search_field == "First Name":
                query.add(Q(user__first_name__icontains=search_keyword), query.connector)
            elif search_field == "Last Name":
                query.add(Q(user__last_name__icontains=search_keyword), query.connector)
            elif search_field == "Phone Number":
                query.add(Q(phone_number__icontains=search_keyword), query.connector)
            elif search_field == "Age Group":
                query.add(Q(age_group__icontains=search_keyword), query.connector)

        query.add(Q(is_deleted=False) & Q(user__is_superuser=False), query.connector)

        sort = "-user__date_joined"
        if sort_by == "last_name":
            sort = "user__last_name" if sort_order == "asc" else "-user__last_name"
        elif sort_by == "first_name":
            sort = "user__first_name" if sort_order == "asc" else "-user__first_name"
        elif sort_by == "created_on":
            sort = "user__date_joined" if sort_order == "asc" else "-user__date_joined"
        elif sort_by == "checkin_score":
            sort = "checkin_steps_score" if sort_order == "asc" else "-checkin_steps_score"
        elif sort_by == "status":
            sort = "-user__is_active" if sort_order == "asc" else "user__is_active"

        total_records = UserProfile.objects.filter(query).values_list("id").order_by(sort)
        page_number = request.GET.get('page')
        page_obj, total_items, start, end = utils.get_pagination(request, total_records, page_number, page_length)

        users = UserProfile.objects.filter(query).values(
            "user_id",
            "user__first_name",
            "user__last_name",
            "age_group",
            "phone_number",
            "user__date_joined",
            "user__last_login",
            "user__is_active",
            "country_code",
            "user__is_superuser",
            "checkin_steps_score",
            "role",
            "school",
            "grade",
            "user__email",
            "class_number",
            ).order_by(sort)[start:end]

        response = {"data": [],
                    "page_obj": page_obj,
                    'total_items': total_items,
                    "logged_in_user_data": logged_in_user_data,
                    "search_field": search_field if search_field else "",
                    "search_keyword": search_keyword if search_keyword else "",
                    "sort_by": sort_by if sort_by else "",
                    "sort_order": sort_order if sort_order else "",
                    "page_length": page_length if page_length else 10,
                    "page_number": page_number if page_number else 1,
                    }

        for user in users:
            response["data"].append({
                "user_id": user["user_id"],
                "user__is_superuser": user["user__is_superuser"],
                "user__first_name": user["user__first_name"] if user["user__first_name"] else "",
                "user__last_name": user["user__last_name"] if user["user__last_name"] else "",
                "age_group": user["age_group"] if user["age_group"] else "",
                "country_code": user["country_code"] if user["country_code"] else "",
                "phone_number": user["phone_number"] if user["phone_number"] else "",
                "checkin_steps_score": user["checkin_steps_score"] if user["checkin_steps_score"] else 0,
                "user__is_active": "Active" if user["user__is_active"] else "Inactive",
                "user__date_joined": utils.get_date_from_datetime(user["user__date_joined"]),
                "email": user["user__email"] if user["user__email"] else "",
                "role": user["role"] if user["role"] else "",
                "school": user["school"] if user["school"] else "",
                "grade": user["grade"] if user["grade"] else "",
                "class_number": user["class_number"] if user["class_number"] else "",
            })

        return render(request, "user_authentication/users.html", response)
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "user_authentication/users.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})


def get_upd_user_profile(request, id):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        if request.method == "POST":
            first_name = request.POST.get("first_name")
            last_name = request.POST.get("last_name")
            age_grp = request.POST.get("age_grp")
            status = request.POST.get("status")
            role = request.POST.get("role")
            user = UserProfile.objects.filter(user_id=id, is_deleted=False).first() if id else None
            if user:
                if not first_name or not last_name or not age_grp:
                    user_data = {
                        "first_name": user.user.first_name if user.user.first_name else "",
                        "last_name": user.user.last_name if user.user.last_name else "",
                        "age_group": user.age_group if user.age_group else "",
                        "phone_number": user.phone_number if user.phone_number else "",
                        "is_active": "active" if user.user.is_active else "inactive",
                        "role": user.role if user.role else "",
                        "user_id": user.user.id,
                        "country_code": user.country_code if user.country_code else "",
                        "checkin_steps_score": user.checkin_steps_score if user.checkin_steps_score else 0,
                        "email": user.user.email if user.user.email else "",
                        "school": user.school if user.school else "",
                        "grade": user.grade if user.grade else "",
                    }
                    return render(request, "user_authentication/user.html", {"user_data": user_data, "logged_in_user_data": logged_in_user_data, "msg": "Please fill out all the mandatory fields."})

                user.user.first_name = first_name
                user.user.last_name = last_name
                user.user.is_active = True if status == "active" else False
                user.age_group = age_grp
                if user.role != "領導者":
                    user.role = role
                user.user.save()
                user.save()

                return redirect("users")
        else:
            user = UserProfile.objects.filter(user_id=id, is_deleted=False).values(
                "user__first_name",
                "user__last_name",
                "age_group",
                "phone_number",
                "user__is_active",
                "role",
                "user_id",
                "country_code",
                "checkin_steps_score",
                "school",
                "grade",
                "user__email",
                "class_number"
                ).first()

            age_grp_li = list(UserProfile.objects.filter(role="領導者").values_list("age_group", flat=True))
            age_grp_li = list(set(["11", "12", "13", "14", "15"] + age_grp_li))
            age_grp_li.sort()

            user_data = {
                "first_name": user["user__first_name"] if user["user__first_name"] else "",
                "last_name": user["user__last_name"] if user["user__last_name"] else "",
                "age_group": user["age_group"] if user["age_group"] else "",
                "phone_number": user["phone_number"] if user["phone_number"] else "",
                "is_active": "active" if user["user__is_active"] else "inactive",
                "role": user["role"] if user["role"] else "",
                "user_id": user["user_id"],
                "country_code": user["country_code"] if user["country_code"] else "",
                "checkin_steps_score": user["checkin_steps_score"] if user["checkin_steps_score"] else 0,
                "email": user["user__email"] if user["user__email"] else "",
                "school": user["school"] if user["school"] else "",
                "grade": user["grade"] if user["grade"] else "",
                "class_number": user["class_number"] if user["class_number"] else "",
            }

            return render(request, "user_authentication/user.html", {"user_data": user_data, "age_grp_li": age_grp_li, "logged_in_user_data": logged_in_user_data})
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "user_authentication/user.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})


def delete_user(request):
    try:
        if request.method == "POST":
            user_id = request.POST.get("user_id")
            user = UserProfile.objects.filter(user_id=user_id, is_deleted=False).first() if user_id else None
            if user:
                user.is_deleted = True
                user.save()

                return HttpResponse(json.dumps({"code": 1, "msg": "Success!"}), content_type="json")
            return HttpResponse(json.dumps({"code": 0, "msg": "User not found."}), content_type="json")
        return HttpResponse(json.dumps({"code": 0, "msg": "Invalid method."}), content_type="json")
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return HttpResponse(json.dumps({"code": 0, "msg": "Something went wrong."}), content_type="json")


def user_list_export(request):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        search_field = request.GET.get("field")
        search_keyword = request.GET.get("search")
        sort_by = request.GET.get("sort_by")
        sort_order = request.GET.get("order")

        query = Q()
        if search_field and search_keyword:
            if search_field == "First Name":
                query.add(Q(user__first_name__icontains=search_keyword), query.connector)
            elif search_field == "Last Name":
                query.add(Q(user__last_name__icontains=search_keyword), query.connector)
            elif search_field == "Phone Number":
                query.add(Q(phone_number__icontains=search_keyword), query.connector)
            elif search_field == "Age Group":
                query.add(Q(age_group__icontains=search_keyword), query.connector)

        query.add(Q(is_deleted=False) & Q(user__is_superuser=False), query.connector)

        sort = "-user__date_joined"
        if sort_by == "last_name":
            sort = "user__last_name" if sort_order == "asc" else "-user__last_name"
        elif sort_by == "first_name":
            sort = "user__first_name" if sort_order == "asc" else "-user__first_name"
        elif sort_by == "created_on":
            sort = "user__date_joined" if sort_order == "asc" else "-user__date_joined"
        elif sort_by == "checkin_score":
            sort = "checkin_steps_score" if sort_order == "asc" else "-checkin_steps_score"
        elif sort_by == "status":
            sort = "-user__is_active" if sort_order == "asc" else "user__is_active"

        user_data = []
        users = UserProfile.objects.filter(query).values(
        'user__date_joined',
        'user__first_name',
        'user__last_name',
        'phone_number',
        'age_group',
        'user__is_active',
        ).order_by(sort)

        for user in users:
            user_data.append({
                "Created On": utils.get_date_from_datetime(str(user["user__date_joined"])) if user["user__date_joined"] else "",
                "First Name" : user["user__first_name"],
                'Last Name' : user["user__last_name"],
                "Phone Number": user["phone_number"],
                "Age Group": user["age_group"],
                "Status": "Active" if user["user__is_active"] else "Inactive"})

        df = pd.DataFrame(user_data)

        now = datetime.now().strftime("%d%m%Y")
        file_path = 'media/temp/user/' + str(request.user.id) + "/"
        file_name = "users_" + str(now) + ".csv"
        full_file_path = file_path + file_name
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        df.to_csv(full_file_path, index=False)

        response = FileResponse(open(full_file_path, 'rb'), content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=' + file_name
        return response

    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, 'user_authentication/users.html', {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})


def download_import_users_samplefile(request):
    try:
        full_file_path = 'sample_files/import_users_sample_file.csv'
        response = utils.download_samplefile(full_file_path, "import_users_sample_file.csv")
        if response:
            return response
        return render(request, "user_authentication/users.html", {"msg": "Sample file is not found."})
    except Exception as e:
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "user_authentication/users.html", {"msg": "Something went wrong."})


def import_usersdata(request):
    logged_in_user_data = utils.get_loggedin_user_details(request)
    try:
        if request.method == "POST":
            import_users_file = request.FILES.get("import_users_file")
            if import_users_file and import_users_file.name and ((import_users_file.name).split(".")[-1]).lower() == "csv":
                # with transaction.atomic():
                xlsx_df = pd.read_csv(import_users_file)
                xlsx_df = xlsx_df.map(lambda x: None if pd.isna(x) or x == '' else x)
                users_dict = xlsx_df.to_dict(orient='records')
                mandatory_field = []

                exist_users = []
                created_users = []
                messages = []
                users_create_obj = []
                family_grp_create_obj = []
                family_member_create_obj = []
                usersprofile_create_obj = []
                counter = 0
                existed_phone_number = []
                for user in users_dict:
                    email = user.get("Username") or None
                    first_name = user.get("First Name") or None
                    last_name = user.get("Last Name") or None
                    country_code = user.get("Country Code") or "+852"
                    phone_number = user.get("Phone Number") or None
                    age_range = user.get("Age") or None
                    school = user.get("School") or None
                    grade = user.get("Grade") or None
                    family_group = user.get("Family Group") or None
                    class_number = user.get("Class Number") or None
                    who_is_leader = user.get("Who is leader") or None
                    family_group_name = user.get("Family group name") or user.get("Family group_name") or None
                    member_one_last_name = user.get("Member one last name") or None
                    member_one_first_name = user.get("Member one first name") or None
                    member_one_email = user.get("Member one email") or None
                    member_one_role = user.get("Member one role") or None
                    member_one_age = user.get("Member one age") or None
                    member_one_phone_number = user.get("Member one phone number") or user.get("Member one phone_number") or None
                    member_two_last_name = user.get("Member two last name") or None
                    member_two_first_name = user.get("Member two first name") or None
                    member_two_email = user.get("Member two email") or None
                    member_two_role = user.get("Member two role") or None
                    member_two_age = user.get("Member two age") or None
                    member_two_phone_number = user.get("Member two phone number") or user.get("Member two phone_number") or None
                    member_three_last_name = user.get("Member three last name") or None
                    member_three_first_name = user.get("Member three first name") or None
                    member_three_email = user.get("Member three email") or None
                    member_three_role = user.get("Member three role") or None
                    member_three_age = user.get("Member three age") or None
                    member_three_phone_number = user.get("Member three phone number") or user.get("Member three phone_number") or None

                    if not email and "Username" not in mandatory_field:
                        mandatory_field.append("Username")
                    if not first_name and "First Name" not in mandatory_field:
                        mandatory_field.append("First Name")
                    if not last_name and "Last Name" not in mandatory_field:
                        mandatory_field.append("Last Name")
                    # if not country_code and "Country Code" not in mandatory_field:
                    #     mandatory_field.append("Country Code")
                    if not phone_number and "Phone Number" not in mandatory_field:
                        mandatory_field.append("Phone Number")
                    if not age_range and "Age" not in mandatory_field:
                        mandatory_field.append("Age")
                    if not school and "School" not in mandatory_field:
                        mandatory_field.append("School")
                    if not grade and "Grade" not in mandatory_field:
                        mandatory_field.append("Grade")
                    if not family_group and "Family Group" not in mandatory_field:
                        mandatory_field.append("Family Group")
                    if not who_is_leader and "Who is leader" not in mandatory_field:
                        mandatory_field.append("Who is leader")
                    if not family_group_name and "Family group name" not in mandatory_field:
                        mandatory_field.append("Family group name")
                    if not member_one_last_name and "Member one last name" not in mandatory_field:
                        mandatory_field.append("Member one last name")
                    if not member_one_first_name and "Member one first name" not in mandatory_field:
                        mandatory_field.append("Member one first name")
                    if not member_one_email and "Member one email" not in mandatory_field:
                        mandatory_field.append("Member one email")
                    if not member_one_role and "Member one role" not in mandatory_field:
                        mandatory_field.append("Member one role")
                    if not member_one_age and "Member one age" not in mandatory_field:
                        mandatory_field.append("Member one age")

                    if mandatory_field:
                        break

                    family_group = "Sports" if family_group.lower() == "sports" else "Food"
                    country_code = "+" + str(country_code)  if "+" not in str(country_code) else country_code
                    # phone_num = str(country_code) + " " + str(phone_number)
                    # all_users_email = email + member_one_email + member_two_email + member_three_email
                    members = [
                            (first_name, last_name, email, "領導者", age_range, school, grade, class_number, phone_number, who_is_leader),
                            (member_one_first_name, member_one_last_name, member_one_email, member_one_role, member_one_age, "", "", "", member_one_phone_number, ""),
                        ]

                    if member_two_first_name and member_two_last_name and member_two_email and member_two_role and member_two_age:
                        members.append((member_two_first_name, member_two_last_name, member_two_email, member_two_role, member_two_age, "", "", "", member_two_phone_number, ""))
                    if member_three_first_name and member_three_last_name and member_three_email and member_three_role and member_three_age:
                        members.append((member_three_first_name, member_three_last_name, member_three_email, member_three_role, member_three_age, "", "", "", member_three_phone_number,""))
                    # if not UserProfile.objects.filter(email__in=all_users_email, is_deleted=False).exists() and phone_num not in existed_phone_number:
                    #     existed_phone_number.append(str(country_code) + " " + str(phone_number))

                        # user_obj = User(username=user_name, first_name=first_name, last_name=last_name, email=email, password=make_password(password))
                        # users_create_obj.append(user_obj)

                    category, created = Category.objects.get_or_create(name=family_group)
                    familygroup_obj = None
                    for index, (f_name, l_name, mail, role, age, school, grade, class_number, phone_number, who_is_leader) in enumerate(members):
                        if "@" not in mail:
                            continue
                        counter += 1
                        last_user_id = User.objects.values("id").order_by("-id").first()
                        seq = int(last_user_id["id"]) + 1 + counter if last_user_id else 1
                        full_name = utils.get_user_full_name(f_name, l_name)
                        user_name = full_name.replace(" ", "") + "_" + str(seq)
                        password = utils.generate_password(phone_number)

                        user_obj = User(username=user_name, first_name=f_name, last_name=l_name, email=mail, password=make_password(password))
                        users_create_obj.append(user_obj)

                        profile_obj = UserProfile(user=user_obj, country_code=country_code, phone_number=phone_number,
                                                  age_group=age, role=role, school=school, grade=grade, family_group=family_group,
                                                  class_number=class_number, who_is_leader=who_is_leader, email=mail)
                        usersprofile_create_obj.append(profile_obj)

                        is_leader = False
                        username = mail
                        if index == 0:
                            family_grp_icon = utils.get_random_icon()
                            family_group_name = utils.get_unique_family_group_name(family_group_name)
                            familygroup_obj = FamilyGroups(leader_id=user_obj, family_name=family_group_name, category=category, icon_url=family_grp_icon)
                            family_grp_create_obj.append(familygroup_obj)
                            is_leader = True
                            username = phone_number

                        family_member_obj = FamilyMembers(family_group=familygroup_obj, member=user_obj, is_member=True, is_leader=is_leader)
                        family_member_create_obj.append(family_member_obj)


                        subject = str(Header("賽馬會智家樂計劃  -「智家樂」家庭健康聯盟遊戲報名資訊", 'utf-8'))
                        email_from = settings.DEFAULT_FROM_EMAIL
                        email_to = mail
                        email_data = {
                            "leader_name": full_name,
                            "country_code": country_code,
                            "username": username,
                            "password": password,
                            "signin_link": f"{settings.QUALTRICS_QUESTIONNAIRE_URL}",
                        }
                        html_content = render_to_string("user_authentication/leader_cred_mail.html", email_data)

                        # email = EmailMultiAlternatives(subject, html_content, email_from, [email_to])
                        # # email.bcc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
                        bcc_emails = ['<EMAIL>']
                        # email.attach_alternative(html_content, "text/html")
                        # messages.append(email)

                        message = Mail(
                            from_email=Email(email_from),  # Use Email() to wrap sender
                            to_emails=To(email_to),  # Use To() for recipients
                            subject=subject,
                            html_content=html_content
                        )

                        # Add BCC recipients
                        message.bcc = [Bcc(email) for email in bcc_emails]
                        messages.append(message)

                        created_users.append(phone_number)
                    # else:
                    #     exist_users.append(str(country_code) + " " + str(phone_number))

                if mandatory_field:
                    msg = "`" + ", ".join(mandatory_field) + "` fields are mandatory. Please add relevant data!" if len(mandatory_field) > 1 else "`" + ", ".join(mandatory_field) + "` field is mandatory. Please add relevant data!"
                    create_from_exceptions(request.user.id, str(msg), traceback.format_exc())
                    return render(request, "user_authentication/users.html", {"msg": msg, "logged_in_user_data": logged_in_user_data})

                with transaction.atomic():
                    if users_create_obj and usersprofile_create_obj:
                        # 创建用户
                        created_users = User.objects.bulk_create(users_create_obj)
                        user_ids = [user.id for user in created_users]

                        # 更新用户资料而不是删除后重建
                        if user_ids:
                            # 获取自动创建的UserProfile
                            existing_profiles = UserProfile.objects.filter(user_id__in=user_ids)

                            # 为每个UserProfile更新信息
                            for i, profile in enumerate(existing_profiles):
                                if i < len(usersprofile_create_obj):
                                    new_profile = usersprofile_create_obj[i]
                                    # 更新现有资料的字段
                                    profile.country_code = new_profile.country_code
                                    profile.phone_number = new_profile.phone_number
                                    profile.role = new_profile.role
                                    profile.age_group = new_profile.age_group
                                    profile.email = new_profile.email
                                    profile.school = new_profile.school
                                    profile.grade = new_profile.grade
                                    profile.family_group = new_profile.family_group
                                    profile.class_number = new_profile.class_number
                                    profile.who_is_leader = new_profile.who_is_leader
                                    profile.save()

                            # 创建家庭组和成员
                            FamilyGroups.objects.bulk_create(family_grp_create_obj)
                            FamilyMembers.objects.bulk_create(family_member_create_obj)

                            if messages:
                                response = utils.send_bulk_emails(messages)
                                if response and "error" in response:
                                    create_from_exceptions(request.user.id, response["error"], traceback.format_exc())
                                    return render(request, "user_authentication/users.html", {"msg": response["error"], "logged_in_user_data": logged_in_user_data})

                if exist_users:
                    msg = "User with this phone numbers `" + ", ".join(exist_users) + "` are already exist." if len(exist_users) > 1 else "User with this phone number `" + ", ".join(exist_users) + "` is already exist."
                    create_from_exceptions(request.user.id, str(msg), traceback.format_exc())

                return redirect("users")
            return render(request, "user_authentication/users.html", {"msg": "Please choose correct file to import!", "logged_in_user_data": logged_in_user_data})
        return render(request, "user_authentication/users.html", {"msg": "Invalid method.", "logged_in_user_data": logged_in_user_data})
    except Exception as e:
        # print("error", e, traceback.format_exc())
        create_from_exceptions(request.user.id, e, traceback.format_exc())
        return render(request, "user_authentication/users.html", {"msg": "Something went wrong.", "logged_in_user_data": logged_in_user_data})


"""----------------------DEACTIVATE MEMBER--------------------------------"""
class DeactivateMemberView(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    action_serializers = {
        "deactivate_member":DeactivateMemberSerializer,
    }

    def get_serializer_class(self):
        return self.action_serializers.get(self.action, self.serializer_class)

    @action(methods=['post'], detail=False, url_path='deactivate-member')
    def deactivate_member(self,request):
        try:
            user_id = request.data.get('user_id',None)
            if user_id is None:
                return Response({
                    "success" : False,
                    "message" :"User ID is required."},status=status.HTTP_400_BAD_REQUEST)

            user = User.objects.get(id=user_id)
            if user:
                user.is_active = False
                user.save()

                return Response({
                        "success" : True,
                        "message" : "Member deactivated successfully."},status=status.HTTP_200_OK)

            return Response({
                "success" :False,
                "message" : "User Not Found."},status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            create_from_exceptions(request.user.id if request.user else None, e, traceback.format_exc())
            return Response({
                "success":False,
                "message" : "Something went wrong."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

