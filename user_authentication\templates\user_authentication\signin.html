{% load compress %}
{% load static %}
{% csrf_token %}

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Signin</title>

  <link href="{% static 'fontawesomefree/css/fontawesome.css' %}" rel="stylesheet" type="text/css">
  <link href="{% static 'fontawesomefree/css/brands.css' %}" rel="stylesheet" type="text/css">
  <link href="{% static 'fontawesomefree/css/solid.css' %}" rel="stylesheet" type="text/css">

  <script src="{% static 'base/js/jquery-3.7.1.min.js' %}?v=0.1"></script>
  <script src="{% static 'base/js/axios.min.js' %}?v=0.1"></script>
  <script src="{% static 'base/js/jquery-3.7.1.slim.min.js'%}?v=0.1"></script>

  {% compress css %}
  <link rel="stylesheet" href="{% static 'src/output.css' %}">
  {% endcompress %}

</head>
<body class="bg-[#eff5ff]">
  <section class="dark:bg-gray-900">
    <div class="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0 ">
      <div style="padding: 25px;">
        <img src="{% static 'base\images\hku_family_link_logo.svg' %}" alt="">
      </div>
      <div class="w-full bg-white rounded-lg shadow-lg dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 ">
        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
                <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">
                    Sign in to your account
                </h1>
                <form class="space-y-4 md:space-y-6" method="post" action="{% url 'signin' %}">
                    {% csrf_token %}
                    <div>
                        <label for="id_username" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Username <span class="text-red-500">*</span></label>
                        <input type="text" name="username" id="id_username" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                    </div>
                    <div>
                        <label for="id_password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Password <span class="text-red-500">*</span></label>
                        
                        <div class="relative flex items-center">
                          <input type="password" name="password" id="id_password" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                          
                          <!-- Open eye icon (to show password) -->
                          <svg onclick="showPwd()" id="id_showPwd" class="absolute right-2 w-6 h-6 text-blue-500 hover:text-blue-600 cursor-pointer" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                              <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                              <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                          </svg>
                          
                          <!-- Closed eye icon (to hide password) -->
                          <svg onclick="hidePwd()" id="id_hidePwd" class="absolute hidden right-2 w-6 h-6 text-blue-500 hover:text-blue-600 cursor-pointer" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                              <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                              <line x1="3" y1="3" x2="21" y2="21" stroke="currentColor" stroke-width="2" />
                              <path stroke="currentColor" stroke-width="2" d="M15 12c0-1.65-1.35-3-3-3s-3 1.35-3 3c0 1.65 1.35 3 3 3s3-1.35 3-3Z"/>
                          </svg>
                      </div>
                    </div>
                    <button type="submit" class="w-full text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 border border-gray-300 shadow-lg">Sign in</button>
                </form>
                {% if msg %}
                <div id="idSigninMsg" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
                  <span class="font-medium">{{ msg }}</span>
                  <span onclick="closeMsg()">
                    <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                  </span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
  </section>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script>
    <script src="{% static 'fontawesomefree/js/all.min.js' %}"></script>
</body>
  <script>
    msg = '{{ msg }}';
    if (msg) {
      setTimeout(() => {
        closeMsg();
      }, 10000);
    }

    function closeMsg() {
        $('#idSigninMsg').hide();
    }

    function showPwd() {
      $('#id_showPwd').hide();
      $('#id_hidePwd').show();
      const passwordInput = document.getElementById('id_password');
      passwordInput.type = 'text';
    }

    function hidePwd() {
      $('#id_showPwd').show();
      $('#id_hidePwd').hide();
      const passwordInput = document.getElementById('id_password');
      passwordInput.type = 'password';
    }
  </script>
</html>