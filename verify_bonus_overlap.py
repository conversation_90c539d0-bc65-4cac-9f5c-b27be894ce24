#!/usr/bin/env python3
"""
验证Bonus questions在Diet和Exercise之间的重叠问题
"""

import pandas as pd

def verify_bonus_overlap():
    """验证Bonus questions重叠问题"""
    
    try:
        print("🔍 验证Bonus questions重叠问题...")
        
        # 读取两个导出文件
        food_file = "hku_exercise_export_Food_20250828_105756.csv"
        sports_file = "hku_exercise_export_Sports_20250828_105757.csv"
        
        print(f"📖 读取文件:")
        print(f"   Food: {food_file}")
        print(f"   Sports: {sports_file}")
        
        food_df = pd.read_csv(food_file)
        sports_df = pd.read_csv(sports_file)
        
        print(f"\n📊 基本统计:")
        print(f"   Food总记录: {len(food_df)}")
        print(f"   Sports总记录: {len(sports_df)}")
        
        # 1. 分析Quiz Type分布
        print(f"\n📋 Quiz Type分布:")
        
        food_quiz_types = food_df['Quiz Type'].value_counts()
        sports_quiz_types = sports_df['Quiz Type'].value_counts()
        
        print(f"   Food类别:")
        for quiz_type, count in food_quiz_types.items():
            print(f"     {quiz_type}: {count} 条")
        
        print(f"   Sports类别:")
        for quiz_type, count in sports_quiz_types.items():
            print(f"     {quiz_type}: {count} 条")
        
        # 2. 检查Bonus questions的重叠
        print(f"\n🔍 检查Bonus questions重叠:")
        
        # 提取Bonus questions
        food_bonus = food_df[food_df['Quiz Type'] == 'Bonus']
        sports_bonus = sports_df[sports_df['Quiz Type'] == 'Bonus']
        
        print(f"   Food中的Bonus: {len(food_bonus)} 条")
        print(f"   Sports中的Bonus: {len(sports_bonus)} 条")
        
        # 检查家庭组重叠
        food_bonus_families = set(food_bonus['Family Name'].unique())
        sports_bonus_families = set(sports_bonus['Family Name'].unique())
        
        print(f"   Food Bonus涉及家庭组: {len(food_bonus_families)} 个")
        print(f"   Sports Bonus涉及家庭组: {len(sports_bonus_families)} 个")
        
        # 重叠的家庭组
        overlapped_families = food_bonus_families & sports_bonus_families
        print(f"   重叠的家庭组: {len(overlapped_families)} 个")
        
        if overlapped_families:
            print(f"   重叠家庭组列表:")
            for family in sorted(overlapped_families):
                print(f"     {family}")
        
        # 3. 检查具体的重叠记录
        print(f"\n🔍 检查具体重叠记录:")
        
        if len(overlapped_families) > 0:
            # 选择一个重叠家庭组进行详细分析
            sample_family = list(overlapped_families)[0]
            print(f"   以'{sample_family}'为例:")
            
            food_sample = food_bonus[food_bonus['Family Name'] == sample_family]
            sports_sample = sports_bonus[sports_bonus['Family Name'] == sample_family]
            
            print(f"     Food中的记录: {len(food_sample)} 条")
            print(f"     Sports中的记录: {len(sports_sample)} 条")
            
            # 检查是否是完全相同的记录
            if len(food_sample) > 0 and len(sports_sample) > 0:
                print(f"     Food中的问题:")
                for _, row in food_sample.head(3).iterrows():
                    print(f"       {row['Answer Date']} | {row['Member Name']} | {row['Question'][:50]}...")
                
                print(f"     Sports中的问题:")
                for _, row in sports_sample.head(3).iterrows():
                    print(f"       {row['Answer Date']} | {row['Member Name']} | {row['Question'][:50]}...")
        
        # 4. 分析Regular questions的分离情况
        print(f"\n📋 Regular questions分离情况:")
        
        food_regular = food_df[food_df['Quiz Type'] != 'Bonus']
        sports_regular = sports_df[sports_df['Quiz Type'] != 'Bonus']
        
        food_regular_families = set(food_regular['Family Name'].unique())
        sports_regular_families = set(sports_regular['Family Name'].unique())
        
        print(f"   Food Regular涉及家庭组: {len(food_regular_families)} 个")
        print(f"   Sports Regular涉及家庭组: {len(sports_regular_families)} 个")
        
        regular_overlap = food_regular_families & sports_regular_families
        print(f"   Regular questions重叠家庭组: {len(regular_overlap)} 个")
        
        if regular_overlap:
            print(f"   Regular重叠家庭组:")
            for family in sorted(regular_overlap):
                print(f"     {family}")
        
        # 5. 建议的解决方案
        print(f"\n💡 问题分析和建议:")
        
        if len(overlapped_families) > 0:
            print(f"   ❌ 确认存在Bonus questions重叠问题!")
            print(f"   📊 重叠统计:")
            print(f"     - {len(overlapped_families)} 个家庭组的Bonus questions同时出现在两个文件中")
            print(f"     - 这导致了数据重复计算")
            
            print(f"\n   🔧 建议的解决方案:")
            print(f"   1. 方案A: 将所有Bonus questions单独导出为一个文件")
            print(f"   2. 方案B: 只在一个类别中包含Bonus questions")
            print(f"   3. 方案C: 根据家庭组的主要类别分配Bonus questions")
            
            # 计算如果移除重叠后的记录数
            total_bonus_records = len(food_bonus) + len(sports_bonus)
            
            # 找出重叠的具体记录
            overlap_records = 0
            for family in overlapped_families:
                family_food_bonus = len(food_bonus[food_bonus['Family Name'] == family])
                family_sports_bonus = len(sports_bonus[sports_bonus['Family Name'] == family])
                overlap_records += min(family_food_bonus, family_sports_bonus)
            
            print(f"\n   📊 重叠影响:")
            print(f"     - 总Bonus记录: {total_bonus_records}")
            print(f"     - 重叠记录: {overlap_records}")
            print(f"     - 去重后应该有: {total_bonus_records - overlap_records} 条Bonus记录")
            
        else:
            print(f"   ✅ 没有发现Bonus questions重叠问题")
        
        if len(regular_overlap) > 0:
            print(f"   ⚠️  Regular questions也有重叠，这不应该发生!")
        else:
            print(f"   ✅ Regular questions正确分离，没有重叠")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_bonus_overlap()
