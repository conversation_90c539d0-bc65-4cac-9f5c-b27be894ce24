from django.db import models
from django.contrib.auth.models import User
# Create your models here.


class ExceptionLogs(models.Model):
    error_type = models.CharField(max_length=500, null=True, blank=True)
    error_msg = models.TextField(null=True, blank=True)
    traceback = models.TextField(null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)