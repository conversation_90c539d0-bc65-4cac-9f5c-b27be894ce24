import os, shutil, re, random, string
from django.http import FileResponse
from django.core.cache import cache
from django.core.paginator import Paginator
from datetime import datetime
from django.contrib.auth.models import User
from django.contrib.auth.hashers import make_password, check_password
from rest_framework.response import Response
from rest_framework import status
from family.models import FamilyGroupFile, FamilyMembers, FamilyGroups
from question.models import Leaderboard
from user_authentication.models import UserProfile
import sendgrid
from sendgrid.helpers.mail import Mail, Email, Personalization
from django.conf import settings

def remove_file(file_path):
    if os.path.exists(file_path):
        shutil.rmtree(file_path)
        response = "Success"
    else:
        response = "File not found"
    return response


def download_samplefile(full_file_path, file_name):
    if os.path.exists(full_file_path):
        response = FileResponse(open(full_file_path, 'rb'), content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=' + str(file_name)
        return response
    return None
    

def get_user_full_name(first_name, last_name):
    user_full_name = ""
    if first_name and last_name:
        user_full_name = first_name + " " + last_name
    elif first_name and not last_name:
        user_full_name = first_name
    elif not first_name and last_name:
        user_full_name = last_name
    return user_full_name


def get_dict_from_queryset(queryset_data, key, value):
    dict_data = {}
    for query in queryset_data:
        dict_data[query[key]] = query[value]
    
    return dict_data


def set_cache(key, value, timeout=3600):
    cache.set(key, value, timeout) 


def get_cache(key):
    data = cache.get(key)
    return data


def delete_cache(key):
    cache.delete(key)


def validate_phone_number(phone_number):
    """Validate the phone number."""
    try:
        pattern = re.compile(r'^[0-9]{8}$')
        return bool(pattern.match(str(phone_number)))
    except:
        return False

def check_int_value(value):
    """Validate the phone number."""
    try:
        pattern = re.compile(r'^[0-9]$')
        return bool(pattern.match(str(value)))
    except:
        return False


def validate_password(password):
    """Validate the password."""
    try:
        pattern = re.compile(r'^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$')
        return bool(pattern.match(password))
    except:
        return False

  
def generate_password(phone_number=None):
    # 如果提供了手机号码，则使用手机号码+@@jcsfl作为密码
    if phone_number:
        return f"{phone_number}@@jcsfl"
    
    # 如果没有提供手机号码，则生成随机密码作为后备
    # Define the character sets
    upper = random.choice(string.ascii_uppercase)
    lower = random.choice(string.ascii_lowercase)
    digit = random.choice(string.digits)
    special = random.choice('@$!%*?&')
    
    # Generate the remaining characters
    remaining_length = 4
    remaining_chars = random.choices(string.ascii_letters + string.digits + '@$!%*?&', k=remaining_length)
    
    password_list = [upper, lower, digit, special] + remaining_chars
    random.shuffle(password_list)
    
    password = ''.join(password_list)
    return password


def generate_temp_password():
    while True:
        password = generate_password()
        exist_passwords = list(User.objects.values_list("password", flat=True))
        for exist_password in exist_passwords:
            if not check_password(password, exist_password):
                return password


def get_unique_username(user_name):
    while True:
        if User.objects.filter(username=user_name).exists():
            data = user_name.split("_")
            full_name = data[0]
            seq = int(data[-1]) + 1
            user_name = full_name + "_" + str(seq)
            continue
        else:
            return user_name

    
def validate_age_group_format(age_group):
    # Define a regex pattern for age group range like "18-25"
    pattern = r'^\d{1,2}-\d{1,2}$'  # Matches a format like "18-25", where each number is 1 or 2 digits
    
    # Use re.match to check if age_group matches the defined pattern
    if isinstance(age_group, str) and re.match(pattern, age_group):
        return True
    else:
        return False
    

def get_loggedin_user_details(request):
    user_data = {}
    if request.user:
        user_full_name = get_user_full_name(request.user.first_name, request.user.last_name)
        user_data = {
            "user_id": request.user.id,
            "user_full_name": user_full_name,
        }
        
    return user_data


def get_pagination(request, query_obj, page_number, page_length):
    page_length = int(page_length) if page_length else 10
    paginator = Paginator(query_obj, page_length)
    page_obj = paginator.get_page(page_number)
    total_items = paginator.count
    
    start = 0
    end = page_length
    if page_number:
        if int(page_number) == 1:
            start = 0
            end = page_length
        else:
            start = end * (int(page_number) - 1)
            end = end * int(page_number)
    
    return page_obj, total_items, start, end


def get_date_from_datetime(datetime_obj):
    try:
        date = datetime.strptime(str(datetime_obj), "%Y-%m-%d %H:%M:%S%z").strftime("%Y-%m-%d")
        return date
    except:
        date = datetime.strptime(str(datetime_obj), "%Y-%m-%d %H:%M:%S.%f%z").strftime("%Y-%m-%d")
        return date

def get_required_msg(data):
    message = ', '.join(data)
    message += ' field is required.' if len(data) == 1 else ' fields are required.'
    return message
    

def custom_error_response(message="An error occurred", status=status.HTTP_400_BAD_REQUEST):
    """
    Custom function to generate an error response.

    Args:
        errors (dict): The error details.
        status_code (int): The HTTP status code for the response (default: 400).
        message (str): The message to be included in the response (default: "An error occurred").

    Returns:
        Response: The custom error response object.
    """
    custom_response_data = {
        'success':False,
        'message': message,
    }
    return Response(custom_response_data, status=status)


def custom_data_response(data=None, message="Success", status_code=status.HTTP_200_OK):
    """
    Custom function to generate a data response.

    Args:
        data (dict): The data to be included in the response.
        status_code (int): The HTTP status code for the response (default: 200).
        message (str): The message to be included in the response (default: "Success").

    Returns:
        Response: The custom data response object.
    """
    custom_response_data = {
        'success':True,
        'message': message,
    }
    if data:
        custom_response_data['data'] = data
    return Response(custom_response_data, status=status_code)


def validate_file_extension(file_name):
    """
    Validate the file extension of a given file name.

    This function checks whether the file extension of the provided file nameis one of the allowed extensions. 
    The allowed extensions are:'jpg', 'jpeg', 'heic', and 'png'.

    Args:
        file_name (str): The name of the file to validate, including the extension.

    Returns:
        bool: True if the file extension is allowed, False otherwise.
    """
    allowed_extensions = ['jpg', 'jpeg', 'heic', 'png']
    ext = file_name.split('.')[-1].lower()
    return ext in allowed_extensions


def validate_country_code(country_code):
    """Validate the phone number."""
    try:
        pattern = re.compile(r'^\+?[0-9]+$')
        return bool(pattern.match(country_code))
    except:
        return False
    

def get_family_group_dp(family_group_ids):
    family_group_id_pic = {}
    try:
        family_group_pics = FamilyGroupFile.objects.filter(family_group_id__in=family_group_ids)
        for family_group_pic in family_group_pics:
            family_group_id_pic[family_group_pic.family_group.id] = family_group_pic.file.url if family_group_pic.file else ""
        
        return family_group_id_pic
    except:
        return family_group_id_pic
    

def get_leaderboard(user_id):
    family_member = FamilyMembers.objects.filter(member_id = user_id).first()
    try:
        leader_board = Leaderboard.objects.get(family_group=family_member.family_group, category = family_member.family_group.category)
    except Leaderboard.DoesNotExist:
        leader_board = Leaderboard.objects.create(family_group=family_member.family_group, category = family_member.family_group.category)
    return leader_board


def check_user_exist(user_id):
    if UserProfile.objects.filter(user_id=user_id, is_deleted=False, user__is_active=True).exists():
        return True
    return False


def get_family_member_category(user_id):
    family_category = FamilyMembers.objects.filter(member_id=user_id, is_deleted=False).values("family_group__category__name").first()
    if family_category:
        return family_category["family_group__category__name"]
    return None


def get_random_icon():
    icon_urls = ['https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/2-superhero-6.png',
                'https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/1-superhero-5.png',
                'https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/8-punch-3.png',
                'https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/3-superhero-7.png',
                'https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/4-superhero-8.png',
                'https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/5-superhero-9.png',
                'https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/6-superhero-10.png',
                'https://dyi4phcstz7uq.cloudfront.net/media/family_group/image/7-robot.png']
    random_icon = random.choice(icon_urls)
    return random_icon


def get_unique_family_group_name(family_group):
    if FamilyGroups.objects.filter(family_name=family_group).exists():
        latest_id = FamilyGroups.objects.values("id").order_by("-id").first()
        if latest_id:
            family_group = family_group + str(latest_id["id"])
        else:
            family_group = family_group + str(1)
        return family_group
    else:
        return family_group
    

def send_bulk_emails(messages):
    """ Function to send bulk emails using SendGrid """
    sg = sendgrid.SendGridAPIClient(settings.EMAIL_HOST_PASSWORD)
    
    try:
        for message in messages:
            response = sg.send(message)
            # response = message.send()
        print(f"Bulk email sent - Status: {response.status_code}")
    except Exception as e:
        print(f"SendGrid Email Error: {e}")
        return {"error": f"SendGrid Email Error: {e}"}