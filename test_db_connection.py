#!/usr/bin/env python3
"""
测试数据库连接
"""

import psycopg2
import sys

def test_database_connection():
    """测试数据库连接"""
    
    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }
    
    print("🔍 测试数据库连接...")
    print(f"📡 主机: {db_config['host']}")
    print(f"🔌 端口: {db_config['port']}")
    print(f"🗄️  数据库: {db_config['database']}")
    print(f"👤 用户: {db_config['user']}")
    
    try:
        print("\n⏳ 尝试连接...")
        conn = psycopg2.connect(**db_config)
        print("✅ 数据库连接成功!")
        
        # 测试基本查询
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"📊 PostgreSQL版本: {version[0]}")
        
        # 测试表是否存在
        print(f"\n🔍 检查关键表...")
        tables_to_check = [
            'question_userquestion',
            'question_question', 
            'auth_user',
            'family_familymembers',
            'family_familygroups',
            'family_category'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} 条记录")
            except Exception as e:
                print(f"   ❌ {table}: 错误 - {e}")
        
        # 测试日期范围内的数据
        print(f"\n📅 检查日期范围内的数据 (2025-05-05 到 2025-05-19)...")
        try:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM question_userquestion 
                WHERE date BETWEEN '2025-05-05' AND '2025-05-19'
                AND answered = true
                AND is_deleted = false
            """)
            date_count = cursor.fetchone()[0]
            print(f"   📊 该日期范围内的quiz记录: {date_count}")
            
            if date_count == 0:
                print("   ⚠️  警告: 指定日期范围内没有数据!")
                
                # 检查实际的日期范围
                cursor.execute("""
                    SELECT MIN(date), MAX(date), COUNT(*)
                    FROM question_userquestion 
                    WHERE answered = true AND is_deleted = false
                """)
                min_date, max_date, total_count = cursor.fetchone()
                print(f"   📅 实际数据日期范围: {min_date} 到 {max_date}")
                print(f"   📊 总quiz记录数: {total_count}")
        except Exception as e:
            print(f"   ❌ 日期查询错误: {e}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ 数据库连接失败 (OperationalError): {e}")
        print(f"\n🔧 可能的原因:")
        print(f"   1. 网络连接问题")
        print(f"   2. 数据库服务器不可达")
        print(f"   3. 防火墙阻止连接")
        print(f"   4. 数据库服务器维护中")
        return False
        
    except psycopg2.DatabaseError as e:
        print(f"❌ 数据库错误 (DatabaseError): {e}")
        print(f"\n🔧 可能的原因:")
        print(f"   1. 数据库名称错误")
        print(f"   2. 用户权限不足")
        print(f"   3. 数据库不存在")
        return False
        
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print(f"\n🌐 测试网络连接...")
    
    import socket
    
    try:
        # 测试DNS解析
        host = 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com'
        ip = socket.gethostbyname(host)
        print(f"✅ DNS解析成功: {host} -> {ip}")
        
        # 测试端口连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, 5432))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口5432连接成功")
            return True
        else:
            print(f"❌ 端口5432连接失败 (错误代码: {result})")
            return False
            
    except socket.gaierror as e:
        print(f"❌ DNS解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")
        return False

def check_psycopg2():
    """检查psycopg2模块"""
    print(f"\n📦 检查psycopg2模块...")
    
    try:
        import psycopg2
        print(f"✅ psycopg2版本: {psycopg2.__version__}")
        return True
    except ImportError as e:
        print(f"❌ psycopg2模块未安装: {e}")
        print(f"💡 请运行: pip install psycopg2-binary")
        return False

def main():
    """主函数"""
    print("🔧 数据库连接诊断工具")
    print("=" * 50)
    
    # 检查模块
    if not check_psycopg2():
        return
    
    # 检查网络
    if not test_network_connectivity():
        print(f"\n💡 建议:")
        print(f"   1. 检查网络连接")
        print(f"   2. 检查防火墙设置")
        print(f"   3. 确认VPN连接（如果需要）")
        return
    
    # 测试数据库连接
    if test_database_connection():
        print(f"\n🎉 数据库连接测试成功!")
        print(f"💡 可以继续进行数据导出")
    else:
        print(f"\n❌ 数据库连接测试失败")
        print(f"💡 请检查连接配置和网络状态")

if __name__ == "__main__":
    main()
