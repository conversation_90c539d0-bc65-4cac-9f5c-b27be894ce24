{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Question list' %}{% endblock %}
{% block content %}
{% load static %}

{% for message in messages %}
<div id="idUserMsg-{{ forloop.counter }}" class="p-4 mb-4 text-sm 
{% if message.tags == 'success' %}
    text-green-800 bg-green-50 dark:text-green-400 border-l-green-800
{% else %}
    text-red-800 bg-red-50 dark:text-red-400 border-l-red-800
{% endif %}
    rounded-lg dark:bg-gray-800 flex justify-between shadow-lg border-l-4" role="alert">
    <span class="font-medium">{{ message }}</span>
    <button onclick="document.getElementById('idUserMsg-{{ forloop.counter }}').style.display='none'">
      <svg class="fill-current h-6 w-6 
        {% if message.tags == 'success' %}
            text-green-800
        {% else %}
            text-red-800
        {% endif %}" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </button>
</div>
{% endfor %}

<div class="p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg-lg p-6">
        <h1 class="text-3xl font-extrabold mb-6 text-gray-900 dark:text-white">{% trans 'Question Detail' %}</h1>
        
        <div class="bg-gray-100 dark:bg-gray-700 p-6 rounded-lg mb-4">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-gray-900 dark:text-gray-100" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zm0 21.938c-5.48 0-9.938-4.458-9.938-9.938S6.52 2.062 12 2.062 21.938 6.52 21.938 12 17.48 21.938 12 21.938zM11 11H8.707l1.147-1.147-1.414-1.414L6.293 11l-2.147-2.147 1.414-1.414L8.707 9H11V6.293L9.853 7.44l-1.414-1.414L11 3.293V1h2v2.293l2.561 2.561-1.414 1.414L13 6.293V9h2.293l-1.147-1.147 1.414-1.414L17.707 11H15v2.293l2.147-2.147 1.414 1.414-2.147 2.147L17.707 15H15v2.293l1.147-1.147 1.414 1.414L15 20.707V23h-2v-2.293l-2.561-2.561 1.414-1.414L11 15V12.707L9.853 13.854l-1.414-1.414L11 11z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ question.id }}. {{ question.question }}</h3>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Type:' %}</strong> {{ question.get_type_display }}</p>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Option Type:' %}</strong> {{ question.get_option_type_display }}</p>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Options:' %}</strong> {{ question.option }}</p>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Answer:' %}</strong> {{ question.answer }}</p>
                </div>
                <div>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Explanation:' %}</strong> {{ question.explanation }}</p>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Category:' %}</strong> {{ question.category }}</p>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Shuffle:' %}</strong> {{ question.shuffle }}</p>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Score:' %}</strong> {{ question.score }}</p>
                    <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Quiz Type:' %}</strong> {{ question.get_quiz_type_display }}</p>
                </div>
            </div>

            <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Date:' %}</strong> {{ question.date }}</p>
            <p class="text-gray-700 dark:text-gray-300"><strong>{% trans 'Created On:' %}</strong> {{ question.created_on }}</p>
        </div>

        {% if question.answer %}
        <div class="bg-green-100 dark:bg-green-700 p-6 rounded-lg">
            <h3 class="text-lg font-semibold text-green-900 dark:text-green-100">{% trans 'Answer' %}</h3>
            <p class="text-gray-700 dark:text-gray-300">{{ question.answer }}</p>
        </div>
        {% else %}
        <div class="bg-red-100 dark:bg-red-700 p-6 rounded-lg">
            <h3 class="text-lg font-semibold text-red-900 dark:text-red-100">{% trans 'No Answer Yet' %}</h3>
            <p class="text-gray-700 dark:text-gray-300">{% trans 'This question has not been answered yet.' %}</p>
        </div>
        {% endif %}
    </div>
</div>

{% endblock content %}
