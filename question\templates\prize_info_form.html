{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Prize Info' %}{% endblock %}
{% block content %}
{% load static %}

<script src="{% static "tinymce/tinymce.min.js" %}"></script>
<script src="{% static "tinymce/jquery.tinymce.min.js" %}"></script>


{% for message in messages %}
{% comment %} <div id="toast-success" class="flex items-center w-full max-w-full p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-lg dark:text-gray-400 dark:bg-gray-800" role="alert"> {% endcomment %}
    {% if message.tags == 'error' %}
    <div id="prizeInfoError" class="error-msg p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
        <span class="font-medium">{{message}}</span>
        <span onclick="closeMsg('prizeInfoError')">
            <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
    </div>
    {% elif message.tags == "success"%}
    <div id="prizeInfoSuccess" class="error-msg p-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 flex justify-between shadow-lg border-l-green-800 border-l-4" role="alert">
        <span class="font-medium">{{message}}</span>
        <span onclick="closeMsg('prizeInfoSuccess')">
            <svg class="fill-current h-6 w-6 text-green-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
    </div>
    {% else %}
    <div id="prizeInfoWarn" class="error-msg p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 flex justify-between shadow-lg border-l-yellow-800 border-l-4" role="alert">
        <span class="font-medium">{{message}}</span>
        <span onclick="closeMsg('prizeInfoWarn')">
            <svg class="fill-current h-6 w-6 text-yellow-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
    </div>
    {% endif %}
{% comment %} <div class="ms-3 text-sm font-normal">{{message}}</div> {% endcomment %}
    {% comment %} <button type="button" class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" data-dismiss-target="#toast-success" aria-label="Close">
        <span class="sr-only">Close</span>
        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
        </svg>
    </button> {% endcomment %}
{% comment %} </div> {% endcomment %}
{% endfor %}



<div class="p-4">
    <div class="mb-3 flex justify-between">
        <h1 class="text-2xl font-bold mb-4">{% trans 'Prize Information' %}</h1>
    </div>
    
    <div class="block max-w-sm p-6 bg-white rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">
        <form action="{% url 'update_prize_info' %}" method="post">
            {% csrf_token %}
            <!-- <label for="prize_info" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Info</label> -->
            <textarea id="id_prize_info" name="prize_info" cols="80" rows="15" class="tinymce block p-2.5 w-full text-sm text-gray-900 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Write here..." >{% if prize_info %}{{ prize_info }}{% endif %}</textarea>
            <button type="submit" id="savePrizeInfo" style="margin-top: 20px;" class="hidden text-white bg-blue-500 mt-5 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg">{% trans 'Save' %}</button>
            <button type="button" id="editPrizeInfo" style="margin-top: 20px;" onclick="editPrizeInfoText()" class="text-white bg-blue-500 mt-5 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 border border-gray-300 shadow-lg">{% trans 'Edit' %}</button>
        </form>
    </div>
</div>

<script>
    tinymce.init({
        mode:"satndard",
        selector: 'textarea#id_prize_info',
        plugins: 'advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime table paste code help wordcount ',
        menubar: false, // Hide the menu bar
        toolbar: 'undo redo | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | styleselect | numlist bullist',
        branding: false,
        content_style:  `
            :root {
            --tw-bg-opacity: 1;
            }
            body {
            background-color: rgb(229 231 235 / var(--tw-bg-opacity));
            color: #000; /* Set the text color to black */
            }
        `,
        setup: function (editor) {
            editor.on('init', function () {
            editor.setMode('readonly'); // Set the editor to read-only mode after initialization
            });
        }
        // other configurations...
    });
  
  function editPrizeInfoText() {
    tinymce.get('id_prize_info').setMode('design');
    const editor = tinymce.get('id_prize_info');
    if (editor) {
        editor.contentDocument.head.querySelectorAll('style').forEach(style => style.remove());
        editor.focus();
        editor.selection.select(editor.getBody(), true);
        editor.selection.collapse(false);
    }
    $("#savePrizeInfo").show();
    $("#editPrizeInfo").hide();
  }

  $(document).ready(function() { 
        setTimeout(() => {
            $('.error-msg').hide();
        }, 10000);
    })
  
  </script>
{% endblock content %}
