# Use the latest Python image from Docker Hub
FROM python:latest

# Set environment variables
ENV PYTHONBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set the working directory inside the container
WORKDIR /HKU-GAME-BACKEND

# Copy the requirements.txt file to the working directory
COPY requirements.txt /HKU-GAME-BACKEND/

# Install Python dependencies
RUN pip install -r requirements.txt

# Copy the current directory contents into the container at /HKU-GAME-BACKEND/
COPY . /HKU-GAME-BACKEND/

# Expose port 8000 to the outside world
EXPOSE 80


CMD [ "bash", "-c", "python manage.py makemigrations && python manage.py migrate && gunicorn --bind 0.0.0.0:80 hku_student_quiz.wsgi" ]
#command: bash -c "python manage.py makemigrations && python manage.py migrate && gunicorn --bind 0.0.0.0:80 hku_student_quiz.wsgi"
    
  