{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Update User' %}{% endblock %}
{% block content %}
{% load static %}

{% if msg %}
<div id="idUserMsg" class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
    <span class="font-medium">{{ msg }}</span>
    <span onclick="closeMsg('idUserMsg')">
      <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </span>
</div>
{% endif %}
<div class="p-4">
  <div class="p-4 rounded-lg dark:border-gray-700 pt-2 pl-2 pr-2 pb-0">
      <h1 class="text-2xl font-bold mb-6">{% trans 'Update User' %}</h1>
      <div class="flex bg-white rounded-lg shadow-lg flex-col items-center justify-center px-6 py-16 mx-auto">
        <div class="w-full dark:border md:mt-0 sm:max-w-7xl dark:bg-gray-800 dark:border-gray-700 ">
          <form id="userForm" method="post" action="/user/{{ user_data.user_id }}/" class="max-w-4xl mx-auto">
            {% csrf_token %}
            <div class="grid grid-cols-2 gap-2">
              <div class="mb-5">
                <label for="id_firstName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'First Name' %} <span class="text-red-500">*</span></label>
                <input type="text" name="first_name" id="id_firstName" value="{{ user_data.first_name }}" class="inputField bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required />
              </div>
              <div class="mb-5">
                <label for="id_lastName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Last Name' %} <span class="text-red-500">*</span></label>
                <input type="text" name="last_name" id="id_lastName" value="{{ user_data.last_name }}" class="inputField bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required />
              </div>
              <div class="mb-5">
                <label for="id_phoneNumber" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Phone Number' %}</label>
                <input id="id_phoneNumber" value="{{ user_data.country_code }}  {{ user_data.phone_number }}" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 bg-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled />
              </div>
              <div class="mb-5">
                <label for="id_ageGrp" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Age Group' %} <span class="text-red-500">*</span></label>
                <select id="id_ageGrp" name="age_grp" class="inputField bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                {% if user_data.role == "領導者" %}
                  {% for age_grp in age_grp_li %}
                  <option value="{{ age_grp }}">{{ age_grp }}</option>
                  {% endfor %}
                {% else %}
                  <option value="<12"><12</option>
                  <option value="12-17">12-17</option>
                  <option value="18-24">18-24</option>
                  <option value="25-34">25-34</option>
                  <option value="35-44">35-44</option>
                  <option value="45-54">45-54</option>
                  <option value="55-64">55-64</option>
                  <option value="65-70">65-70</option>
                  <option value="71-80">71-80</option>
                  <option value=">80">>80</option>
                {% endif %}
                </select>
              </div>
              <div class="mb-5">
                <label for="id_role" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Role' %} {% if user_data.role != "領導者" %} <span class="text-red-500">*</span> {% endif %}</label>
                {% if user_data.role == "領導者" %}
                <input id="id_role" value="{{ user_data.role }}" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 bg-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled />
                {% else %}
                <select id="id_role" name="role" class="inputField bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                  <option value="兄弟">兄弟</option>
                  <option value="姊妹">姊妹</option>
                  <option value="祖父/外公">祖父/外公</option>
                  <option value="祖母/外婆">祖母/外婆</option>
                  <option value="母親">母親</option>
                  <option value="父親">父親</option>
                  <option value="其他">其他</option>
                  <option value="子女">子女</option>
                  <option value="⁠配偶">⁠配偶</option>
                </select>
                {% endif %}
              </div>
              <div class="mb-5">
                <label for="id_email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Email' %}</label>
                <input id="id_email" value="{{ user_data.email }}" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 bg-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled />
              </div>
              <div class="mb-5">
                <label for="id_school" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'School' %}</label>
                <input id="id_school" title="{{ user_data.school }}" value="{{ user_data.school }}" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 bg-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled />
              </div>
              <div class="mb-5">
                <label for="id_grade" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Grade' %}</label>
                <input id="id_grade" value="{{ user_data.grade }}" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 bg-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled />
              </div>
              <div class="mb-5">
                <label for="id_class_number" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Class Number' %}</label>
                <input id="id_class_number" value="{{ user_data.class_number }}" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 bg-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled />
              </div>
              <div class="mb-5">
                <label for="id_checkin_score" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Checkin Score' %}</label>
                <input id="id_checkin_score" value="{{ user_data.checkin_steps_score }}" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 bg-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled />
              </div>
              <div class="mb-5">
                <label for="id_status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans 'Status' %} <span class="text-red-500">*</span></label>
                <select id="id_status" name="status" class="inputField bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
          </div>

            <button type="submit" class="btn-click text-white bg-blue-500 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 border border-gray-300 shadow-lg">{% trans 'Save' %}</button>
          </form>
        </div>
      </div>
  </div>
</div>
<script>
  var changed = false;
  $(document).ready(function() { 
        msg = '{{ msg }}';
        if (msg) {
            setTimeout(() => {
                $('#idUserMsg').hide();
            }, 10000);
        }

        var dropdown = document.getElementById('id_ageGrp');
        var age_group = '{{ user_data.age_group }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === age_group) {
                dropdown.options[i].selected = true;
                break;
            }
        }

        var dropdown = document.getElementById('id_role');
        var role = '{{ user_data.role }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === role) {
                dropdown.options[i].selected = true;
                break;
            }
        }

        var dropdown = document.getElementById('id_status');
        var status = '{{ user_data.is_active }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === status) {
                dropdown.options[i].selected = true;
                break;
            }
        }

        checkFormValueChange();
  });

  window.addEventListener('beforeunload', function(event) {
      if (changed) {
          var confirmationMessage = 'Are you sure you want to leave this page?';
          (event || window.event).returnValue = confirmationMessage;
          return confirmationMessage;
      }
  });

  $('.btn-click').click(function () {
      changed = false;
  })

  function checkFormValueChange() {
      // Get the form and input fields
      var form = document.getElementById('userForm');
      var inputFields = document.getElementsByClassName('inputField');

      // Store the initial values of the input fields
      var initialValues = [];
      for (var i = 0; i < inputFields.length; i++) {
          initialValues.push(inputFields[i].value);
      }

      // Add event listener for input changes
      for (var i = 0; i < inputFields.length; i++) {
          inputFields[i].addEventListener('input', function() {
              for (var j = 0; j < inputFields.length; j++) {
                  if (inputFields[j].value !== initialValues[j]) {
                      changed = true;
                      break;
                  }
              }
          });
      }
  }
</script>
{% endblock content %}