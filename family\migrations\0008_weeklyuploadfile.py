# Generated by Django 5.0.6 on 2024-07-25 09:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("family", "0007_alter_familymemberssteps_file"),
    ]

    operations = [
        migrations.CreateModel(
            name="WeeklyUploadFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        blank=True, null=True, upload_to="weekly_uploaded_file/image"
                    ),
                ),
                ("file_name", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "weekly_uploaded_file",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="family.familygroups",
                    ),
                ),
            ],
        ),
    ]
