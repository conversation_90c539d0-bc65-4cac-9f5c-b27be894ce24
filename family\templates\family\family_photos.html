{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Family Photos' %}{% endblock %}
{% block content %}
{% load static %}

{% if msg %}
<div id="idFamilyPhotoMsg" class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
    <span class="font-medium">{{ msg }}</span>
    <span onclick="closeMsg('idFamilyPhotoMsg')">
        <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </span>
</div>
{% endif %}
 
<div class="p-4">
    <div class="mb-3 flex">
        <h1 class="text-2xl font-bold mb-4">{% trans 'Family Photos' %}</h1> <span class="text-2xl text-gray-500">&nbsp- {{ family_group_name }} ( {{ family_group_category }} )</span>
    </div>
 
    <div class="flex flex-col md:flex-row w-full mx-auto mb-3 justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
 
        <!-- Page length dropdown -->
        <div id="dropdown" class="flex items-center space-x-2 z-10 w-full md:w-auto">
 
            <span>Show</span>
                <select id="id_pageLength" name="page_length" onchange="changePageLength('{{family_group_id}}')" class="shadow-lg w-fit bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>
 
            <div id="date-range-picker" date-rangepicker class="flex items-center">
                <div class="relative flex">
                    <div class="inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                        <svg class="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400 shadow-lg" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                        </svg>
                    </div>
                    <input id="datepicker-range-start" name="start" type="text" style="width: 130px;" value="{{ from_date }}" class=" flex-grow bg-gray-50 border border-gray-300 dark:border-gray-600 mr-0.5 shadow-lg md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500 rounded md:rounded-lg" placeholder="Select start date">
                </div>
                <span class="text-gray-500" style="margin: 0px 5px 0px 5px;"> to </span>
                <div class="relative">
                    <input id="datepicker-range-end" name="end" type="text" style="width: 130px;" value="{{ to_date }}" class="date-picker flex-grow bg-gray-50 border border-gray-300 dark:border-gray-600 mr-0.5 shadow-lg md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500 rounded md:rounded-lg" placeholder="Select end date">
                </div>
                <button type="button" id="id_clearSearch" onclick="clearSearch('{{family_group_id}}')" title="Clear" class="{% if not from_date or not to_date %} hidden {% endif %} p-2.5 mb-2 md:mb-0 text-sm font-medium text-gray-600 bg-transparent border-none cursor-pointer focus:outline-none focus:ring-0 md:rounded-none">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 6l8 8M14 6l-8 8" />
                    </svg>
                    <span class="sr-only">Clear</span>
                </button>
                <button type="button" onclick="searchFamilyPhotoByDate('{{family_group_id}}')" class="shadow-lg p-2.5 text-sm font-medium text-white bg-blue-500 rounded-lg md:rounded-r-lg hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 border border-gray-300">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                    <span class="sr-only">Search</span>
                </button>
            </div>
        </div>
 
        <div class="relative w-full shadow-lg rounded-lg overflow-x-auto sm:rounded-lg">
            <div class="table-container">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                    <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                        <tr>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Created On' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Photo' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Score' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Delete' %}
                                </div>
                            </th>
                        </tr>
                    </thead>
     
                    <tbody>
                        {% if data %}
                            {% for dt in data %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td class="px-6 py-4">
                                    {{ dt.created_on }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        {% if dt.file %}
                                        <div class="flex">
                                            <img src="{{dt.file}}" class="image-style rounded border" alt="">
                                            <a href="{{dt.file}}" target="_blank" class="ml-2 cursor-pointer content-center" title="View Image">
                                                <svg class="w-6 h-6 text-blue-500 hover:text-blue-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"></path>
                                                    <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
                                                    <path stroke="none" d="M0 0h24v24H0z"></path>
                                                    <line x1="4" y1="7" x2="20" y2="7"></line>
                                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                                                    <path d="M9 7v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                                                </svg>
                                            </a>
                                        </div>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.score }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex">
                                        <button type="button" class="ml-2" onclick="deleteWeeklyFamilyPhoto('{{ dt.id }}')" title="Delete">
                                            <svg class="text-red-500 hover:text-red-600"  width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">  <path stroke="none" d="M0 0h24v24H0z"/>  <line x1="4" y1="7" x2="20" y2="7" />  <line x1="10" y1="11" x2="10" y2="17" />  <line x1="14" y1="11" x2="14" y2="17" />  <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12" />  <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3" /></svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="4" class="px-6 py-4 text-base">Data is not available.</td></tr>
                        {% endif %}
                    </tbody>           
                </table>
            </div>
            <nav class="flex flex-col bg-gray-50 md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4" aria-label="Table navigation">
                <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                    Showing
                    <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
                    of
                    <span class="font-semibold text-gray-900 dark:text-white">{{ total_items }}</span>
                </span>
                <ul class="inline-flex items-stretch -space-x-px">
                    {% if page_obj.has_previous %}
                    <li>
                        <a href="?page={{ page_obj.previous_page_number }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <span class="sr-only">Previous</span>
                            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                    {% endif %}
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li>
                            <a
                            class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                {{ num }}
                            </a>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %} <li>
                            <a href="?page={{ num }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}"
                            class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                {{ num }}
                            </a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    {% if page_obj.has_next %}
                    <li>
                        <a href="?page={{ page_obj.next_page_number }}&length={{ page_length }}&field={{ search_field }}&search={{ search_keyword }}&sort_by={{ sort_by }}&order={{ sort_order }}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <span class="sr-only">Next</span>
                            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        </div>
    </div>
    </div>

<script src="{% static 'family/js/family_photo.js'%}?v=0.2"></script>
 <script>
    $(document).ready(function() { 
        msg = '{{ msg }}';
        if (msg) {
            setTimeout(() => {
                $('#idFamilyPhotoMsg').hide();
            }, 10000);
        }

        var dropdown = document.getElementById('id_pageLength');
        var page_length = '{{ page_length }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === page_length) {
                dropdown.options[i].selected = true;
                break;
            }
        }
    });
 </script>
{% endblock content %}
 