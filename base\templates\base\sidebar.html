{% load i18n %}
{% load static %}

<style>
  /* .menu-link:hover .menu-icon {
      color: rgb(63 131 248 / var(--tw-text-opacity));
  } */
</style>

<div
    class="fixed top-0 left-0 z-40 w-64 h-screen transition-transform -translate-x-full bg-color-sidebar shadow-lg md:translate-x-0 dark:bg-gray-800 dark:border-gray-700"
    aria-label="Sidenav"
    id="drawer-navigation">
  <div style="padding: 25px;">
    <a href="/users/">
      <img src="{% static 'base\images\hku_family_link_logo.svg' %}" alt="">
    </a>
  </div>
  <div class="overflow-y-auto px-3 h-full bg-color-sidebar dark:bg-gray-800">
    <ul class="space-y-2">
      <li>
        <a
          href="/users/"
          class="menu-link flex items-center p-2 text-base border-solid border-blue-100 font-medium text-gray-900 rounded-lg dark:text-white hover:bg-blue-200 hover:border-l-4 hover:border-blue-600 dark:hover:bg-gray-700 hover:shadow-lg group"
        >
        <svg class="w-6 h-6 text-gray-800 dark:text-white menu-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M8 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4H6Zm7.25-2.095c.478-.86.75-1.85.75-2.905a5.973 5.973 0 0 0-.75-2.906 4 4 0 1 1 0 5.811ZM15.466 20c.34-.588.535-1.271.535-2v-1a5.978 5.978 0 0 0-1.528-4H18a4 4 0 0 1 4 4v1a2 2 0 0 1-2 2h-4.535Z" clip-rule="evenodd"/>
        </svg>
        
          <span class="ml-3">{% trans 'Users' %}</span>
        </a>
      </li>
      <li>
        <a
          href="/families/"
          class="menu-link flex items-center p-2 text-base border-solid border-blue-100 font-medium text-gray-900 rounded-lg dark:text-white hover:bg-blue-200 hover:border-l-4 hover:border-blue-600 dark:hover:bg-gray-700 hover:shadow-lg group"
        >
        <svg class="w-6 h-6 text-gray-800 dark:text-white menu-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M12 6a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Zm-1.5 8a4 4 0 0 0-4 4 2 2 0 0 0 2 2h7a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-3Zm6.82-3.096a5.51 5.51 0 0 0-2.797-6.293 3.5 3.5 0 1 1 2.796 6.292ZM19.5 18h.5a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-1.1a5.503 5.503 0 0 1-.471.762A5.998 5.998 0 0 1 19.5 18ZM4 7.5a3.5 3.5 0 0 1 5.477-2.889 5.5 5.5 0 0 0-2.796 6.293A3.501 3.501 0 0 1 4 7.5ZM7.1 12H6a4 4 0 0 0-4 4 2 2 0 0 0 2 2h.5a5.998 5.998 0 0 1 3.071-5.238A5.505 5.505 0 0 1 7.1 12Z" clip-rule="evenodd"/>
        </svg>
        
          <span class="ml-3">{% trans 'Families' %}</span>
        </a>
      </li>
      <li>
        <a
          href="/question-list/"
          class="menu-link flex items-center p-2 text-base border-solid border-blue-100 font-medium text-gray-900 rounded-lg dark:text-white hover:bg-blue-200 hover:border-l-4 hover:border-blue-600 dark:hover:bg-gray-700 hover:shadow-lg group"
        >
        <svg class="w-6 h-6 text-gray-800 dark:text-white menu-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm9.008-3.018a1.502 1.502 0 0 1 2.522 1.159v.024a1.44 1.44 0 0 1-1.493 1.418 1 1 0 0 0-1.037.999V14a1 1 0 1 0 2 0v-.539a3.44 3.44 0 0 0 2.529-3.256 3.502 3.502 0 0 0-7-.255 1 1 0 0 0 2 .076c.014-.398.187-.774.48-1.044Zm.982 7.026a1 1 0 1 0 0 2H12a1 1 0 1 0 0-2h-.01Z" clip-rule="evenodd"/>
        </svg>      
          <span class="ml-3">{% trans 'Questions' %}</span>
        </a>
      </li>
      <li>
        <button type="button" class="menu-link flex items-center w-full p-2 text-base border-solid border-blue-100 font-medium text-gray-900 rounded-lg dark:text-white hover:bg-blue-200 hover:border-l-4 hover:border-blue-600 dark:hover:bg-gray-700 hover:shadow-lg group" aria-controls="dropdown-example" data-collapse-toggle="dropdown-example">
          <svg class="w-6 h-6 text-gray-800 dark:text-white menu-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12" stroke="white" stroke-width="2" stroke-linecap="round"></line>
            <line x1="12" y1="8" x2="12" y2="8" stroke="white" stroke-width="2" stroke-linecap="round"></line>
          </svg>   
          <span class="flex-1 text-base font-medium  ms-3 text-left rtl:text-right whitespace-nowrap">Info</span>
          <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
          </svg>
        </button>
        <ul id="dropdown-example" class="py-2 space-y-2">
              <li>
                 <a href="/prize_info/" style="padding: 8px 8px 8px 44px;" class="menu-link flex items-center w-full text-base font-medium transition duration-75 rounded-lg border-solid border-blue-100 text-gray-900 dark:text-white hover:bg-blue-200 hover:border-l-4 hover:border-blue-600 dark:hover:bg-gray-700 hover:shadow-lg group">Prize Info</a>
              </li>
              <li>
                 <a href="/score_info/" style="padding: 8px 8px 8px 44px;" class="menu-link flex items-center w-full text-base font-medium transition duration-75 rounded-lg border-solid border-blue-100 text-gray-900 dark:text-white hover:bg-blue-200 hover:border-l-4 hover:border-blue-600 dark:hover:bg-gray-700 hover:shadow-lg group">Score Info</a>
              </li>
        </ul>
     </li>
      <li>
        <a
          href="/leaderboard/"
          class="menu-link flex items-center p-2 text-base border-solid border-blue-100 font-medium text-gray-900 rounded-lg dark:text-white hover:bg-blue-200 hover:border-l-4 hover:border-blue-600 dark:hover:bg-gray-700 hover:shadow-lg group"
        >
        <svg class="w-6 h-6 text-gray-800 dark:text-white menu-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path d="M11 9a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"/>
          <path fill-rule="evenodd" d="M9.896 3.051a2.681 2.681 0 0 1 4.208 0c.147.186.38.282.615.255a2.681 2.681 0 0 1 2.976 2.975.681.681 0 0 0 .254.615 2.681 2.681 0 0 1 0 4.208.682.682 0 0 0-.254.615 2.681 2.681 0 0 1-2.976 2.976.681.681 0 0 0-.615.254 2.682 2.682 0 0 1-4.208 0 .681.681 0 0 0-.614-.255 2.681 2.681 0 0 1-2.976-2.975.681.681 0 0 0-.255-.615 2.681 2.681 0 0 1 0-4.208.681.681 0 0 0 .255-.615 2.681 2.681 0 0 1 2.976-2.975.681.681 0 0 0 .614-.255ZM12 6a3 3 0 1 0 0 6 3 3 0 0 0 0-6Z" clip-rule="evenodd"/>
          <path d="M5.395 15.055 4.07 19a1 1 0 0 0 1.264 1.267l1.95-.65 1.144 1.707A1 1 0 0 0 10.2 21.1l1.12-3.18a4.641 4.641 0 0 1-2.515-1.208 4.667 4.667 0 0 1-3.411-1.656Zm7.269 2.867 1.12 3.177a1 1 0 0 0 1.773.224l1.144-1.707 1.95.65A1 1 0 0 0 19.915 19l-1.32-3.93a4.667 4.667 0 0 1-3.4 1.642 4.643 4.643 0 0 1-2.53 1.21Z"/>
        </svg>
              
          <span class="ml-3">{% trans 'Leaderboard' %}</span>
        </a>
      </li>
    </ul>
  </div>
</div>