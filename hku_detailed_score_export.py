#!/usr/bin/env python
"""
HKU Student Quiz - Detailed Score Export
导出详细的按日期和成员展开的分数记录
"""

import psycopg2
from datetime import datetime, timedelta
import csv
from collections import defaultdict

class HKUDetailedScoreExporter:
    """HKU详细分数导出器"""
    
    def __init__(self):
        self.db_config = {
            'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
            'port': 5432,
            'database': 'hku_staging',
            'user': 'postgres',
            'password': 'k9Z#x$Lh3&!V'
        }
        
        self.score_config = {
            'USER_DAILY_CHECKIN_SCORE': 100,
            'WEEKLY_FAMILY_PHOTO_SCORE': 100,
            'FOOD_SCREENSHOT_SCORE': 500,
            'STEPS_SCREENSHOT_SCORE': 500
        }
        
        self.conn = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            print("✅ 成功连接到HKU数据库!")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            print("🔌 数据库连接已断开")
    
    def execute_query(self, query, params=None):
        """执行SQL查询"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(query, params)
            columns = [desc[0] for desc in cursor.description]
            results = cursor.fetchall()
            cursor.close()
            
            return [dict(zip(columns, row)) for row in results]
        except Exception as e:
            print(f"❌ 查询执行失败: {e}")
            print(f"Query: {query}")
            return []
    
    def get_family_groups_with_members(self):
        """获取家庭组及其成员信息"""
        query = """
        SELECT 
            fg.id as family_group_id,
            fg.family_id,
            fg.family_name,
            fg.created_on as family_created_on,
            c.name as category_name,
            fm.member_id,
            au.username as member_username,
            au.first_name,
            au.last_name,
            fm.created_on as member_joined_date,
            fm.is_leader
        FROM family_familygroups fg
        JOIN family_category c ON fg.category_id = c.id
        JOIN family_familymembers fm ON fg.id = fm.family_group_id
        JOIN auth_user au ON fm.member_id = au.id
        WHERE fm.is_deleted = false 
        AND fm.is_member = true
        ORDER BY fg.family_name, au.username
        """
        return self.execute_query(query)
    
    def get_member_checkin_details(self, member_id, start_date=None, end_date=None):
        """获取成员签到详情"""
        date_filter = ""
        params = [member_id]
        
        if start_date and end_date:
            date_filter = "AND created_on::date BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        
        query = f"""
        SELECT 
            created_on::date as checkin_date,
            COUNT(*) as checkin_count
        FROM user_authentication_usercheckin
        WHERE user_id = %s
        {date_filter}
        GROUP BY created_on::date
        ORDER BY created_on::date
        """
        return self.execute_query(query, params)
    
    def get_member_quiz_scores(self, member_id, start_date=None, end_date=None):
        """获取成员测验分数详情"""
        date_filter = ""
        params = [member_id]
        
        if start_date and end_date:
            date_filter = "AND uq.date BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        
        query = f"""
        SELECT
            uq.date as quiz_date,
            q.quiz_type,
            SUM(CASE WHEN uq.is_correct THEN q.score ELSE 0 END) as daily_score,
            COUNT(*) as questions_answered
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        WHERE uq.user_id = %s
        AND uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        {date_filter}
        GROUP BY uq.date, q.quiz_type
        ORDER BY uq.date
        """
        return self.execute_query(query, params)
    
    def get_member_food_step_scores(self, member_id, start_date=None, end_date=None):
        """获取成员Food/Steps分数详情"""
        date_filter = ""
        params = [member_id]
        
        if start_date and end_date:
            date_filter = "AND created_at::date BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        
        query = f"""
        SELECT 
            created_at::date as score_date,
            SUM(score) as daily_food_step_score
        FROM question_userleaderboard
        WHERE user_id = %s
        AND type = 'Food-Step-SS'
        AND is_deleted = false
        {date_filter}
        GROUP BY created_at::date
        ORDER BY created_at::date
        """
        return self.execute_query(query, params)
    
    def get_family_photo_scores(self, family_group_id, start_date=None, end_date=None):
        """获取家庭照片分数详情"""
        date_filter = ""
        params = [family_group_id]
        
        if start_date and end_date:
            date_filter = "AND created_on::date BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        
        query = f"""
        SELECT 
            created_on::date as photo_date,
            SUM(score) as daily_photo_score,
            COUNT(*) as photo_count
        FROM family_weeklyuploadfile
        WHERE family_group_id = %s
        {date_filter}
        GROUP BY created_on::date
        ORDER BY created_on::date
        """
        return self.execute_query(query, params)
    
    def get_family_total_scores(self, family_group_id):
        """获取家庭组总分"""
        query = """
        SELECT 
            COALESCE(l.score, 0) as leaderboard_score,
            COALESCE(sl.score, 0) as steps_leaderboard_score
        FROM family_familygroups fg
        LEFT JOIN question_leaderboard l ON fg.id = l.family_group_id AND l.is_deleted = false
        LEFT JOIN question_stepsleaderboard sl ON fg.id = sl.family_group_id AND sl.is_deleted = false
        WHERE fg.id = %s
        """
        result = self.execute_query(query, (family_group_id,))
        if result:
            return result[0]
        return {'leaderboard_score': 0, 'steps_leaderboard_score': 0}
    
    def generate_detailed_export(self, start_date=None, end_date=None, days_back=30):
        """生成详细的分数导出数据"""
        if not self.connect():
            return None
        
        try:
            # 设置日期范围 - 使用更大的范围以包含所有数据
            if not start_date or not end_date:
                end_date = datetime.now().date()
                start_date = datetime(2025, 1, 1).date()  # 从2025年开始包含所有数据
            
            print(f"📅 导出日期范围: {start_date} 到 {end_date}")
            
            # 获取所有家庭组和成员
            family_members = self.get_family_groups_with_members()
            print(f"👥 找到 {len(family_members)} 个家庭成员记录")
            
            export_data = []
            
            # 按家庭组分组处理
            family_groups = defaultdict(list)
            for member in family_members:
                family_groups[member['family_group_id']].append(member)
            
            for family_group_id, members in family_groups.items():
                family_info = members[0]  # 获取家庭基本信息
                
                # 获取家庭照片分数
                family_photos = self.get_family_photo_scores(family_group_id, start_date, end_date)
                photo_scores_by_date = {p['photo_date']: p['daily_photo_score'] for p in family_photos}
                
                # 获取家庭总分
                family_totals = self.get_family_total_scores(family_group_id)
                
                # 为每个成员生成记录
                for member in members:
                    member_id = member['member_id']
                    
                    # 获取成员各项分数
                    checkins = self.get_member_checkin_details(member_id, start_date, end_date)
                    quiz_scores = self.get_member_quiz_scores(member_id, start_date, end_date)
                    food_step_scores = self.get_member_food_step_scores(member_id, start_date, end_date)
                    
                    # 按日期组织数据
                    checkin_by_date = {c['checkin_date']: c['checkin_count'] for c in checkins}
                    
                    quiz_by_date = defaultdict(lambda: {'regular': 0, 'bonus': 0})
                    for q in quiz_scores:
                        quiz_by_date[q['quiz_date']][q['quiz_type']] = q['daily_score']
                    
                    food_step_by_date = {f['score_date']: f['daily_food_step_score'] for f in food_step_scores}
                    
                    # 生成日期范围内的记录
                    current_date = start_date
                    while current_date <= end_date:
                        # 计算当日分数
                        daily_checkin = checkin_by_date.get(current_date, 0) * self.score_config['USER_DAILY_CHECKIN_SCORE']
                        daily_quiz = quiz_by_date[current_date]['regular']
                        daily_bonus = quiz_by_date[current_date]['bonus']
                        daily_food_step = food_step_by_date.get(current_date, 0)
                        daily_photo = photo_scores_by_date.get(current_date, 0)
                        
                        # 计算总分
                        daily_total = daily_checkin + daily_quiz + daily_bonus + daily_food_step
                        date_family_score = daily_total + daily_photo  # 包含家庭照片分数
                        
                        # 只导出有活动的日期
                        if daily_total > 0 or daily_photo > 0:
                            export_data.append({
                                'Date': current_date.strftime('%d-%b-%Y'),
                                'Family Group Name': family_info['family_name'],
                                'Family Member': member['member_username'],
                                'Checkin Score': daily_checkin,
                                'Quiz Score': daily_quiz,
                                'Bonus Question': daily_bonus,
                                'Total Score': daily_total,
                                'Date Family Group Score': date_family_score,
                                'Total Family Group Score': family_totals['leaderboard_score']
                            })
                        
                        current_date += timedelta(days=1)
            
            return export_data
            
        finally:
            self.disconnect()

    def generate_detailed_export_by_category(self, category_name, start_date=None, end_date=None, days_back=30):
        """按类别生成详细的分数导出数据"""
        if not self.connect():
            return None

        try:
            # 设置日期范围
            if not start_date or not end_date:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days_back)

            print(f"📅 导出{category_name}类别数据，日期范围: {start_date} 到 {end_date}")

            # 获取指定类别的家庭组和成员
            family_members = self.get_family_groups_with_members()
            category_members = [m for m in family_members if m['category_name'] == category_name]
            print(f"👥 找到 {len(category_members)} 个{category_name}类别家庭成员记录")

            # 统计家庭组数量
            unique_families = set(m['family_group_id'] for m in category_members)
            print(f"🏠 包含 {len(unique_families)} 个{category_name}类别家庭组")

            # 显示家庭组列表
            family_names = set(m['family_name'] for m in category_members)
            print(f"📋 {category_name}类别家庭组: {', '.join(sorted(family_names))}")

            export_data = []

            # 按家庭组分组处理
            family_groups = defaultdict(list)
            for member in category_members:
                family_groups[member['family_group_id']].append(member)

            for family_group_id, members in family_groups.items():
                family_info = members[0]  # 获取家庭基本信息

                # 获取家庭照片分数
                family_photos = self.get_family_photo_scores(family_group_id, start_date, end_date)
                photo_scores_by_date = {p['photo_date']: p['daily_photo_score'] for p in family_photos}

                # 获取家庭总分
                family_totals = self.get_family_total_scores(family_group_id)

                # 为每个成员生成记录
                for member in members:
                    member_id = member['member_id']

                    # 获取成员各项分数
                    checkins = self.get_member_checkin_details(member_id, start_date, end_date)
                    quiz_scores = self.get_member_quiz_scores(member_id, start_date, end_date)
                    food_step_scores = self.get_member_food_step_scores(member_id, start_date, end_date)

                    # 按日期组织数据
                    checkin_by_date = {c['checkin_date']: c['checkin_count'] for c in checkins}

                    quiz_by_date = defaultdict(lambda: {'regular': 0, 'bonus': 0})
                    for q in quiz_scores:
                        quiz_by_date[q['quiz_date']][q['quiz_type']] = q['daily_score']

                    food_step_by_date = {f['score_date']: f['daily_food_step_score'] for f in food_step_scores}

                    # 生成日期范围内的记录
                    current_date = start_date
                    while current_date <= end_date:
                        # 计算当日分数
                        daily_checkin = checkin_by_date.get(current_date, 0) * self.score_config['USER_DAILY_CHECKIN_SCORE']
                        daily_quiz = quiz_by_date[current_date]['regular']
                        daily_bonus = quiz_by_date[current_date]['bonus']
                        daily_food_step = food_step_by_date.get(current_date, 0)
                        daily_photo = photo_scores_by_date.get(current_date, 0)

                        # 计算总分
                        daily_total = daily_checkin + daily_quiz + daily_bonus + daily_food_step
                        date_family_score = daily_total + daily_photo  # 包含家庭照片分数

                        # 只导出有活动的日期
                        if daily_total > 0 or daily_photo > 0:
                            export_data.append({
                                'Date': current_date.strftime('%d-%b-%Y'),
                                'Category': category_name,
                                'Family Group Name': family_info['family_name'],
                                'Family Member': member['member_username'],
                                'Checkin Score': daily_checkin,
                                'Quiz Score': daily_quiz,
                                'Bonus Question': daily_bonus,
                                'Food/Steps Score': daily_food_step,
                                'Family Photo Score': daily_photo,
                                'Total Score': daily_total,
                                'Date Family Group Score': date_family_score,
                                'Total Family Group Score': family_totals['leaderboard_score']
                            })

                        current_date += timedelta(days=1)

            print(f"📊 生成了 {len(export_data)} 条{category_name}类别记录")
            return export_data

        finally:
            self.disconnect()

    def export_to_csv(self, data, filename=None):
        """导出数据到CSV"""
        if not filename:
            filename = f"hku_detailed_scores_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        if not data:
            print("❌ 没有数据可导出")
            return None
        
        # 检查数据是否包含Category字段
        has_category = data and 'Category' in data[0]

        if has_category:
            fieldnames = [
                'Date', 'Category', 'Family Group Name', 'Family Member', 'Checkin Score',
                'Quiz Score', 'Bonus Question', 'Food/Steps Score', 'Family Photo Score',
                'Total Score', 'Date Family Group Score', 'Total Family Group Score'
            ]
        else:
            fieldnames = [
                'Date', 'Family Group Name', 'Family Member', 'Checkin Score',
                'Quiz Score', 'Bonus Question', 'Total Score',
                'Date Family Group Score', 'Total Family Group Score'
            ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for row in data:
                writer.writerow(row)
        
        print(f"📊 CSV导出完成: {filename}")
        print(f"📝 导出记录数: {len(data)}")
        return filename


def main():
    """主函数"""
    print("🎓 HKU Student Quiz - Detailed Score Export")
    print("📊 按日期和成员展开的详细分数导出")
    print("=" * 60)
    
    try:
        exporter = HKUDetailedScoreExporter()
        
        # 询问导出类别
        print("\n📋 选择导出类别:")
        print("1. Diet (饮食类别)")
        print("2. Sports (运动类别)")
        print("3. 所有类别")

        category_choice = input("选择类别 (1-3): ").strip()

        # 询问导出范围
        print("\n📅 选择导出范围:")
        print("1. 最近30天")
        print("2. 最近7天")
        print("3. 自定义日期范围")

        choice = input("选择选项 (1-3): ").strip()

        start_date = None
        end_date = None
        days_back = 30

        if choice == '2':
            days_back = 7
        elif choice == '3':
            start_str = input("开始日期 (YYYY-MM-DD): ").strip()
            end_str = input("结束日期 (YYYY-MM-DD): ").strip()
            try:
                start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_str, '%Y-%m-%d').date()
            except ValueError:
                print("❌ 日期格式错误，使用默认30天")
                days_back = 30

        print("\n🔄 正在生成详细分数报告...")

        # 根据选择生成数据
        if category_choice == '1':
            data = exporter.generate_detailed_export_by_category('Food', start_date, end_date, days_back)
            filename_suffix = "_Diet"
        elif category_choice == '2':
            data = exporter.generate_detailed_export_by_category('Sports', start_date, end_date, days_back)
            filename_suffix = "_Exercise"
        else:
            # 生成两个分离的文件
            diet_data = exporter.generate_detailed_export_by_category('Food', start_date, end_date, days_back)
            exercise_data = exporter.generate_detailed_export_by_category('Sports', start_date, end_date, days_back)

            if diet_data:
                diet_filename = f"hku_detailed_scores_Diet_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                exporter.export_to_csv(diet_data, diet_filename)
                print(f"\n✅ Diet类别导出完成!")
                print(f"📁 文件: {diet_filename}")
                print(f"📊 记录数: {len(diet_data)}")

            if exercise_data:
                exercise_filename = f"hku_detailed_scores_Exercise_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                exporter.export_to_csv(exercise_data, exercise_filename)
                print(f"\n✅ Exercise类别导出完成!")
                print(f"📁 文件: {exercise_filename}")
                print(f"📊 记录数: {len(exercise_data)}")

            return

        if data:
            # 生成带类别后缀的文件名
            custom_filename = f"hku_detailed_scores{filename_suffix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            filename = exporter.export_to_csv(data, custom_filename)
            print(f"\n✅ 导出完成!")
            print(f"📁 文件: {filename}")
            print(f"📊 记录数: {len(data)}")

            # 显示样本数据
            if len(data) > 0:
                print(f"\n📋 样本数据 (前3条):")
                for i, row in enumerate(data[:3]):
                    category_info = f" | {row['Category']}" if 'Category' in row else ""
                    print(f"   {i+1}. {row['Date']}{category_info} | {row['Family Group Name']} | {row['Family Member']} | 总分: {row['Total Score']}")
        else:
            print("❌ 没有找到数据")
            
    except KeyboardInterrupt:
        print("\n⏹️  用户取消操作")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
