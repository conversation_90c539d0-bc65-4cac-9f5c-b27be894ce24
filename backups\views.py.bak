from django.shortcuts import render, get_object_or_404, redirect
from rest_framework.views import APIView
import traceback, random, json, os 
from django.utils import timezone
from datetime import datetime, timedelta
from user_authentication.models import UserProfile, PhoneNumOtp, User, User<PERSON><PERSON>ckin
from user_authentication.serializers import EditUserProfileSerializer
from rest_framework import status
from rest_framework.response import Response
from user_authentication.helpers import *
from base.views import create_from_exceptions
from django.contrib.auth.hashers import make_password
from base import utils
from django.contrib.auth import authenticate, logout, login
from django.http import HttpResponse, FileResponse
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.db.models import Q, Max
import pandas as pd
from family.models import FamilyGroups ,FamilyMembers
from django.db import transaction
from django.template.loader import render_to_string
from django.conf import settings
from base.service import send_mail
from .serializers import (
    LoginSerializer, SendOtpSerializer, VerifyOtpSerializer, ChangePasswordSerializer, UpdateUserProfileDetailSerializer,
    InviteFamilyMemberSerializer, CancelFamilyInvitationSerializer, CheckinSerializer, LogoutSerializer, 
    DeactivateMemberSerializer, MemberSendOtpSerializer
)
from rest_framework import viewsets
from rest_framework.decorators import action
from django.core.mail import EmailMultiAlternatives, get_connection
import time
from question.models import UserLeaderboard
from email.header import Header
from family.models import Category
import sendgrid
from sendgrid.helpers.mail import Mail, From, To, Bcc, Personalization, Email