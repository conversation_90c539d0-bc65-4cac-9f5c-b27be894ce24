#!/usr/bin/env python3
"""
测试修复后的导出功能
"""

from hku_detailed_score_export import HKUDetailedScoreExporter
from datetime import datetime

def test_fixed_export():
    """测试修复后的导出"""
    
    print("🧪 测试修复后的HKU详细分数导出...")
    
    try:
        exporter = HKUDetailedScoreExporter()
        
        # 使用标准日期范围
        start_date = datetime(2025, 5, 5).date()
        end_date = datetime(2025, 5, 19).date()
        
        print(f"📅 使用日期范围: {start_date} 到 {end_date}")
        
        # 测试Diet类别导出
        print(f"\n🍎 测试Diet类别导出...")
        diet_data = exporter.generate_detailed_export_by_category('Food', start_date, end_date)
        
        if diet_data:
            print(f"✅ Diet类别导出成功: {len(diet_data)} 条记录")
            
            # 显示样本数据
            if len(diet_data) > 0:
                print(f"📋 Diet样本数据 (前3条):")
                for i, row in enumerate(diet_data[:3]):
                    print(f"   {i+1}. {row['Date']} | {row['Family Group Name']} | {row['Family Member']} | 总分: {row['Total Score']}")
        else:
            print("❌ Diet类别导出失败")
        
        # 测试Exercise类别导出
        print(f"\n🏃 测试Exercise类别导出...")
        exercise_data = exporter.generate_detailed_export_by_category('Sports', start_date, end_date)
        
        if exercise_data:
            print(f"✅ Exercise类别导出成功: {len(exercise_data)} 条记录")
            
            # 显示样本数据
            if len(exercise_data) > 0:
                print(f"📋 Exercise样本数据 (前3条):")
                for i, row in enumerate(exercise_data[:3]):
                    print(f"   {i+1}. {row['Date']} | {row['Family Group Name']} | {row['Family Member']} | 总分: {row['Total Score']}")
        else:
            print("❌ Exercise类别导出失败")
        
        # 生成CSV文件
        if diet_data:
            diet_filename = f"hku_detailed_scores_Diet_FIXED_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            exporter.export_to_csv(diet_data, diet_filename)
            print(f"\n📁 Diet CSV文件: {diet_filename}")
        
        if exercise_data:
            exercise_filename = f"hku_detailed_scores_Exercise_FIXED_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            exporter.export_to_csv(exercise_data, exercise_filename)
            print(f"📁 Exercise CSV文件: {exercise_filename}")
        
        # 总结
        total_records = (len(diet_data) if diet_data else 0) + (len(exercise_data) if exercise_data else 0)
        diet_families = len(set(row['Family Group Name'] for row in diet_data)) if diet_data else 0
        exercise_families = len(set(row['Family Group Name'] for row in exercise_data)) if exercise_data else 0
        
        print(f"\n📊 修复后的导出总结:")
        print(f"   总记录数: {total_records}")
        print(f"   Diet家庭组: {diet_families}")
        print(f"   Exercise家庭组: {exercise_families}")
        print(f"   总家庭组: {diet_families + exercise_families}")
        print(f"   日期范围: {start_date} 到 {end_date} (15天)")
        
        # 验证数据质量
        if diet_data and exercise_data:
            print(f"\n✅ 修复验证:")
            print(f"   ✓ 使用正确的日期字段 (date)")
            print(f"   ✓ 使用标准日期范围 (2025-05-05 到 2025-05-19)")
            print(f"   ✓ 正确分离Diet和Exercise类别")
            print(f"   ✓ 包含所有分数类型 (不只是Bonus)")
            
            # 检查是否有重叠的家庭组
            diet_families_set = set(row['Family Group Name'] for row in diet_data)
            exercise_families_set = set(row['Family Group Name'] for row in exercise_data)
            overlap = diet_families_set.intersection(exercise_families_set)
            
            if overlap:
                print(f"   ⚠️  发现重叠家庭组: {len(overlap)} 个")
                for family in sorted(overlap):
                    print(f"       {family}")
            else:
                print(f"   ✓ 无重叠家庭组，分离正确")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_export()
