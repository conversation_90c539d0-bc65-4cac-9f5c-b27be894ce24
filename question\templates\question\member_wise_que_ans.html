{% extends "base/base.html" %}
{% load i18n %}
{% block title %}{% trans 'Member - Question Answer' %}{% endblock %}
{% block content %}
{% load static %}

{% if msg %}
<div id="idFamilyMemberMsg" class="p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 flex justify-between shadow-lg border-l-red-800 border-l-4" role="alert">
    <span class="font-medium">{{ msg }}</span>
    <span onclick="closeMsg('idFamilyMemberMsg')">
        <svg class="fill-current h-6 w-6 text-red-800" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
    </span>
</div>
{% endif %}
  
<div class="p-4">
    <div class="mb-3 flex">
        <h1 class="text-2xl font-bold mb-4">{% trans 'Member - Question Answer' %} </h1> <span class="text-2xl text-gray-500">&nbsp- <a href="/familymember/{{ family_group_id }}" class="text-blue-500 hover:text-blue-600 hover:underline">{{ family_group_name }} ( {{ family_group_category }} )</a> > {{ member_name }}</span>
    </div>
    <div class="mb-3 flex flex-wrap">
        <a href="?type=quiz" style="width: 160px;" class="min-w-[160px] p-1.5 mb-1 mr-1 rounded-lg text-center bg-gray-200 shadow-lg hover:bg-blue-200 hover:border-b-2 hover:border-blue-600 hover:font-medium {% if type != 'steps' %}tab-active{% endif %}">Quiz</a>
        <a href="?type=steps" style="width: 160px;" class="min-w-[160px] p-1.5 mb-1 mr-1 rounded-lg text-center bg-gray-200 shadow-lg hover:bg-blue-200 hover:border-b-2 hover:border-blue-600 hover:font-medium {% if type == 'steps' %}tab-active{% endif %}">Steps</a>
    </div>
    <div class="flex flex-col md:flex-row w-full mx-auto mb-3 justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
        <!-- Page length dropdown -->
        <div id="dropdown" class="flex items-center space-x-2 z-10 w-full md:w-auto">
            <span>Show</span>
            <select id="id_pageLength" name="page_length" onchange="changePageLength('{{user_id}}', '{{type}}')" class="shadow-lg w-fit bg-white hover:bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>

        <div id="date-range-picker" date-rangepicker class="flex items-center">
            <div class="relative flex">
                <div class="inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <svg class="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400 shadow-lg" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                    </svg>
                </div>
                <input id="datepicker-range-start" name="start" type="text" style="width: 130px;" value="{{ from_date }}" class="date-picker flex-grow bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 mr-0.5 shadow-lg md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500 rounded-lg" placeholder="Select start date">
            </div>
            <span class="text-gray-500" style="margin: 0px 5px 0px 5px;"> to </span>
            <div class="relative">
                <input id="datepicker-range-end" name="end" type="text" style="width: 130px;" value="{{ to_date }}" class="date-picker flex-grow bg-white hover:bg-gray-50 border border-gray-300 dark:border-gray-600 mr-0.5 shadow-lg md:w-auto mb-2 md:mb-0 text-sm text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500 rounded-lg" placeholder="Select end date">
            </div>
            <button type="button" id="id_clearSearch" onclick="clearSearch('{{user_id}}', '{{type}}')" title="Clear" class="{% if not from_date or not to_date %} hidden {% endif %} p-2.5 mb-2 md:mb-0 text-sm font-medium text-gray-600 bg-transparent border-none cursor-pointer focus:outline-none focus:ring-0 md:rounded-none">
                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 6l8 8M14 6l-8 8" />
                </svg>
                <span class="sr-only">Clear</span>
            </button>
            <button type="button" onclick="searchUsersQueAnsByDate('{{user_id}}', '{{type}}')" class="shadow-lg p-2.5 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 border border-gray-300">
                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                </svg>
                <span class="sr-only">Search</span>
            </button>
        </div>
    </div>

    <div class="relative w-full shadow-lg rounded-lg overflow-x-auto sm:rounded-lg">
        {% if type == "steps" %}
        <div class="table-container">
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                    {% if family_group_category == 'Sports' or family_group_category == 'sports' %}
                        <tr>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date }}&end_date={{ to_date }}{% endif %}{% if request.GET.sort_by == 'date' and request.GET.order == 'asc' %}&sort_by=date&order=desc{% else %}&sort_by=date&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                    {% trans 'Created On' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'date' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'date' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Total Weekly Steps' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'File' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Total Weekly Steps Score' %}
                                </div>
                            </th>
                        </tr>
                    {% else %}
                        <tr>
                            <th scope="col" class="px-6 py-3">
                                <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date }}&end_date={{ to_date }}{% endif %}{% if request.GET.sort_by == 'date' and request.GET.order == 'asc' %}&sort_by=date&order=desc{% else %}&sort_by=date&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                    {% trans 'Created On' %}
                                    <div class="ml-2 content-center">
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'date' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                        </svg>
                                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                            <path class="{% if request.GET.sort_by == 'date' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                        </svg>
                                    </div>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'File' %}
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                <div class="flex items-center">
                                    {% trans 'Upload File Type' %}
                                </div>
                            </th>
                        </tr>
                    {% endif %}
                </thead>

                <tbody>
                    {% if data %}
                        {% for dt in data %}
                            {% if family_group_category == 'Sports' or family_group_category == 'sports' %}
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4">
                                        {{ dt.created_on }}
                                    </td>
                                    <td class="px-6 py-4 flex">
                                        {{ dt.total_steps }}
                                        <!-- <input type="number" name="steps" id="id_steps_{{dt.user_id}}_{{dt.uploaded_on}}" value="{{ dt.steps }}" class="inputField bg-gray-200 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-1/5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" disabled /> -->
                                        {% if dt.total_steps > 0 %}
                                        <div class="ml-2 cursor-pointer content-center" id="id_editUserSteps_{{dt.id}}" onclick="editUserSteps('{{dt.id}}')" title="Edit Steps">
                                            <svg class="text-blue-500 hover:text-blue-600" width="20"  height="20"  viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round">  
                                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                                            </svg>                                         
                                        </div>
                                        {% endif %}
                                        <!-- <div class="ml-2 cursor-pointer content-center hidden" id="id_saveUserSteps_{{dt.user_id}}_{{dt.uploaded_on}}" onclick="saveUserSteps('{{dt.user_id}}', '{{dt.uploaded_on}}')">
                                            <svg class="w-6 h-6 text-white dark:text-white bg-green-500 hover:bg-green-600 rounded" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 11.917 9.724 16.5 19 7.5"/>
                                            </svg> 
                                        </div> -->
                                    </td>
                                    <td class="px-6 py-4">
                                    {% if dt.file %}
                                        <div class="flex">
                                            <img src="{{dt.file}}" class="image-style rounded border" alt="">
                                            <a href="{{dt.file}}" target="_blank" class="ml-2 cursor-pointer content-center" title="View Image">
                                                <svg class="w-6 h-6 text-blue-500 hover:text-blue-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"></path>
                                                    <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
                                                    <path stroke="none" d="M0 0h24v24H0z"></path>
                                                    <line x1="4" y1="7" x2="20" y2="7"></line>
                                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                                                    <path d="M9 7v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                                                </svg>
                                            </a> 
                                        </div>
                                        </td>
                                    {% endif %}
                                    <td class="px-6 py-4">
                                        {{ dt.steps_score }}
                                    </td>
                                </tr>
                            {% else %}
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4">
                                        {{ dt.created_on }}
                                    </td>
                                    <td class="px-6 py-4">
                                    {% if dt.file %}
                                        <div class="flex">
                                            <img src="{{dt.file}}" class="image-style rounded border" alt="">
                                            <a href="{{dt.file}}" target="_blank" class="ml-2 cursor-pointer content-center" title="View Image">
                                                <svg class="w-6 h-6 text-blue-500 hover:text-blue-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"></path>
                                                    <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
                                                    <path stroke="none" d="M0 0h24v24H0z"></path>
                                                    <line x1="4" y1="7" x2="20" y2="7"></line>
                                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                                                    <path d="M9 7v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                                                </svg>
                                            </a> 
                                        </div>
                                        </td>
                                    {% endif %}
                                    <td class="px-6 py-4">
                                        {{ dt.upload_type }}
                                    </td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        {% if family_group_category == 'Sports' or family_group_category == 'sports' %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="4" class="px-6 py-4 text-base">Data is not available.</td></tr>
                        {% else %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="3" class="px-6 py-4 text-base">Data is not available.</td></tr>
                        {% endif %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="table-container">
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 table">
                <thead class="text-gray-700 bg-blue-200 dark:bg-gray-700 dark:text-gray-400 thead">
                    <tr>
                        <th scope="col" class="px-6 py-3">
                            <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if request.GET.sort_by == 'date' and request.GET.order == 'asc' %}&sort_by=date&order=desc{% else %}&sort_by=date&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                {% trans 'Created On' %}
                                <div class="ml-2 content-center">
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'date' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                    </svg>
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'date' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                    </svg>
                                </div>
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if request.GET.sort_by == 'category' and request.GET.order == 'asc' %}&sort_by=category&order=desc{% else %}&sort_by=category&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                {% trans 'Category' %}
                                <div class="ml-2 content-center">
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'category' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                    </svg>
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'category' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                    </svg>
                                </div>
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if request.GET.sort_by == 'quiz_type' and request.GET.order == 'asc' %}&sort_by=quiz_type&order=desc{% else %}&sort_by=quiz_type&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                {% trans 'Quiz Type' %}
                                <div class="ml-2 content-center">
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'quiz_type' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                    </svg>
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'quiz_type' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                    </svg>
                                </div>
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Question' %}
                            </div>
                        </th>
                        <!-- <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Question Type' %}
                            </div>
                        </th> 
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Option Type' %}
                            </div>
                        </th> -->
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Correct Answer' %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Given Answer' %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <a class="flex" href="?page={% if page_number %}{{ page_number }}{% else %}1{% endif %}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if request.GET.sort_by == 'is_correct' and request.GET.order == 'asc' %}&sort_by=is_correct&order=desc{% else %}&sort_by=is_correct&order=asc{% endif %}{% if type %}&type={{ type }}{% endif %}">
                                {% trans 'Is Correct' %}
                                <div class="ml-2 content-center">
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'is_correct' and request.GET.order == 'asc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M9.207 1A2 2 0 0 0 6.38 1L.793 6.586A2 2 0 0 0 2.207 10H13.38a2 2 0 0 0 1.414-3.414L9.207 1Z"/>
                                    </svg>
                                    <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 10">
                                        <path class="{% if request.GET.sort_by == 'is_correct' and request.GET.order == 'desc' %} text-blue-500 {% else %} text-gray-500 {% endif %}" d="M15.434 1.235A2 2 0 0 0 13.586 0H2.414A2 2 0 0 0 1 3.414L6.586 9a2 2 0 0 0 2.828 0L15 3.414a2 2 0 0 0 .434-2.179Z"/>
                                    </svg>
                                </div>
                            </a>
                        </th>
                        <!-- <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Explanation' %}
                            </div>
                        </th> -->
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center">
                                {% trans 'Score' %}
                            </div>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    {% if data %}
                        {% for dt in data %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td class="px-6 py-4">
                                    {{ dt.date }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.question__category }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.question__quiz_type }}
                                </td>
                                <td class="px-6 py-4">
                                    {% if dt.que_image %}
                                        <img src="{{dt.que_image}}" class="image-style rounded border" alt="">
                                    {% else %}
                                    {{ dt.question__question }}
                                    {% endif %}
                                </td>
                                <!-- <td class="px-6 py-4">
                                    {{ dt.question__type }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ dt.question__option_type }}
                                </td> -->
                                <td class="px-6 py-4">
                                    {% if dt.question__option_type == 'image_option' %}
                                        <img src="{{dt.question__answer}}" class="image-style rounded border" alt="">
                                    {% else %}
                                    {{ dt.question__answer }}
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    {% if dt.user_answer and dt.is_user_ans_img %}
                                        <img src="{{dt.user_answer}}" class="image-style rounded border" alt="">
                                    {% else %}
                                    {{ dt.user_answer }}
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    <div {% if dt.is_correct == True %} class="w-fit pl-2 pr-2 bg-green-100 text-green-900 rounded-md" {% else %} class="w-fit pl-2 pr-2 bg-red-100 text-red-900 rounded-md" {% endif %}>
                                        {{ dt.is_correct }}
                                    </div>
                                </td>
                                <!-- <td class="px-6 py-4">
                                    {{ dt.question__explanation }}
                                </td> -->
                                <td class="px-6 py-4">
                                    {{ dt.question__score }}
                                </td>
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-center"><td colspan="8" class="px-6 py-4 text-base">Data is not available.</td></tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        {% endif %}
        <nav class="flex flex-col bg-gray-50 md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4" aria-label="Table navigation">
            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                Showing
                <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
                of
                <span class="font-semibold text-gray-900 dark:text-white">{{ total_items }}</span>
            </span>
            <ul class="inline-flex items-stretch -space-x-px">
                {% if page_obj.has_previous %}
                <li>
                    <a href="?page={{ page_obj.previous_page_number }}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if sort_by and sort_order %}&sort_by={{ sort_by }}&order={{ sort_order }}{% endif %}{% if type %}&type={{ type }}{% endif %}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <span class="sr-only">Previous</span>
                        <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </li>
                {% endif %}
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li>
                        <a
                        class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            {{ num }}
                        </a>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %} <li>
                        <a href="?page={{ num }}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if sort_by and sort_order %}&sort_by={{ sort_by }}&order={{ sort_order }}{% endif %}{% if type %}&type={{ type }}{% endif %}"
                        class="flex shadow-lg items-center justify-center text-sm py-2 px-3 leading-tight {% if page_number|stringformat:'s' == num|stringformat:'s' %} bg-blue-200 text-gray-900 {% else %} bg-white text-gray-500 {% endif %} border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            {{ num }}
                        </a>
                    </li>
                    {% endif %}
                {% endfor %}
                {% if page_obj.has_next %}
                <li>
                    <a href="?page={{ page_obj.next_page_number }}&length={{ page_length }}{% if from_date and to_date %}&start_date={{ from_date}}&end_date={{ to_date }}{% endif %}{% if sort_by and sort_order %}&sort_by={{ sort_by }}&order={{ sort_order }}{% endif %}{% if type %}&type={{ type }}{% endif %}" class="flex shadow-lg items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <span class="sr-only">Next</span>
                        <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>

    <!-- User Steps Modal toggle -->
    <button id="userStepsStaticModal" data-modal-target="user-Steps-static-modal" data-modal-toggle="user-Steps-static-modal" class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" type="button">
        Toggle modal
    </button>

    <!-- User Steps Main modal -->
    <div id="user-Steps-static-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-baseline w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <div class="flex">
                        <h3 id="userStepsModalTitle" class="text-xl font-semibold text-gray-900 dark:text-white"></h3>
                        <span id="id_user_full_name" class="text-xl text-gray-500"></span>
                        <span id="id_user_total_steps" class="text-xl text-gray-900"></span>
                    </div>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="user-Steps-static-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div id="userStepsModalBody" class="p-4 md:p-5 space-y-4 flex flex-col items-center justify-center">
                </div>
                <div id="idUserStepsModalMsg" class="mb-4 mt-4 ml-5 mr-5 hidden">
                </div>
                <!-- Modal footer -->
                <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600 justify-end">
                    <button id="modalUserStepsConfirmBtn" type="button" onclick="saveUserStepsData()" class="text-white bg-blue-500 hover:bg-blue-600 border-gray-300 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 shadow-lg">{% trans 'Save' %}</button>
                    <button id="modalUserStepsCloseBtn" data-modal-hide="user-Steps-static-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-primary-300 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600 shadow-lg">
                        {% trans 'Close' %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{% static 'question/js/member_wise_que_ans.js'%}?v=0.3"></script>
<script>
    window.addEventListener('pageshow', function(event) {
        if (!window.location.search.includes('start_date') && !window.location.search.includes('end_date')) {
            $('#datepicker-range-start').val('');
            $('#datepicker-range-end').val('');
        }
    });
    $(document).ready(function() { 
        msg = '{{ msg }}';
        if (msg) {
            setTimeout(() => {
                $('#idFamilyMemberMsg').hide();
            }, 10000);
        }

        var dropdown = document.getElementById('id_pageLength');
        var page_length = '{{ page_length }}';
        
        for (var i = 0; i < dropdown.options.length; i++) {
            if (dropdown.options[i].value === page_length) {
                dropdown.options[i].selected = true;
                break;
            }
        }
    })
</script>
{% endblock content %}
