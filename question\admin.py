from django.contrib import admin
from .models import (
    Question, AnswerText, AnswerImage, QuestionImage, OptionImages, UserQuestion, QuizStep, Leaderboard, Info ,StepsLeaderboard,
    UserLeaderboard, UserSteps, UserStepImage, QuizDates, UserFoodStepImage
)
from user_authentication.models import UserProfile

# Register your models here.




@admin.register(Question)
class AdminQuestion(admin.ModelAdmin):
    list_display = ['id', 'question', 'option_type', 'quiz_type', 'category', 'is_deleted']
    readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted']

admin.site.register(AnswerText, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])
admin.site.register(AnswerImage, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])
admin.site.register(QuestionImage, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])
admin.site.register(OptionImages, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])
admin.site.register(StepsLeaderboard, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])
admin.site.register(QuizDates, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])

admin.site.register(UserLeaderboard, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])


@admin.register(UserQuestion)
class AdminUserQuestion(admin.ModelAdmin):
    list_display = ['id', 'question_id', 'date', 'user','is_correct', 'answered', 'is_deleted']
    readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted']

admin.site.register(Info, readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted'])

@admin.register(QuizStep)
class AdminQuizStepModel(admin.ModelAdmin):
    list_display = ['id', 'week_start_date', 'is_deleted']
    readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted']

@admin.register(UserSteps)
class AdminUserSteps(admin.ModelAdmin):
    list_display = ['id',  'is_deleted']
    readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted']

@admin.register(UserStepImage)
class AdminUserStepImage(admin.ModelAdmin):
    list_display = ['id',  'is_deleted']
    readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted']

@admin.register(Leaderboard)
class AdminLeaderboardModel(admin.ModelAdmin):
    list_display = ['id', 'created_by','updated_by','family_group','category','score']
    readonly_fields = ['created_at','updated_at','created_by', 'updated_by', 'is_deleted']
    
@admin.register(UserFoodStepImage)
class AdminUserFoodStepImageModel(admin.ModelAdmin):
    list_display = ['created_at', 'updated_at', 'user', 'get_family_group', 'image', 'upload_type']

    def get_family_group(self, obj):
        # Get family group from UserProfile
        try:
            profile = UserProfile.objects.get(user=obj.user)
            return profile.family_group or '-'
        except UserProfile.DoesNotExist:
            return '-'
    get_family_group.short_description = 'Family Group'