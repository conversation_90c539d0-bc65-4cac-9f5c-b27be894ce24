#!/usr/bin/env python3
"""
快速检查HKU数据库中答题日期分布
"""

import psycopg2
from datetime import datetime

def check_answer_dates():
    """检查答题日期分布"""

    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }

    try:
        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        # 首先检查表结构
        print("🔍 检查相关表结构:")

        # 检查question_questionanswer表
        table_check_query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name LIKE '%question%'
        ORDER BY table_name
        """
        cursor.execute(table_check_query)
        tables = cursor.fetchall()
        print("   Question相关表:")
        for table in tables:
            print(f"     {table[0]}")

        # 检查question_userquestion表的字段
        print("\n   question_userquestion表字段:")
        columns_query = """
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'question_userquestion'
        ORDER BY ordinal_position
        """
        cursor.execute(columns_query)
        columns = cursor.fetchall()
        for col_name, col_type in columns:
            print(f"     {col_name}: {col_type}")

        # 检查question_question表的字段
        print("\n   question_question表字段:")
        columns_query2 = """
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'question_question'
        ORDER BY ordinal_position
        """
        cursor.execute(columns_query2)
        columns2 = cursor.fetchall()
        for col_name, col_type in columns2:
            print(f"     {col_name}: {col_type}")

        # 使用正确的表名重新查询
        print(f"\n🏃 运动类别答题日期分布 (使用question_userquestion):")
        sports_query = """
        SELECT
            uq.created_at::date as answer_date,
            COUNT(*) as answer_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND q.category IN ('Sports', 'Walk')
        GROUP BY uq.created_at::date
        ORDER BY uq.created_at::date
        """

        cursor.execute(sports_query)
        sports_results = cursor.fetchall()

        for date, count in sports_results:
            print(f"   {date}: {count} 条")

        print(f"\n🍎 饮食类别答题日期分布:")
        food_query = """
        SELECT
            uq.created_at::date as answer_date,
            COUNT(*) as answer_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        AND q.category = 'Food'
        GROUP BY uq.created_at::date
        ORDER BY uq.created_at::date
        LIMIT 20
        """

        cursor.execute(food_query)
        food_results = cursor.fetchall()

        for date, count in food_results:
            print(f"   {date}: {count} 条")

        if len(food_results) == 20:
            print("   ... (更多日期)")

        # 检查总体日期范围
        print(f"\n📅 总体答题日期范围:")
        range_query = """
        SELECT
            MIN(uq.created_at::date) as min_date,
            MAX(uq.created_at::date) as max_date,
            COUNT(DISTINCT uq.created_at::date) as total_days
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        """

        cursor.execute(range_query)
        min_date, max_date, total_days = cursor.fetchone()

        print(f"   最早日期: {min_date}")
        print(f"   最晚日期: {max_date}")
        print(f"   总天数: {total_days} 天")

        # 检查date字段的分布
        print(f"\n📅 检查question_userquestion.date字段的分布:")
        date_field_query = """
        SELECT
            uq.date as question_date,
            COUNT(*) as answer_count
        FROM question_userquestion uq
        JOIN question_question q ON uq.question_id = q.id
        WHERE uq.answered = true
        AND uq.is_deleted = false
        AND q.is_deleted = false
        GROUP BY uq.date
        ORDER BY uq.date
        """

        cursor.execute(date_field_query)
        date_results = cursor.fetchall()

        for date, count in date_results:
            print(f"   {date}: {count} 条")

        # 检查是否有更多的答题数据在其他表中
        print(f"\n🔍 检查是否有其他答题相关表:")

        # 检查是否有其他可能的答题表
        other_tables_query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND (table_name LIKE '%answer%' OR table_name LIKE '%quiz%' OR table_name LIKE '%response%')
        ORDER BY table_name
        """
        cursor.execute(other_tables_query)
        other_tables = cursor.fetchall()
        for table in other_tables:
            print(f"   {table[0]}")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")

if __name__ == "__main__":
    check_answer_dates()
