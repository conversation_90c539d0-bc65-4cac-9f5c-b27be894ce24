#!/usr/bin/env python3
"""
测试Diet和Exercise类别分离
"""

import psycopg2

def test_category_separation():
    """测试类别分离"""
    
    # 数据库连接配置
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }
    
    try:
        print("🔍 测试Diet和Exercise类别分离...")
        
        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查family_category表
        print("\n📋 检查family_category表:")
        category_query = """
        SELECT id, name, description
        FROM family_category
        ORDER BY name
        """
        cursor.execute(category_query)
        categories = cursor.fetchall()
        
        for cat_id, name, desc in categories:
            print(f"   ID: {cat_id}, Name: {name}, Description: {desc}")
        
        # 检查家庭组的类别分布
        print(f"\n🏠 家庭组类别分布:")
        family_category_query = """
        SELECT 
            c.name as category_name,
            COUNT(fg.id) as family_count,
            STRING_AGG(fg.family_name, ', ' ORDER BY fg.family_name) as family_names
        FROM family_familygroups fg
        JOIN family_category c ON fg.category_id = c.id
        GROUP BY c.name
        ORDER BY c.name
        """
        
        cursor.execute(family_category_query)
        family_categories = cursor.fetchall()
        
        for category, count, names in family_categories:
            print(f"\n   {category}类别: {count}个家庭组")
            print(f"     家庭组: {names}")
        
        # 检查成员数量
        print(f"\n👥 成员数量分布:")
        member_category_query = """
        SELECT 
            c.name as category_name,
            COUNT(fm.member_id) as member_count
        FROM family_familygroups fg
        JOIN family_category c ON fg.category_id = c.id
        JOIN family_familymembers fm ON fg.id = fm.family_group_id
        WHERE fm.is_deleted = false 
        AND fm.is_member = true
        GROUP BY c.name
        ORDER BY c.name
        """
        
        cursor.execute(member_category_query)
        member_categories = cursor.fetchall()
        
        for category, count in member_categories:
            print(f"   {category}类别: {count}个成员")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    test_category_separation()
