// function deleteFamily(user_id) {
//     var msg = "Are you sure you want to delete this item?";
//     showConfirmationDialogue(msg, function (is_confirm) {
//         if (is_confirm) {
//             var formData = new FormData();
//             formData.append('family_id', family_id);
//             formData.append('csrfmiddlewaretoken', $("input[name=csrfmiddlewaretoken]").val());
//             axios({
//                 method: "post",
//                 url: "/delete-families/",
//                 data: formData,
//                 headers: { "Content-Type": "multipart/form-data" },
//             })
//             .then(function (response) {
//                 if (response.data.code == 0) {
//                     $('#cancelBtnClick').click();
//                     showMessage('appMsg', 'danger', response.data.msg, 10);
//                 } else {
//                     window.location.href = "/families/";
//                 }
//             })
//             .catch(function (response) {
//                 showMessage('appMsg', 'danger', 'Something went wrong!', 5);
//             });
//         }
//     })
// }

function searchFamily() {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    var selectedLength = $('#id_pageLength').val();
    var page_length = 10;
    if (selectedLength) {
        page_length = selectedLength;
    }
    if (selected_field && search_keyword) {
        window.location = "/families/?page=1&length=" + page_length + "&field=" + selected_field + "&search=" + search_keyword;
    } else {
        window.location = "/families/?page=1&length=" + page_length;
    }
}

$('#searchKeyword').on("keyup", function (e) {
    if (e.keyCode == 13) {
        e.preventDefault();
        searchFamily();
    }
})

function changePageLength() {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    var selectedLength = $('#id_pageLength').val();
    if (selectedLength) {
        window.location = "/families/?page=1&length=" + selectedLength + "&field=" + selected_field + "&search=" + search_keyword;
    } else {
        window.location = "/families/?page=1&length=10";
    }
}

function clearSearch () {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    if (selected_field || search_keyword) {
        var selectedLength = $('#id_pageLength').val();
        var page_length = 10;
        if (selectedLength) {
            page_length = selectedLength;
        }
        window.location = "/families/?page=1&length=" + page_length;
    }
}

$('#searchKeyword').keyup(function () {
    var search_keyword = $('#searchKeyword').val();
    if (search_keyword) {
        $('#id_clearSearch').show();
    } else {
        $('#id_clearSearch').hide();
    }
})

