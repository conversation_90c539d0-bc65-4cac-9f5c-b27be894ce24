#!/usr/bin/env python
"""
检查特定家庭组的分数构成
"""

import psycopg2

def check_family_scores():
    """检查魚群711的分数构成"""
    db_config = {
        'host': 'hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com',
        'port': 5432,
        'database': 'hku_staging',
        'user': 'postgres',
        'password': 'k9Z#x$Lh3&!V'
    }
    
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 查找魚群711的家庭组ID
        cursor.execute("""
            SELECT id, family_name 
            FROM family_familygroups 
            WHERE family_name LIKE '%魚群%'
        """)
        families = cursor.fetchall()
        
        print("🔍 找到的家庭组:")
        for family_id, family_name in families:
            print(f"   ID: {family_id}, Name: {family_name}")
            
            # 检查leaderboard分数
            cursor.execute("""
                SELECT score 
                FROM question_leaderboard 
                WHERE family_group_id = %s AND is_deleted = false
            """, (family_id,))
            leaderboard_result = cursor.fetchone()
            leaderboard_score = leaderboard_result[0] if leaderboard_result else 0
            
            # 检查steps leaderboard分数
            cursor.execute("""
                SELECT score 
                FROM question_stepsleaderboard 
                WHERE family_group_id = %s AND is_deleted = false
            """, (family_id,))
            steps_result = cursor.fetchone()
            steps_score = steps_result[0] if steps_result else 0
            
            total = leaderboard_score + steps_score
            
            print(f"   📊 分数构成:")
            print(f"      - Leaderboard Score: {leaderboard_score}")
            print(f"      - Steps Leaderboard Score: {steps_score}")
            print(f"      - Total: {total}")
            print()
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    check_family_scores()
