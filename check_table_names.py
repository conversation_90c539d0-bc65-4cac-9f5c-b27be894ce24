#!/usr/bin/env python3
"""
检查数据库中的表名
"""

from hku_local_config import HKU_LOCAL_CONFIG
import psycopg2

def check_tables():
    """检查数据库表"""
    
    try:
        conn = psycopg2.connect(**HKU_LOCAL_CONFIG)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        
        print(f"📋 数据库中的所有表 ({len(tables)}个):")
        
        # 查找包含特定关键词的表
        keywords = ['question', 'step', 'family', 'auth', 'user']
        
        for keyword in keywords:
            print(f"\n🔍 包含'{keyword}'的表:")
            matching_tables = [table[0] for table in tables if keyword.lower() in table[0].lower()]
            
            for table in matching_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table}: {count} 条记录")
                except Exception as e:
                    print(f"   ❌ {table}: 错误 - {e}")
        
        # 特别检查步数相关的表
        print(f"\n🔍 查找步数相关的表:")
        step_tables = [table[0] for table in tables if 'step' in table[0].lower()]
        
        if step_tables:
            for table in step_tables:
                print(f"   📊 {table}")
                try:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 1;")
                    sample = cursor.fetchone()
                    if sample:
                        cursor.execute(f"""
                            SELECT column_name, data_type 
                            FROM information_schema.columns 
                            WHERE table_name = '{table}'
                            ORDER BY ordinal_position;
                        """)
                        columns = cursor.fetchall()
                        print(f"     字段: {', '.join([f'{col[0]}({col[1]})' for col in columns])}")
                except Exception as e:
                    print(f"     错误: {e}")
        else:
            print(f"   ❌ 没有找到步数相关的表")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    check_tables()
