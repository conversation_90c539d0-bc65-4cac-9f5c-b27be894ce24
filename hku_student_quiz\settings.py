"""
Django settings for hku_student_quiz project.

Generated by 'django-admin startproject' using Django 5.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
from decouple import config
from datetime import timedelta, datetime, date
import boto3
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-2)ik@b8fxg%6ee_o^27df8ws=380*jr!qupd(ps2wkzbb@8q)e'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG')

ALLOWED_HOSTS = ["*"]

CORS_ORIGIN_ALLOW_ALL = True

CORS_ALLOW_HEADERS = [
'accept',
'accept-encoding',
'authorization',
'content-type',
'dnt',
'origin',
'user-agent',
'x-csrftoken',
'x-requested-with',
]
CSRF_TRUSTED_ORIGINS = [
    'https://backend.healthyfamily-jcsfl.com',
    'https://healthyfamily-jcsfl.com',
]

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'compressor',
    'drf_yasg',
    'rest_framework',
    'rest_framework_simplejwt',
    # 'rest_framework_swagger',
    'corsheaders',
    'base',
    'user_authentication',
    'family',
    'question',
    'fontawesomefree',
    'tinymce',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'base.custom_middleware.LoginRequiredMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
}

ROOT_URLCONF = 'hku_student_quiz.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'hku_student_quiz.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE'  : 'django.db.backends.postgresql', 
        'NAME'    : config('DB_NAME'),
        'USER'    : config('DB_USERNAME'),
        'PASSWORD': config('DB_PASS'),
        'HOST'    : config('DB_HOST'),
        'PORT'    : config('DB_PORT'),
    }, 
}


DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

# TIME_ZONE = 'UTC'
TIME_ZONE = 'Asia/Hong_Kong'

USE_I18N = True

USE_TZ = True

if config("DEBUG", cast=bool) == True:
    #############################################################
    # SRC: https://devcenter.heroku.com/articles/django-assets

    # Static files (CSS, JavaScript, Images)
    # https://docs.djangoproject.com/en/1.9/howto/static-files/
    # Static files (CSS, JavaScript, Images)
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    STATIC_URL = '/static/'

    # Extra places for collectstatic to find static files.
    STATICFILES_DIRS = (
        os.path.join(BASE_DIR, 'static'),
    )

    MEDIA_ROOT = os.path.join(BASE_DIR, 'media/')
    MEDIA_URL = '/media/'

    ############################################################
    #TinyMce settings
    ############################################################
    TINYMCE_JS_URL = os.path.join(STATIC_URL, "tinymce/tinymce.min.js")
    TINYMCE_COMPRESSOR = False
    #############################################################
    # Compression settings
    #############################################################
    COMPRESS_ROOT = os.path.join(BASE_DIR, 'static')
    COMPRESS_ENABLED = True
    #############################################################
else:
    #############################################################
    # Working S3 settings # comment above code for S3
    #############################################################
    AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY')

    # Bucket for static files
    STATIC_BUCKET_NAME = config('AWS_STATIC_STORAGE_BUCKET_NAME')

    # Bucket for media files
    MEDIA_BUCKET_NAME = config('AWS_MEDIA_STORAGE_BUCKET_NAME')

    AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME')
    AWS_S3_SIGNATURE_VERSION = config('AWS_S3_SIGNATURE_VERSION')
    AWS_S3_VERIFY = True
    AWS_DEFAULT_ACL = None

    STATICFILES_DIRS = (
        os.path.join(BASE_DIR, 'static'),
    )

    if STATIC_BUCKET_NAME:
        STATIC_LOCATION = 'static'
        AWS_S3_OBJECT_PARAMETERS = {'CacheControl': 'max-age=86400'}
        STATIC_CLOUDFRONT_DOMAIN = config('AWS_STATIC_S3_CUSTOM_DOMAIN')
        # Static files settings
        STATIC_URL = f"https://{STATIC_CLOUDFRONT_DOMAIN}/"
        STATICFILES_STORAGE = 'utils.storage_backends.StaticStorage' 

    if MEDIA_BUCKET_NAME:
        PUBLIC_MEDIA_LOCATION = 'media'
        AWS_S3_OBJECT_PARAMETERS = {'CacheControl': 'max-age=86400'}
        MEDIA_CLOUDFRONT_DOMAIN = config('AWS_MEDIA_S3_CUSTOM_DOMAIN')
        # Media files settings
        MEDIA_URL = f"https://{MEDIA_CLOUDFRONT_DOMAIN}/"
        DEFAULT_FILE_STORAGE = 'utils.storage_backends.MediaStorage'

    ############################################################
    #TinyMce settings
    ############################################################
    TINYMCE_JS_URL = os.path.join(STATIC_URL, "tinymce/tinymce.min.js")
    TINYMCE_COMPRESSOR = True
    #############################################################
    # Compression settings
    #############################################################
    COMPRESS_ROOT = STATIC_LOCATION  # Ensure COMPRESS_ROOT is set
    COMPRESS_ENABLED = False
    COMPRESS_STORAGE = STATICFILES_STORAGE
    #############################################################

# Additional settings
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
]

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY')

# Bucket for static files
STATIC_BUCKET_NAME = config('AWS_STATIC_STORAGE_BUCKET_NAME')

# Bucket for media files
MEDIA_BUCKET_NAME = config('AWS_MEDIA_STORAGE_BUCKET_NAME')

AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME')
AWS_S3_SIGNATURE_VERSION = config('AWS_S3_SIGNATURE_VERSION')
AWS_S3_VERIFY = True
AWS_DEFAULT_ACL = None

##################################################################
#swagger
################################################################## 
SWAGGER_SETTINGS = {
    "USE_SESSION_AUTH": False,
    "SECURITY_DEFINITIONS": {
        "api_key": {"type": "apiKey", "in": "header", "name": "Authorization"}
    },
}

##################################################################
# Jwt Settigns for the token and token life
##################################################################
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=200),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=200),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": False,
    "UPDATE_LAST_LOGIN": False,

    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",

    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "TOKEN_USER_CLASS": "rest_framework_simplejwt.models.TokenUser",

    "JTI_CLAIM": "jti",

    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(seconds=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),

    "TOKEN_OBTAIN_SERIALIZER": "rest_framework_simplejwt.serializers.TokenObtainPairSerializer",
    "TOKEN_REFRESH_SERIALIZER": "rest_framework_simplejwt.serializers.TokenRefreshSerializer",
    "TOKEN_VERIFY_SERIALIZER": "rest_framework_simplejwt.serializers.TokenVerifySerializer",
    "TOKEN_BLACKLIST_SERIALIZER": "rest_framework_simplejwt.serializers.TokenBlacklistSerializer",
    "SLIDING_TOKEN_OBTAIN_SERIALIZER": "rest_framework_simplejwt.serializers.TokenObtainSlidingSerializer",
    "SLIDING_TOKEN_REFRESH_SERIALIZER": "rest_framework_simplejwt.serializers.TokenRefreshSlidingSerializer",
}


##################################################################
# To Dactiveate Authentication Comment bellow code
# Note : Do Not comment the bellow code in Production 
##################################################################

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    "DEFAULT_PARSER_CLASSES": [
        "rest_framework.parsers.JSONParser",
        "rest_framework.parsers.FormParser",
        "rest_framework.parsers.MultiPartParser",
    ],
    # 'EXCEPTION_HANDLER': 'utils.global_exception.custom_exception_handler',
    # "DEFAULT_PAGINATION_CLASS": "utils.pagination.CustomPagination",
    # "PAGE_SIZE": 8,
}

""" email cred """
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_HOST = 'smtp.your-email-provider.com'
# EMAIL_PORT = 587
# EMAIL_USE_TLS = True
# EMAIL_HOST_USER = '<EMAIL>'
# EMAIL_HOST_PASSWORD = 'your-email-password'


EMAIL_HOST = config('EMAIL_HOST')
EMAIL_HOST_USER = config('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD')
EMAIL_PORT = config('EMAIL_PORT')
EMAIL_USE_TLS = config('EMAIL_USE_TLS')
EMAIL_BACKEND = config('EMAIL_BACKEND')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL')


LIVE_FRONTEND_URL = config('LIVE_FRONTEND_URL')
QUALTRICS_QUESTIONNAIRE_URL = config('QUALTRICS_QUESTIONNAIRE_URL')
WEEKLY_FAMILY_PHOTO_SCORE = config('WEEKLY_FAMILY_PHOTO_SCORE')
USER_DAILY_CHECKIN_SCORE = config('USER_DAILY_CHECKIN_SCORE')
STEPS_SCREENSHOT_SCORE = config('STEPS_SCREENSHOT_SCORE')
FOOD_SCREENSHOT_SCORE = config('FOOD_SCREENSHOT_SCORE')

#############################################################
#Logger Config
#############################################################

current_date = datetime.now().strftime('%Y-%m-%d')
date_today = date.today()

boto3_logs_client = boto3.client("logs",  
                                 aws_access_key_id=config("AWS_ACCESS_KEY_ID"), 
                                 aws_secret_access_key=config("AWS_SECRET_ACCESS_KEY"),
                                 region_name=config("AWS_REGION_NAME")
                                 )


LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'aws',
            'level': 'INFO',
        },
        # 'watchtower': {
        #     'class': 'watchtower.CloudWatchLogHandler',
        #     'boto3_client': boto3_logs_client,
        #     'log_group_name': f'{config("AWS_LOG_GROUP")}',
        #     'level': 'DEBUG',
        #     'formatter': 'aws',
        #     'stream_name': f'{date_today} {config("AWS_LOG_STREAM")}',
        # }
    },
    'loggers': {
        'django': {
            'level': 'INFO',
            'handlers': ['console'],
            'propagate': False,
        },
    },
    'root': {
        'level': 'INFO',  # Set the level for the root logger
        'handlers': ['console'],
    },
    'formatters': {
        'aws': {
            'format': '{levelname} - {asctime} - {lineno} - {message}',
            'style': '{',
            'datefmt': "%Y-%m-%d %H:%M:%S"
        },
    }
}

"""-----------------------------------Twilio Cred---------------------------------"""
ACCOUNT_SID = config("ACCOUNT_SID")
AUTH_TOKEN = config("AUTH_TOKEN")
PHONE_NUMBER = config("PHONE_NUMBER")



TINYMCE_DEFAULT_CONFIG = {
    'selector': 'textarea',
    'height': 500,
    'width': '100%',
    'plugins': 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
    'toolbar': 'undo redo | formatselect | bold italic backcolor | \
                alignleft aligncenter alignright alignjustify | \
                bullist numlist outdent indent | removeformat | help',
}
