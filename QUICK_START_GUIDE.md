# HKU Score Breakdown - 快速开始指南

## 🚀 快速运行 (1分钟搞定)

### 步骤1: 安装依赖
```bash
pip install psycopg2-binary
```

### 步骤2: 运行脚本
```bash
python hku_score_breakdown_remote.py
```

就这么简单！脚本会自动连接到HKU数据库并生成完整的分数报告。

## 📊 输出示例

```
================================================================================
HKU STUDENT QUIZ - FAMILY GROUPS SCORE BREAKDOWN REPORT
================================================================================
Generated on: 2024-01-15 14:30:25
Database: hku_staging @ hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com
Total Family Groups: 25
================================================================================

📊 FAMILY: 健康家庭001 (FAM001)
   Category: Food
   Leader: user123
   Created: 2024-01-01 10:00:00
------------------------------------------------------------
✅ Daily Checkin Score: 300
   - Total Checkins: 45
   - Family Members: 4

📸 Family Photo Upload Score: 400
   - Total Uploads: 4

🎯 Quiz Total Score: 850
   - Regular Quiz Score: 720
   - Bonus Quiz Score: 130
   - Questions Answered: 68

🏃 Food/Steps Screenshot Score: 1500
🏆 Steps Leaderboard Score: 2250

----------------------------------------
📈 Official Total Score: 3050
🧮 Manual Calculated Total: 3050
============================================================
```

## 📈 功能特性

### ✅ 自动分析的分数类型
- **Daily Checkin** (每日签到) - 100分/次
- **Family Photo Upload** (家庭照片) - 100分/次  
- **Quiz Scores** (测验分数)
  - Regular Quiz (常规测验)
  - Bonus Quiz (奖励测验)
- **Food/Steps Screenshots** (食物/步数截图) - 500分/次

### 📊 报告功能
- 详细的家庭组分数breakdown
- 按分类统计 (Food/Sports)
- 分数验证 (检测计算差异)
- CSV导出功能
- 排行榜显示

## 🔧 数据库连接信息

脚本已预配置HKU数据库连接：
- **Host**: hku.chl19rrlujgq.ap-east-1.rds.amazonaws.com
- **Database**: hku_staging
- **User**: postgres
- **Port**: 5432

## 📤 导出选项

运行完成后，脚本会询问是否导出CSV文件：
```
📤 Export Options:
1. Export to CSV
2. Skip export
Choose option (1-2): 1
```

CSV文件包含所有分数详情，便于进一步分析。

## 🏆 汇总统计示例

```
📊 SUMMARY STATISTICS
================================================================================
Total Family Groups: 25
Categories: 2

🏷️  FOOD (15 families)
   Daily Checkin Total: 4500
   Family Photo Total: 1500
   Quiz Total: 12750
   Food/Steps Total: 22500
   Leaderboard Total: 41250
   🥇 Top Family: 健康美食家 (3850 points)

🏷️  SPORTS (10 families)
   Daily Checkin Total: 3200
   Family Photo Total: 1000
   Quiz Total: 8900
   Food/Steps Total: 15000
   Leaderboard Total: 28100
   🥇 Top Family: 运动达人 (3200 points)

✅ No score discrepancies found!
```

## ⚠️ 注意事项

1. **网络连接**: 需要能访问AWS RDS数据库
2. **Python版本**: 建议Python 3.7+
3. **权限**: 脚本使用只读权限，不会修改数据
4. **运行时间**: 大约需要30秒-2分钟，取决于数据量

## 🔍 故障排除

### 连接失败
```
❌ 数据库连接失败: could not connect to server
```
**解决方案**: 检查网络连接，确保能访问AWS

### 依赖缺失
```
ModuleNotFoundError: No module named 'psycopg2'
```
**解决方案**: 运行 `pip install psycopg2-binary`

### 权限错误
```
❌ 查询执行失败: permission denied
```
**解决方案**: 联系数据库管理员检查权限

## 📋 输出文件

### CSV文件字段
- family_id, family_name, category, leader
- daily_checkin_score, checkin_count, member_count  
- family_photo_score, photo_upload_count
- quiz_total_score, regular_quiz_score, bonus_quiz_score
- food_step_ss_score, steps_leaderboard_score
- total_leaderboard_score, manual_calculated_total, score_difference

### 文件命名
- CSV: `hku_score_breakdown_YYYYMMDD_HHMMSS.csv`

## 🎯 使用场景

- 📊 **定期报告**: 每周/月生成分数统计
- 🏆 **排行榜更新**: 查看最新排名
- 🔍 **数据验证**: 检查分数计算是否正确
- 📈 **趋势分析**: 导出数据进行进一步分析
- 🎮 **活动总结**: 比赛或活动结束后的总结

## 💡 小贴士

1. **最佳运行时间**: 建议在数据库负载较低时运行
2. **定期备份**: 可以定期运行并保存CSV文件作为历史记录
3. **分类对比**: 注意Food和Sports分类的分数差异
4. **异常检测**: 关注"Score Difference"不为0的家庭组

---

🎓 **HKU Student Quiz Score Breakdown Tool**  
📧 如有问题，请联系技术支持
