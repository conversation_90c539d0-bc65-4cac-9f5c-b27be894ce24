from django.contrib import admin
from .models import Category, FamilyGroups, FamilyMembers, FamilyMembersSteps, CategoryChangeReq, FamilyGroupFile ,WeeklyUploadFile


@admin.register(Category)
class AdminCategoryModel(admin.ModelAdmin):
    list_display = ['name','is_deleted']

@admin.register(FamilyGroups)
class AdminFamilyGroupsModel(admin.ModelAdmin):
    list_display = ['family_id','leader_id','family_name' ,'category' , 'is_quiz_starts' , 'created_on']

@admin.register(FamilyMembers)
class AdminFamilyMembersModel(admin.ModelAdmin):
    list_display = ['family_group','member','is_member' ,'created_on' , 'is_deleted' , 'is_leader']

@admin.register(FamilyGroupFile)
class AdminFamilyGroupFileModel(admin.ModelAdmin):
    list_display = ['family_group','file' ,'file_name']

@admin.register(FamilyMembersSteps)
class AdminFamilyMembersStepsModel(admin.ModelAdmin):
    list_display = ['member','steps','file' ,'file_name' , 'created_on']

@admin.register(CategoryChangeReq)
class AdminCategoryChangeReqModel(admin.ModelAdmin):
    list_display = ['family','reason','created_on']

@admin.register(WeeklyUploadFile)
class AdminWeeklyUploadFileModel(admin.ModelAdmin):
    list_display = ['family_group','file','file_name','score', 'created_on']







