function deleteUser(user_id) {
    var msg = "Are you sure you want to delete this item? <br>This will delete all user related data.";
    showConfirmationDialogue(msg, function (is_confirm) {
        if (is_confirm) {
            var formData = new FormData();
            formData.append('user_id', user_id);
            formData.append('csrfmiddlewaretoken', $("input[name=csrfmiddlewaretoken]").val());
            axios({
                method: "post",
                url: "/delete-user/",
                data: formData,
                headers: { "Content-Type": "multipart/form-data" },
            })
            .then(function (response) {
                if (response.data.code == 0) {
                    $('#cancelBtnClick').click();
                    showMessage('appMsg', 'danger', response.data.msg, 10);
                } else {
                    window.location.href = "/users/";
                }
            })
            .catch(function (response) {
                showMessage('appMsg', 'danger', 'Something went wrong!', 5);
            });
        }
    })
}

function searchUsers() {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    var selectedLength = $('#id_pageLength').val();
    var page_length = 10;
    if (selectedLength) {
        page_length = selectedLength;
    }
    if (selected_field && search_keyword) {
        window.location = "/users/?page=1&length=" + page_length + "&field=" + selected_field + "&search=" + search_keyword;
    } else {
        window.location = "/users/?page=1&length=" + page_length;
    }
}

$('#searchKeyword').on("keyup", function (e) {
    if (e.keyCode == 13) {
        e.preventDefault();
        searchUsers();
    }
})

function changePageLength() {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    var selectedLength = $('#id_pageLength').val();
    if (selectedLength) {
        window.location = "/users/?page=1&length=" + selectedLength + "&field=" + selected_field + "&search=" + search_keyword;
    } else {
        window.location = "/users/?page=1&length=10";
    }
}

function clearSearch () {
    var selected_field = $('#id_userField').val();
    var search_keyword = $('#searchKeyword').val();
    if (selected_field || search_keyword) {
        var selectedLength = $('#id_pageLength').val();
        var page_length = 10;
        if (selectedLength) {
            page_length = selectedLength;
        }
        window.location = "/users/?page=1&length=" + page_length;
    }
}

$('#searchKeyword').keyup(function () {
    var search_keyword = $('#searchKeyword').val();
    if (search_keyword) {
        $('#id_clearSearch').show();
    } else {
        $('#id_clearSearch').hide();
    }
})
// function importUsers() {
//     var title = "Import Users";
//     var confirm_btn_txt = "Import";
//     var processing_btn_txt = "Importing...";
//     var body = `<label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white" for="file_input">Upload file</label>
//                 <input accept=".csv" class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400" id="id_userFile" type="file">
//                 `;
//     var info_msg = `Only <b>.csv</b> file is allowed. <a class="text-blue-500" href="/download-import-users-samplefile/">Click here</a> to download sample file.`;

//     openStaticModal(body, title, confirm_btn_txt, processing_btn_txt, info_msg, function (is_confirm) {
//         if (is_confirm) {
//             var formData = new FormData();
//             var import_users_file = $('#id_userFile')[0].files[0];
//             formData.append('import_users_file', import_users_file);
//             formData.append('csrfmiddlewaretoken', $("input[name=csrfmiddlewaretoken]").val());
//             if (import_users_file && import_users_file.name && ((import_users_file.name).split('.').pop()).toLowerCase() == "csv") {
//                 $('#modalConfirmBtn').hide();
//                 $('#modalProcessingBtn').show();
//                 axios({
//                     method: "post",
//                     url: "/import-usersdata/",
//                     data: formData,
//                     headers: { "Content-Type": "multipart/form-data" },
//                 })
//                 .then(function (response) {
//                     if (response.data.code == 0) {
//                         $('#modalProcessingBtn').hide();
//                         $('#modalConfirmBtn').show();
//                         showMessage('idModalMsg', 'danger', response.data.msg, 10);
//                     } else {
//                         window.location.href = "/users/";
//                     }
//                 })
//                 .catch(function (response) {
//                     $('#modalProcessingBtn').hide();
//                     $('#modalConfirmBtn').show();
//                     showMessage('idModalMsg', 'danger', 'Something went wrong!', 10);
//                 });
//             } else {
//                 setTimeout(() => {
//                     showMessage('idModalMsg', 'info', 'Please choose correct file to import!', 10);
//                 }, 0);
//               }
//         }
//     })
// }
