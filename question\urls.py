from django.urls import path, include
from .views import (
    QuestionListView, ImportQuestionView, QuestionDetailView, member_wise_que_ans, get_usersteps, update_usersteps ,leaderboard_list, download_import_questions_samplefile,
    PrizeInfo, UpdatePrizeInfo, ScoreInfo, UpdateScoreInfo, assign_to_new_users
)

urlpatterns = [
    path('', include('question.api.urls')),
    path('question-list/', QuestionListView.as_view(), name="question_list"),
    path('import-question/', ImportQuestionView.as_view(), name="import_question"),
    path('question-detail/<int:id>/', QuestionDetailView.as_view(), name="question_detail"),
    path('member_wise_que_ans/<int:id>/', member_wise_que_ans, name="member_wise_que_ans"),
    path('get-usersteps/<int:id>/', get_usersteps, name="get_usersteps"),
    path('update-usersteps/', update_usersteps, name="update_usersteps"),
    path('leaderboard/', leaderboard_list , name="leaderboard"),
    path('download-import-questions-samplefile/', download_import_questions_samplefile, name="download_import_questions_samplefile"),
    path('prize_info/', PrizeInfo.as_view(), name="prize_info"),
    path('score_info/', ScoreInfo.as_view(), name="score_info"),
    path('update_prize_info/', UpdatePrizeInfo.as_view(), name="update_prize_info"),
    path('update_score_info/', UpdateScoreInfo.as_view(), name="update_score_info"),
    path('assign_to_new_users/', assign_to_new_users, name="assign_to_new_users"),


]
